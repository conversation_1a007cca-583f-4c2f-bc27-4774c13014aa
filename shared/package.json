{"name": "inventory-shared", "version": "1.0.0", "description": "Shared types, utilities, and constants for Inventory Management System", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit"}, "dependencies": {"moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.1"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "typescript": "^5.3.3", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["inventory", "shared", "types", "utilities", "typescript"], "author": "Your Company", "license": "MIT"}