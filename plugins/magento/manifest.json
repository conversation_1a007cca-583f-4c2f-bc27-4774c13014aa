{"id": "magento", "name": "Magento Integration", "version": "1.0.0", "description": "Sync inventory, orders, and customer data with Magento stores", "author": "Inventory Management System", "license": "Enterprise", "main": "MagentoPlugin.js", "platform": "Magento", "apiVersion": "2.4", "autoSync": true, "syncInterval": 300000, "features": ["product-sync", "inventory-sync", "order-sync", "customer-sync", "category-sync", "attribute-sync", "configurable-products", "bundle-products", "grouped-products", "multi-store-support", "multi-website-support", "advanced-pricing", "tier-pricing", "custom-attributes"], "requirements": {"magento": ">=2.3.0", "php": ">=7.4.0", "mysql": ">=5.7.0"}, "configuration": {"required": ["baseUrl", "adminToken"], "optional": ["storeCode", "websiteCode", "syncCategories", "syncAttributes", "defaultAttributeSet"]}, "endpoints": {"products": "/rest/V1/products", "categories": "/rest/V1/categories", "orders": "/rest/V1/orders", "customers": "/rest/V1/customers", "inventory": "/rest/V1/stockItems", "attributes": "/rest/V1/products/attributes", "attribute_sets": "/rest/V1/products/attribute-sets", "configurable": "/rest/V1/configurable-products"}, "webhooks": [], "rateLimit": {"requests": 120, "window": 60000}, "permissions": ["Magento_Catalog::products", "Magento_Sales::sales", "Magento_Customer::customer", "Magento_CatalogInventory::cataloginventory"]}