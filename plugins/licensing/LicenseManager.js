/**
 * License Manager - Handles subscription-based licensing for plugins
 * Manages license validation, activation, and subscription plans
 */

const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');

class LicenseManager {
    constructor() {
        this.licenses = new Map();
        this.subscriptions = new Map();
        this.licenseFile = path.join(__dirname, 'licenses.json');
        this.subscriptionFile = path.join(__dirname, 'subscriptions.json');
        this.secretKey = process.env.LICENSE_SECRET_KEY || 'default-secret-key-change-in-production';
        
        // Subscription plans
        this.plans = {
            free: {
                id: 'free',
                name: 'Free Plan',
                price: 0,
                features: ['basic-inventory', 'manual-import-export'],
                plugins: [],
                maxStores: 1,
                support: 'community'
            },
            premium: {
                id: 'premium',
                name: 'Premium Plan',
                price: 29,
                features: ['all-free', 'real-time-sync', 'webhooks', 'analytics'],
                plugins: ['woocommerce', 'shopify', 'ebay'],
                maxStores: 3,
                support: 'email'
            },
            enterprise: {
                id: 'enterprise',
                name: 'Enterprise Plan',
                price: 99,
                features: ['all-premium', 'custom-integrations', 'priority-support', 'white-label'],
                plugins: ['woocommerce', 'shopify', 'magento', 'amazon', 'ebay'],
                maxStores: -1, // unlimited
                support: 'phone'
            }
        };
    }

    /**
     * Initialize license manager
     */
    async initialize() {
        console.log('🔐 Initializing License Manager...');
        
        try {
            await this.loadLicenses();
            await this.loadSubscriptions();
            
            // Start license validation scheduler
            this.startLicenseValidator();
            
            console.log('✅ License Manager initialized');
        } catch (error) {
            console.error('❌ Failed to initialize License Manager:', error);
            throw error;
        }
    }

    /**
     * Load licenses from file
     */
    async loadLicenses() {
        try {
            const data = await fs.readFile(this.licenseFile, 'utf8');
            const licenses = JSON.parse(data);
            
            for (const license of licenses) {
                this.licenses.set(license.key, license);
            }
            
            console.log(`📄 Loaded ${licenses.length} licenses`);
        } catch (error) {
            if (error.code === 'ENOENT') {
                console.log('📄 No existing licenses file, starting fresh');
                await this.saveLicenses();
            } else {
                throw error;
            }
        }
    }

    /**
     * Load subscriptions from file
     */
    async loadSubscriptions() {
        try {
            const data = await fs.readFile(this.subscriptionFile, 'utf8');
            const subscriptions = JSON.parse(data);
            
            for (const subscription of subscriptions) {
                this.subscriptions.set(subscription.userId, subscription);
            }
            
            console.log(`📋 Loaded ${subscriptions.length} subscriptions`);
        } catch (error) {
            if (error.code === 'ENOENT') {
                console.log('📋 No existing subscriptions file, starting fresh');
                await this.saveSubscriptions();
            } else {
                throw error;
            }
        }
    }

    /**
     * Save licenses to file
     */
    async saveLicenses() {
        const licenses = Array.from(this.licenses.values());
        await fs.writeFile(this.licenseFile, JSON.stringify(licenses, null, 2));
    }

    /**
     * Save subscriptions to file
     */
    async saveSubscriptions() {
        const subscriptions = Array.from(this.subscriptions.values());
        await fs.writeFile(this.subscriptionFile, JSON.stringify(subscriptions, null, 2));
    }

    /**
     * Generate license key
     */
    generateLicenseKey(userId, planId, pluginId = null) {
        const data = {
            userId,
            planId,
            pluginId,
            timestamp: Date.now(),
            random: crypto.randomBytes(8).toString('hex')
        };
        
        const payload = Buffer.from(JSON.stringify(data)).toString('base64');
        const signature = crypto
            .createHmac('sha256', this.secretKey)
            .update(payload)
            .digest('hex');
        
        return `${payload}.${signature}`;
    }

    /**
     * Validate license key
     */
    validateLicenseKey(licenseKey) {
        try {
            const [payload, signature] = licenseKey.split('.');
            
            // Verify signature
            const expectedSignature = crypto
                .createHmac('sha256', this.secretKey)
                .update(payload)
                .digest('hex');
            
            if (signature !== expectedSignature) {
                return { valid: false, error: 'Invalid signature' };
            }
            
            // Decode payload
            const data = JSON.parse(Buffer.from(payload, 'base64').toString());
            
            return { valid: true, data };
        } catch (error) {
            return { valid: false, error: 'Invalid license format' };
        }
    }

    /**
     * Activate license
     */
    async activateLicense(licenseKey, userId, deviceId) {
        const validation = this.validateLicenseKey(licenseKey);
        
        if (!validation.valid) {
            throw new Error(`Invalid license: ${validation.error}`);
        }
        
        const { data } = validation;
        
        // Check if license already exists
        if (this.licenses.has(licenseKey)) {
            const existingLicense = this.licenses.get(licenseKey);
            if (existingLicense.status === 'active') {
                throw new Error('License already activated');
            }
        }
        
        // Check subscription
        const subscription = this.subscriptions.get(userId);
        if (!subscription || subscription.status !== 'active') {
            throw new Error('No active subscription found');
        }
        
        // Create license record
        const license = {
            key: licenseKey,
            userId,
            deviceId,
            planId: data.planId,
            pluginId: data.pluginId,
            status: 'active',
            activatedAt: new Date().toISOString(),
            expiresAt: subscription.expiresAt,
            features: this.plans[data.planId].features,
            maxStores: this.plans[data.planId].maxStores
        };
        
        this.licenses.set(licenseKey, license);
        await this.saveLicenses();
        
        console.log(`✅ License activated for user ${userId}, plan ${data.planId}`);
        return license;
    }

    /**
     * Validate license for plugin
     */
    async validateLicense(pluginId, userId = null) {
        // Find license for plugin
        for (const [key, license] of this.licenses) {
            if (license.status !== 'active') continue;
            
            // Check if license covers this plugin
            const plan = this.plans[license.planId];
            if (!plan.plugins.includes(pluginId)) continue;
            
            // Check expiration
            if (new Date(license.expiresAt) < new Date()) {
                license.status = 'expired';
                await this.saveLicenses();
                continue;
            }
            
            // Check user if specified
            if (userId && license.userId !== userId) continue;
            
            return true;
        }
        
        return false;
    }

    /**
     * Check if user has valid license
     */
    hasValidLicense(pluginId, userId = null) {
        for (const [key, license] of this.licenses) {
            if (license.status !== 'active') continue;
            
            const plan = this.plans[license.planId];
            if (!plan.plugins.includes(pluginId)) continue;
            
            if (new Date(license.expiresAt) < new Date()) continue;
            
            if (userId && license.userId !== userId) continue;
            
            return true;
        }
        
        return false;
    }

    /**
     * Create subscription
     */
    async createSubscription(userId, planId, paymentMethod = 'stripe') {
        const plan = this.plans[planId];
        if (!plan) {
            throw new Error(`Invalid plan: ${planId}`);
        }
        
        const subscription = {
            id: crypto.randomUUID(),
            userId,
            planId,
            status: 'active',
            paymentMethod,
            createdAt: new Date().toISOString(),
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
            autoRenew: true,
            price: plan.price
        };
        
        this.subscriptions.set(userId, subscription);
        await this.saveSubscriptions();
        
        // Generate licenses for all plugins in plan
        const licenses = [];
        for (const pluginId of plan.plugins) {
            const licenseKey = this.generateLicenseKey(userId, planId, pluginId);
            licenses.push(licenseKey);
        }
        
        console.log(`✅ Created ${planId} subscription for user ${userId}`);
        return { subscription, licenses };
    }

    /**
     * Get user subscription
     */
    getUserSubscription(userId) {
        return this.subscriptions.get(userId);
    }

    /**
     * Get user licenses
     */
    getUserLicenses(userId) {
        const userLicenses = [];
        
        for (const [key, license] of this.licenses) {
            if (license.userId === userId) {
                userLicenses.push(license);
            }
        }
        
        return userLicenses;
    }

    /**
     * Start license validation scheduler
     */
    startLicenseValidator() {
        // Check licenses every hour
        setInterval(async () => {
            await this.validateAllLicenses();
        }, 60 * 60 * 1000);
        
        console.log('⏰ License validator scheduler started');
    }

    /**
     * Validate all licenses
     */
    async validateAllLicenses() {
        let expiredCount = 0;
        
        for (const [key, license] of this.licenses) {
            if (license.status === 'active' && new Date(license.expiresAt) < new Date()) {
                license.status = 'expired';
                expiredCount++;
            }
        }
        
        if (expiredCount > 0) {
            await this.saveLicenses();
            console.log(`⚠️ Expired ${expiredCount} licenses`);
        }
    }

    /**
     * Get available plans
     */
    getPlans() {
        return Object.values(this.plans);
    }

    /**
     * Get license statistics
     */
    getLicenseStats() {
        const stats = {
            total: this.licenses.size,
            active: 0,
            expired: 0,
            byPlan: {}
        };
        
        for (const [key, license] of this.licenses) {
            if (license.status === 'active') {
                stats.active++;
            } else if (license.status === 'expired') {
                stats.expired++;
            }
            
            if (!stats.byPlan[license.planId]) {
                stats.byPlan[license.planId] = 0;
            }
            stats.byPlan[license.planId]++;
        }
        
        return stats;
    }
}

module.exports = LicenseManager;
