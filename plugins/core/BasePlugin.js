/**
 * Base Plugin Class - Abstract base class for all e-commerce plugins
 * Provides common functionality and interface for platform integrations
 */

const EventEmitter = require('events');
const crypto = require('crypto');

class BasePlugin extends EventEmitter {
    constructor(manifest) {
        super();
        this.manifest = manifest;
        this.config = {};
        this.isInitialized = false;
        this.lastSync = null;
        this.syncStats = {
            products: { synced: 0, errors: 0 },
            orders: { synced: 0, errors: 0 },
            inventory: { synced: 0, errors: 0 },
            customers: { synced: 0, errors: 0 }
        };
        this.rateLimiter = new Map();
    }

    /**
     * Initialize the plugin - must be implemented by subclasses
     */
    async initialize() {
        throw new Error('initialize() method must be implemented by subclass');
    }

    /**
     * Cleanup plugin resources
     */
    async cleanup() {
        this.removeAllListeners();
        this.isInitialized = false;
        console.log(`🧹 Cleaned up plugin: ${this.manifest.name}`);
    }

    /**
     * Main sync method - orchestrates all sync operations
     */
    async sync() {
        if (!this.isInitialized) {
            throw new Error('Plugin not initialized');
        }

        console.log(`🔄 Starting sync for ${this.manifest.name}...`);
        const startTime = Date.now();

        try {
            // Sync in order: Products -> Inventory -> Orders -> Customers
            await this.syncProducts();
            await this.syncInventory();
            await this.syncOrders();
            await this.syncCustomers();

            this.lastSync = new Date().toISOString();
            const duration = Date.now() - startTime;
            
            console.log(`✅ Sync completed for ${this.manifest.name} in ${duration}ms`);
            this.emit('sync:complete', { duration, stats: this.syncStats });
            
        } catch (error) {
            console.error(`❌ Sync failed for ${this.manifest.name}:`, error);
            this.emit('sync:error', error);
            throw error;
        }
    }

    /**
     * Sync products - must be implemented by subclasses
     */
    async syncProducts() {
        throw new Error('syncProducts() method must be implemented by subclass');
    }

    /**
     * Sync inventory - must be implemented by subclasses
     */
    async syncInventory() {
        throw new Error('syncInventory() method must be implemented by subclass');
    }

    /**
     * Sync orders - must be implemented by subclasses
     */
    async syncOrders() {
        throw new Error('syncOrders() method must be implemented by subclass');
    }

    /**
     * Sync customers - must be implemented by subclasses
     */
    async syncCustomers() {
        throw new Error('syncCustomers() method must be implemented by subclass');
    }

    /**
     * Rate limiting helper
     */
    async rateLimit(key, maxRequests = 100, windowMs = 60000) {
        const now = Date.now();
        const windowStart = now - windowMs;
        
        if (!this.rateLimiter.has(key)) {
            this.rateLimiter.set(key, []);
        }
        
        const requests = this.rateLimiter.get(key);
        
        // Remove old requests outside the window
        const validRequests = requests.filter(time => time > windowStart);
        
        if (validRequests.length >= maxRequests) {
            const oldestRequest = Math.min(...validRequests);
            const waitTime = oldestRequest + windowMs - now;
            
            if (waitTime > 0) {
                console.log(`⏳ Rate limit reached for ${key}, waiting ${waitTime}ms`);
                await this.sleep(waitTime);
            }
        }
        
        validRequests.push(now);
        this.rateLimiter.set(key, validRequests);
    }

    /**
     * HTTP request helper with retry logic
     */
    async makeRequest(url, options = {}, retries = 3) {
        const fetch = require('node-fetch');
        
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                const response = await fetch(url, {
                    timeout: 30000,
                    ...options,
                    headers: {
                        'User-Agent': `InventoryMS-Plugin/${this.manifest.version}`,
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                console.error(`❌ Request failed (attempt ${attempt}/${retries}):`, error.message);
                
                if (attempt === retries) {
                    throw error;
                }
                
                // Exponential backoff
                const delay = Math.pow(2, attempt) * 1000;
                await this.sleep(delay);
            }
        }
    }

    /**
     * Validate configuration
     */
    validateConfig(requiredFields) {
        const missing = requiredFields.filter(field => !this.config[field]);
        
        if (missing.length > 0) {
            throw new Error(`Missing required configuration: ${missing.join(', ')}`);
        }
    }

    /**
     * Encrypt sensitive data
     */
    encrypt(text, key) {
        const algorithm = 'aes-256-gcm';
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipher(algorithm, key);
        
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        
        const authTag = cipher.getAuthTag();
        
        return {
            encrypted,
            iv: iv.toString('hex'),
            authTag: authTag.toString('hex')
        };
    }

    /**
     * Decrypt sensitive data
     */
    decrypt(encryptedData, key) {
        const algorithm = 'aes-256-gcm';
        const decipher = crypto.createDecipher(algorithm, key);
        
        decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
        
        let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        
        return decrypted;
    }

    /**
     * Sleep utility
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Log with plugin context
     */
    log(level, message, data = {}) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            plugin: this.manifest.name,
            level,
            message,
            data
        };
        
        console.log(`[${timestamp}] [${this.manifest.name}] ${level.toUpperCase()}: ${message}`);
        
        if (Object.keys(data).length > 0) {
            console.log('Data:', data);
        }
        
        this.emit('log', logEntry);
    }

    /**
     * Update sync statistics
     */
    updateSyncStats(type, success = true) {
        if (this.syncStats[type]) {
            if (success) {
                this.syncStats[type].synced++;
            } else {
                this.syncStats[type].errors++;
            }
        }
    }

    /**
     * Get plugin health status
     */
    getHealthStatus() {
        return {
            plugin: this.manifest.name,
            version: this.manifest.version,
            initialized: this.isInitialized,
            lastSync: this.lastSync,
            stats: this.syncStats,
            uptime: this.isInitialized ? Date.now() - this.initTime : 0
        };
    }

    /**
     * Test connection to platform
     */
    async testConnection() {
        throw new Error('testConnection() method must be implemented by subclass');
    }
}

module.exports = BasePlugin;
