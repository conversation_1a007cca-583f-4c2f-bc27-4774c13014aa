/**
 * Plugin Manager - Core system for managing e-commerce integrations
 * Handles plugin loading, licensing, and lifecycle management
 */

const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');

class PluginManager {
    constructor() {
        this.plugins = new Map();
        this.activePlugins = new Set();
        this.licenseManager = null;
        this.config = {
            pluginsDir: path.join(__dirname, '..'),
            licenseFile: path.join(__dirname, '../licensing/licenses.json'),
            maxRetries: 3,
            syncInterval: 300000 // 5 minutes
        };
    }

    /**
     * Initialize the plugin manager
     */
    async initialize() {
        console.log('🔌 Initializing Plugin Manager...');
        
        try {
            // Load license manager
            const LicenseManager = require('../licensing/LicenseManager');
            this.licenseManager = new LicenseManager();
            await this.licenseManager.initialize();

            // Discover and load plugins
            await this.discoverPlugins();
            
            // Auto-activate licensed plugins
            await this.autoActivatePlugins();
            
            console.log('✅ Plugin Manager initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize Plugin Manager:', error);
            throw error;
        }
    }

    /**
     * Discover available plugins
     */
    async discoverPlugins() {
        const pluginDirs = ['woocommerce', 'shopify', 'magento', 'amazon', 'ebay'];
        
        for (const dir of pluginDirs) {
            try {
                const pluginPath = path.join(this.config.pluginsDir, dir);
                const manifestPath = path.join(pluginPath, 'manifest.json');
                
                // Check if plugin exists
                const manifestExists = await this.fileExists(manifestPath);
                if (!manifestExists) continue;

                // Load plugin manifest
                const manifestData = await fs.readFile(manifestPath, 'utf8');
                const manifest = JSON.parse(manifestData);
                
                // Load plugin class
                const PluginClass = require(path.join(pluginPath, manifest.main));
                const plugin = new PluginClass(manifest);
                
                this.plugins.set(manifest.id, {
                    instance: plugin,
                    manifest: manifest,
                    path: pluginPath,
                    status: 'discovered'
                });
                
                console.log(`📦 Discovered plugin: ${manifest.name} v${manifest.version}`);
            } catch (error) {
                console.error(`❌ Failed to discover plugin ${dir}:`, error);
            }
        }
    }

    /**
     * Auto-activate plugins with valid licenses
     */
    async autoActivatePlugins() {
        for (const [pluginId, pluginData] of this.plugins) {
            try {
                const hasValidLicense = await this.licenseManager.validateLicense(pluginId);
                
                if (hasValidLicense) {
                    await this.activatePlugin(pluginId);
                } else {
                    console.log(`⚠️ Plugin ${pluginId} requires valid license`);
                }
            } catch (error) {
                console.error(`❌ Failed to auto-activate plugin ${pluginId}:`, error);
            }
        }
    }

    /**
     * Activate a plugin
     */
    async activatePlugin(pluginId) {
        const pluginData = this.plugins.get(pluginId);
        if (!pluginData) {
            throw new Error(`Plugin ${pluginId} not found`);
        }

        // Validate license
        const hasValidLicense = await this.licenseManager.validateLicense(pluginId);
        if (!hasValidLicense) {
            throw new Error(`Invalid or expired license for plugin ${pluginId}`);
        }

        try {
            // Initialize plugin
            await pluginData.instance.initialize();
            
            // Mark as active
            this.activePlugins.add(pluginId);
            pluginData.status = 'active';
            
            console.log(`✅ Activated plugin: ${pluginData.manifest.name}`);
            
            // Start sync if auto-sync is enabled
            if (pluginData.manifest.autoSync) {
                this.startPluginSync(pluginId);
            }
            
            return true;
        } catch (error) {
            console.error(`❌ Failed to activate plugin ${pluginId}:`, error);
            throw error;
        }
    }

    /**
     * Deactivate a plugin
     */
    async deactivatePlugin(pluginId) {
        const pluginData = this.plugins.get(pluginId);
        if (!pluginData) {
            throw new Error(`Plugin ${pluginId} not found`);
        }

        try {
            // Stop sync
            this.stopPluginSync(pluginId);
            
            // Cleanup plugin
            if (pluginData.instance.cleanup) {
                await pluginData.instance.cleanup();
            }
            
            // Mark as inactive
            this.activePlugins.delete(pluginId);
            pluginData.status = 'inactive';
            
            console.log(`🔌 Deactivated plugin: ${pluginData.manifest.name}`);
            return true;
        } catch (error) {
            console.error(`❌ Failed to deactivate plugin ${pluginId}:`, error);
            throw error;
        }
    }

    /**
     * Start plugin synchronization
     */
    startPluginSync(pluginId) {
        const pluginData = this.plugins.get(pluginId);
        if (!pluginData || !this.activePlugins.has(pluginId)) return;

        const syncInterval = setInterval(async () => {
            try {
                await pluginData.instance.sync();
            } catch (error) {
                console.error(`❌ Sync failed for plugin ${pluginId}:`, error);
            }
        }, this.config.syncInterval);

        pluginData.syncInterval = syncInterval;
        console.log(`🔄 Started sync for plugin: ${pluginData.manifest.name}`);
    }

    /**
     * Stop plugin synchronization
     */
    stopPluginSync(pluginId) {
        const pluginData = this.plugins.get(pluginId);
        if (pluginData && pluginData.syncInterval) {
            clearInterval(pluginData.syncInterval);
            delete pluginData.syncInterval;
            console.log(`⏹️ Stopped sync for plugin: ${pluginData.manifest.name}`);
        }
    }

    /**
     * Get plugin status
     */
    getPluginStatus(pluginId) {
        const pluginData = this.plugins.get(pluginId);
        if (!pluginData) return null;

        return {
            id: pluginId,
            name: pluginData.manifest.name,
            version: pluginData.manifest.version,
            status: pluginData.status,
            active: this.activePlugins.has(pluginId),
            licensed: this.licenseManager.hasValidLicense(pluginId),
            lastSync: pluginData.instance.lastSync || null
        };
    }

    /**
     * Get all plugins status
     */
    getAllPluginsStatus() {
        const status = [];
        for (const [pluginId] of this.plugins) {
            status.push(this.getPluginStatus(pluginId));
        }
        return status;
    }

    /**
     * Execute plugin method
     */
    async executePluginMethod(pluginId, method, ...args) {
        const pluginData = this.plugins.get(pluginId);
        if (!pluginData) {
            throw new Error(`Plugin ${pluginId} not found`);
        }

        if (!this.activePlugins.has(pluginId)) {
            throw new Error(`Plugin ${pluginId} is not active`);
        }

        if (typeof pluginData.instance[method] !== 'function') {
            throw new Error(`Method ${method} not found in plugin ${pluginId}`);
        }

        return await pluginData.instance[method](...args);
    }

    /**
     * Utility: Check if file exists
     */
    async fileExists(filePath) {
        try {
            await fs.access(filePath);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Cleanup and shutdown
     */
    async shutdown() {
        console.log('🔌 Shutting down Plugin Manager...');
        
        // Deactivate all plugins
        for (const pluginId of this.activePlugins) {
            await this.deactivatePlugin(pluginId);
        }
        
        console.log('✅ Plugin Manager shutdown complete');
    }
}

module.exports = PluginManager;
