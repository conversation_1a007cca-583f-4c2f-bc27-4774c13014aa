/**
 * Shopify Plugin - Integration with Shopify stores
 * Handles product, order, inventory, and customer synchronization
 */

const BasePlugin = require('../core/BasePlugin');

class ShopifyPlugin extends BasePlugin {
    constructor(manifest) {
        super(manifest);
        this.apiUrl = '';
        this.accessToken = '';
        this.locations = new Map();
        this.rateLimitInfo = {
            remaining: 40,
            max: 40,
            resetTime: Date.now()
        };
    }

    /**
     * Initialize Shopify plugin
     */
    async initialize() {
        this.log('info', 'Initializing Shopify plugin...');
        
        try {
            // Validate configuration
            this.validateConfig(['shopDomain', 'accessToken']);
            
            // Setup API URL and authentication
            this.apiUrl = `https://${this.config.shopDomain}/admin/api/2023-10`;
            this.accessToken = this.config.accessToken;
            
            // Test connection
            await this.testConnection();
            
            // Load locations
            await this.loadLocations();
            
            // Setup webhooks if configured
            if (this.config.webhookSecret) {
                await this.setupWebhooks();
            }
            
            this.isInitialized = true;
            this.initTime = Date.now();
            
            this.log('info', 'Shopify plugin initialized successfully');
        } catch (error) {
            this.log('error', 'Failed to initialize Shopify plugin', { error: error.message });
            throw error;
        }
    }

    /**
     * Test connection to Shopify store
     */
    async testConnection() {
        try {
            const response = await this.makeShopifyRequest('/shop.json');
            this.log('info', 'Connection test successful', { 
                shop: response.shop.name,
                domain: response.shop.domain,
                status: 'connected'
            });
            return true;
        } catch (error) {
            this.log('error', 'Connection test failed', { error: error.message });
            throw new Error(`Shopify connection failed: ${error.message}`);
        }
    }

    /**
     * Load store locations
     */
    async loadLocations() {
        try {
            const response = await this.makeShopifyRequest('/locations.json');
            
            for (const location of response.locations) {
                this.locations.set(location.id, location);
            }
            
            this.log('info', `Loaded ${response.locations.length} locations`);
        } catch (error) {
            this.log('error', 'Failed to load locations', { error: error.message });
        }
    }

    /**
     * Sync products from Shopify to local inventory
     */
    async syncProducts() {
        this.log('info', 'Starting product sync...');
        
        try {
            let sinceId = 0;
            let hasMore = true;
            let totalSynced = 0;
            
            while (hasMore) {
                // Shopify rate limiting
                await this.handleRateLimit();
                
                const response = await this.makeShopifyRequest('/products.json', {
                    limit: 250,
                    since_id: sinceId,
                    fields: 'id,title,handle,product_type,vendor,tags,status,created_at,updated_at,variants,images,options'
                });
                
                if (response.products.length === 0) {
                    hasMore = false;
                    break;
                }
                
                // Process each product
                for (const shopifyProduct of response.products) {
                    try {
                        await this.processProduct(shopifyProduct);
                        this.updateSyncStats('products', true);
                        totalSynced++;
                        sinceId = Math.max(sinceId, shopifyProduct.id);
                    } catch (error) {
                        this.log('error', `Failed to sync product ${shopifyProduct.id}`, { error: error.message });
                        this.updateSyncStats('products', false);
                    }
                }
                
                // Update since_id for next iteration
                if (response.products.length > 0) {
                    sinceId = response.products[response.products.length - 1].id;
                }
                
                // Prevent infinite loops
                if (response.products.length < 250) {
                    hasMore = false;
                }
            }
            
            this.log('info', `Product sync completed: ${totalSynced} products synced`);
        } catch (error) {
            this.log('error', 'Product sync failed', { error: error.message });
            throw error;
        }
    }

    /**
     * Process individual product
     */
    async processProduct(shopifyProduct) {
        const product = {
            externalId: shopifyProduct.id.toString(),
            platform: 'shopify',
            name: shopifyProduct.title,
            handle: shopifyProduct.handle,
            description: shopifyProduct.body_html || '',
            productType: shopifyProduct.product_type || '',
            vendor: shopifyProduct.vendor || '',
            tags: shopifyProduct.tags ? shopifyProduct.tags.split(',').map(tag => tag.trim()) : [],
            status: shopifyProduct.status === 'active' ? 'active' : 'inactive',
            images: shopifyProduct.images ? shopifyProduct.images.map(img => ({
                id: img.id,
                src: img.src,
                alt: img.alt || '',
                position: img.position
            })) : [],
            options: shopifyProduct.options || [],
            variants: [],
            seo: {
                title: shopifyProduct.seo_title || '',
                description: shopifyProduct.seo_description || ''
            },
            createdAt: shopifyProduct.created_at,
            updatedAt: shopifyProduct.updated_at
        };
        
        // Process variants
        if (shopifyProduct.variants) {
            for (const variant of shopifyProduct.variants) {
                product.variants.push({
                    id: variant.id,
                    sku: variant.sku || '',
                    barcode: variant.barcode || '',
                    price: parseFloat(variant.price) || 0,
                    compareAtPrice: parseFloat(variant.compare_at_price) || 0,
                    weight: parseFloat(variant.weight) || 0,
                    weightUnit: variant.weight_unit || 'kg',
                    inventoryQuantity: variant.inventory_quantity || 0,
                    inventoryPolicy: variant.inventory_policy || 'deny',
                    inventoryManagement: variant.inventory_management || null,
                    option1: variant.option1,
                    option2: variant.option2,
                    option3: variant.option3,
                    position: variant.position,
                    createdAt: variant.created_at,
                    updatedAt: variant.updated_at
                });
            }
        }
        
        // Send to inventory system
        await this.sendToInventorySystem('products', 'upsert', product);
        
        this.log('debug', `Processed product: ${product.name} with ${product.variants.length} variants`);
    }

    /**
     * Sync inventory levels
     */
    async syncInventory() {
        this.log('info', 'Starting inventory sync...');
        
        try {
            // Get inventory updates from local system
            const inventoryUpdates = await this.getInventoryUpdates();
            
            for (const update of inventoryUpdates) {
                try {
                    await this.handleRateLimit();
                    
                    // Update inventory level for each location
                    for (const [locationId, location] of this.locations) {
                        await this.makeShopifyRequest('/inventory_levels/set.json', {
                            location_id: locationId,
                            inventory_item_id: update.inventoryItemId,
                            available: update.quantity
                        }, 'POST');
                    }
                    
                    this.updateSyncStats('inventory', true);
                    this.log('debug', `Updated inventory for item ${update.inventoryItemId}: ${update.quantity}`);
                } catch (error) {
                    this.log('error', `Failed to update inventory for item ${update.inventoryItemId}`, { error: error.message });
                    this.updateSyncStats('inventory', false);
                }
            }
            
            this.log('info', `Inventory sync completed: ${inventoryUpdates.length} items updated`);
        } catch (error) {
            this.log('error', 'Inventory sync failed', { error: error.message });
            throw error;
        }
    }

    /**
     * Sync orders from Shopify
     */
    async syncOrders() {
        this.log('info', 'Starting order sync...');
        
        try {
            // Get orders from last sync or last 24 hours
            const since = this.lastSync || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
            
            let sinceId = 0;
            let hasMore = true;
            let totalSynced = 0;
            
            while (hasMore) {
                await this.handleRateLimit();
                
                const response = await this.makeShopifyRequest('/orders.json', {
                    limit: 250,
                    since_id: sinceId,
                    updated_at_min: since,
                    status: 'any',
                    fields: 'id,order_number,email,created_at,updated_at,cancelled_at,closed_at,processed_at,currency,total_price,subtotal_price,total_tax,total_discounts,financial_status,fulfillment_status,customer,billing_address,shipping_address,line_items,shipping_lines,tax_lines,payment_gateway_names,note'
                });
                
                if (response.orders.length === 0) {
                    hasMore = false;
                    break;
                }
                
                for (const shopifyOrder of response.orders) {
                    try {
                        await this.processOrder(shopifyOrder);
                        this.updateSyncStats('orders', true);
                        totalSynced++;
                        sinceId = Math.max(sinceId, shopifyOrder.id);
                    } catch (error) {
                        this.log('error', `Failed to sync order ${shopifyOrder.id}`, { error: error.message });
                        this.updateSyncStats('orders', false);
                    }
                }
                
                if (response.orders.length < 250) {
                    hasMore = false;
                }
            }
            
            this.log('info', `Order sync completed: ${totalSynced} orders synced`);
        } catch (error) {
            this.log('error', 'Order sync failed', { error: error.message });
            throw error;
        }
    }

    /**
     * Process individual order
     */
    async processOrder(shopifyOrder) {
        const order = {
            externalId: shopifyOrder.id.toString(),
            platform: 'shopify',
            orderNumber: shopifyOrder.order_number || shopifyOrder.name,
            email: shopifyOrder.email,
            currency: shopifyOrder.currency,
            totalPrice: parseFloat(shopifyOrder.total_price) || 0,
            subtotalPrice: parseFloat(shopifyOrder.subtotal_price) || 0,
            totalTax: parseFloat(shopifyOrder.total_tax) || 0,
            totalDiscounts: parseFloat(shopifyOrder.total_discounts) || 0,
            financialStatus: shopifyOrder.financial_status,
            fulfillmentStatus: shopifyOrder.fulfillment_status,
            customer: shopifyOrder.customer ? {
                id: shopifyOrder.customer.id,
                email: shopifyOrder.customer.email,
                firstName: shopifyOrder.customer.first_name,
                lastName: shopifyOrder.customer.last_name,
                phone: shopifyOrder.customer.phone
            } : null,
            billingAddress: shopifyOrder.billing_address,
            shippingAddress: shopifyOrder.shipping_address,
            lineItems: shopifyOrder.line_items ? shopifyOrder.line_items.map(item => ({
                id: item.id,
                productId: item.product_id,
                variantId: item.variant_id,
                title: item.title,
                name: item.name,
                sku: item.sku,
                quantity: item.quantity,
                price: parseFloat(item.price),
                totalDiscount: parseFloat(item.total_discount) || 0,
                fulfillmentStatus: item.fulfillment_status
            })) : [],
            shippingLines: shopifyOrder.shipping_lines || [],
            taxLines: shopifyOrder.tax_lines || [],
            paymentGateways: shopifyOrder.payment_gateway_names || [],
            note: shopifyOrder.note || '',
            createdAt: shopifyOrder.created_at,
            updatedAt: shopifyOrder.updated_at,
            cancelledAt: shopifyOrder.cancelled_at,
            closedAt: shopifyOrder.closed_at,
            processedAt: shopifyOrder.processed_at
        };
        
        // Send to inventory system
        await this.sendToInventorySystem('orders', 'upsert', order);
        
        this.log('debug', `Processed order: ${order.orderNumber} (${order.financialStatus})`);
    }

    /**
     * Sync customers from Shopify
     */
    async syncCustomers() {
        this.log('info', 'Starting customer sync...');
        
        try {
            const since = this.lastSync || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
            
            let sinceId = 0;
            let hasMore = true;
            let totalSynced = 0;
            
            while (hasMore) {
                await this.handleRateLimit();
                
                const response = await this.makeShopifyRequest('/customers.json', {
                    limit: 250,
                    since_id: sinceId,
                    updated_at_min: since,
                    fields: 'id,email,first_name,last_name,phone,verified_email,accepts_marketing,created_at,updated_at,orders_count,total_spent,last_order_id,last_order_name,default_address,addresses,tags'
                });
                
                if (response.customers.length === 0) {
                    hasMore = false;
                    break;
                }
                
                for (const shopifyCustomer of response.customers) {
                    try {
                        await this.processCustomer(shopifyCustomer);
                        this.updateSyncStats('customers', true);
                        totalSynced++;
                        sinceId = Math.max(sinceId, shopifyCustomer.id);
                    } catch (error) {
                        this.log('error', `Failed to sync customer ${shopifyCustomer.id}`, { error: error.message });
                        this.updateSyncStats('customers', false);
                    }
                }
                
                if (response.customers.length < 250) {
                    hasMore = false;
                }
            }
            
            this.log('info', `Customer sync completed: ${totalSynced} customers synced`);
        } catch (error) {
            this.log('error', 'Customer sync failed', { error: error.message });
            throw error;
        }
    }

    /**
     * Process individual customer
     */
    async processCustomer(shopifyCustomer) {
        const customer = {
            externalId: shopifyCustomer.id.toString(),
            platform: 'shopify',
            email: shopifyCustomer.email,
            firstName: shopifyCustomer.first_name || '',
            lastName: shopifyCustomer.last_name || '',
            phone: shopifyCustomer.phone || '',
            verifiedEmail: shopifyCustomer.verified_email || false,
            acceptsMarketing: shopifyCustomer.accepts_marketing || false,
            ordersCount: shopifyCustomer.orders_count || 0,
            totalSpent: parseFloat(shopifyCustomer.total_spent) || 0,
            lastOrderId: shopifyCustomer.last_order_id,
            lastOrderName: shopifyCustomer.last_order_name,
            defaultAddress: shopifyCustomer.default_address,
            addresses: shopifyCustomer.addresses || [],
            tags: shopifyCustomer.tags ? shopifyCustomer.tags.split(',').map(tag => tag.trim()) : [],
            createdAt: shopifyCustomer.created_at,
            updatedAt: shopifyCustomer.updated_at
        };
        
        // Send to inventory system
        await this.sendToInventorySystem('customers', 'upsert', customer);
        
        this.log('debug', `Processed customer: ${customer.email}`);
    }

    /**
     * Handle Shopify rate limiting
     */
    async handleRateLimit() {
        // Shopify uses a leaky bucket algorithm
        // 40 requests per second, with burst capacity of 80
        
        if (this.rateLimitInfo.remaining <= 5) {
            const waitTime = Math.max(1000, this.rateLimitInfo.resetTime - Date.now());
            this.log('info', `Rate limit approaching, waiting ${waitTime}ms`);
            await this.sleep(waitTime);
        }
    }

    /**
     * Make authenticated request to Shopify API
     */
    async makeShopifyRequest(endpoint, data = null, method = 'GET') {
        const url = this.apiUrl + endpoint;
        
        const options = {
            method,
            headers: {
                'X-Shopify-Access-Token': this.accessToken,
                'Content-Type': 'application/json',
                'User-Agent': `InventoryMS-Shopify/${this.manifest.version}`
            }
        };
        
        // Add query parameters for GET requests
        if (method === 'GET' && data) {
            const urlObj = new URL(url);
            Object.keys(data).forEach(key => {
                urlObj.searchParams.append(key, data[key]);
            });
            return await this.makeRequest(urlObj.toString(), options);
        }
        
        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }
        
        const response = await this.makeRequest(url, options);
        
        // Update rate limit info from headers
        if (response.headers) {
            this.rateLimitInfo.remaining = parseInt(response.headers['x-shopify-shop-api-call-limit']?.split('/')[0] || '40');
            this.rateLimitInfo.max = parseInt(response.headers['x-shopify-shop-api-call-limit']?.split('/')[1] || '40');
        }
        
        return response;
    }

    /**
     * Setup webhooks for real-time updates
     */
    async setupWebhooks() {
        this.log('info', 'Setting up webhooks...');
        
        const webhooks = [
            { topic: 'products/create', event: 'product.created' },
            { topic: 'products/update', event: 'product.updated' },
            { topic: 'orders/create', event: 'order.created' },
            { topic: 'orders/updated', event: 'order.updated' },
            { topic: 'inventory_levels/update', event: 'inventory.updated' }
        ];
        
        for (const webhook of webhooks) {
            try {
                const response = await this.makeShopifyRequest('/webhooks.json', {
                    webhook: {
                        topic: webhook.topic,
                        address: `${this.config.webhookUrl}/${webhook.event}`,
                        format: 'json'
                    }
                }, 'POST');
                
                this.log('info', `Webhook created: ${webhook.topic}`);
            } catch (error) {
                this.log('error', `Failed to create webhook ${webhook.topic}`, { error: error.message });
            }
        }
    }

    /**
     * Get inventory updates from local system
     */
    async getInventoryUpdates() {
        // This would typically call your inventory system API
        return [];
    }

    /**
     * Send data to inventory system
     */
    async sendToInventorySystem(type, action, data) {
        // This would typically send data to your inventory system API
        this.log('debug', `Sending ${type} data to inventory system`, { action, id: data.externalId });
    }
}

module.exports = ShopifyPlugin;
