{"id": "shopify", "name": "Shopify Integration", "version": "1.0.0", "description": "Sync inventory, orders, and customer data with Shopify stores", "author": "Inventory Management System", "license": "Premium", "main": "ShopifyPlugin.js", "platform": "Shopify", "apiVersion": "2023-10", "autoSync": true, "syncInterval": 300000, "features": ["product-sync", "inventory-sync", "order-sync", "customer-sync", "webhook-support", "real-time-updates", "bulk-operations", "fulfillment-tracking", "variant-support", "metafields-sync"], "requirements": {"shopify": ">=2020-01", "api_version": ">=2023-10"}, "configuration": {"required": ["shopDomain", "accessToken"], "optional": ["webhookSecret", "syncVariants", "syncMetafields", "defaultLocation"]}, "endpoints": {"products": "/admin/api/2023-10/products.json", "variants": "/admin/api/2023-10/products/{product_id}/variants.json", "inventory": "/admin/api/2023-10/inventory_levels.json", "orders": "/admin/api/2023-10/orders.json", "customers": "/admin/api/2023-10/customers.json", "webhooks": "/admin/api/2023-10/webhooks.json", "locations": "/admin/api/2023-10/locations.json"}, "webhooks": ["products/create", "products/update", "products/delete", "orders/create", "orders/updated", "orders/paid", "orders/cancelled", "orders/fulfilled", "customers/create", "customers/update", "inventory_levels/update"], "rateLimit": {"requests": 40, "window": 1000, "burst": 80}, "permissions": ["read_products", "write_products", "read_orders", "write_orders", "read_customers", "write_customers", "read_inventory", "write_inventory"]}