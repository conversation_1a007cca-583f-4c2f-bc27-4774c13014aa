{"id": "woocommerce", "name": "WooCommerce Integration", "version": "1.0.0", "description": "Sync inventory, orders, and customer data with WooCommerce stores", "author": "Inventory Management System", "license": "Premium", "main": "WooCommercePlugin.js", "platform": "WooCommerce", "apiVersion": "v3", "autoSync": true, "syncInterval": 300000, "features": ["product-sync", "inventory-sync", "order-sync", "customer-sync", "webhook-support", "real-time-updates", "bulk-operations", "rto-tracking"], "requirements": {"woocommerce": ">=5.0.0", "wordpress": ">=5.0.0", "php": ">=7.4.0"}, "configuration": {"required": ["storeUrl", "consumerKey", "consumerSecret"], "optional": ["webhookSecret", "syncCategories", "syncImages", "defaultStatus"]}, "endpoints": {"products": "/wp-json/wc/v3/products", "orders": "/wp-json/wc/v3/orders", "customers": "/wp-json/wc/v3/customers", "webhooks": "/wp-json/wc/v3/webhooks", "inventory": "/wp-json/wc/v3/products/{id}"}, "webhooks": ["product.created", "product.updated", "product.deleted", "order.created", "order.updated", "order.completed", "customer.created", "customer.updated"], "rateLimit": {"requests": 100, "window": 60000}, "permissions": ["read_products", "write_products", "read_orders", "write_orders", "read_customers", "write_customers"]}