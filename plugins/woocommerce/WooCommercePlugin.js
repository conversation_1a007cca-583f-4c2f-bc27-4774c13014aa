/**
 * WooCommerce Plugin - Integration with WooCommerce stores
 * Handles product, order, inventory, and customer synchronization
 */

const BasePlugin = require('../core/BasePlugin');
const crypto = require('crypto');

class WooCommercePlugin extends BasePlugin {
    constructor(manifest) {
        super(manifest);
        this.apiUrl = '';
        this.auth = {};
        this.webhookEndpoints = new Map();
    }

    /**
     * Initialize WooCommerce plugin
     */
    async initialize() {
        this.log('info', 'Initializing WooCommerce plugin...');
        
        try {
            // Validate configuration
            this.validateConfig(['storeUrl', 'consumerKey', 'consumerSecret']);
            
            // Setup API URL and authentication
            this.apiUrl = this.config.storeUrl.replace(/\/$/, '') + '/wp-json/wc/v3';
            this.auth = {
                consumer_key: this.config.consumerKey,
                consumer_secret: this.config.consumerSecret
            };
            
            // Test connection
            await this.testConnection();
            
            // Setup webhooks if configured
            if (this.config.webhookSecret) {
                await this.setupWebhooks();
            }
            
            this.isInitialized = true;
            this.initTime = Date.now();
            
            this.log('info', 'WooCommerce plugin initialized successfully');
        } catch (error) {
            this.log('error', 'Failed to initialize WooCommerce plugin', { error: error.message });
            throw error;
        }
    }

    /**
     * Test connection to WooCommerce store
     */
    async testConnection() {
        try {
            const response = await this.makeWooCommerceRequest('/system_status');
            this.log('info', 'Connection test successful', { 
                version: response.environment?.version,
                status: 'connected'
            });
            return true;
        } catch (error) {
            this.log('error', 'Connection test failed', { error: error.message });
            throw new Error(`WooCommerce connection failed: ${error.message}`);
        }
    }

    /**
     * Sync products from WooCommerce to local inventory
     */
    async syncProducts() {
        this.log('info', 'Starting product sync...');
        
        try {
            let page = 1;
            let hasMore = true;
            let totalSynced = 0;
            
            while (hasMore) {
                // Rate limiting
                await this.rateLimit('products', 50, 60000);
                
                const products = await this.makeWooCommerceRequest('/products', {
                    per_page: 100,
                    page: page,
                    status: 'publish'
                });
                
                if (products.length === 0) {
                    hasMore = false;
                    break;
                }
                
                // Process each product
                for (const wcProduct of products) {
                    try {
                        await this.processProduct(wcProduct);
                        this.updateSyncStats('products', true);
                        totalSynced++;
                    } catch (error) {
                        this.log('error', `Failed to sync product ${wcProduct.id}`, { error: error.message });
                        this.updateSyncStats('products', false);
                    }
                }
                
                page++;
                
                // Prevent infinite loops
                if (page > 100) {
                    this.log('warning', 'Reached maximum page limit for product sync');
                    break;
                }
            }
            
            this.log('info', `Product sync completed: ${totalSynced} products synced`);
        } catch (error) {
            this.log('error', 'Product sync failed', { error: error.message });
            throw error;
        }
    }

    /**
     * Process individual product
     */
    async processProduct(wcProduct) {
        const product = {
            externalId: wcProduct.id.toString(),
            platform: 'woocommerce',
            name: wcProduct.name,
            sku: wcProduct.sku || `wc-${wcProduct.id}`,
            description: wcProduct.description || wcProduct.short_description || '',
            price: parseFloat(wcProduct.price) || 0,
            regularPrice: parseFloat(wcProduct.regular_price) || 0,
            salePrice: parseFloat(wcProduct.sale_price) || 0,
            stock: parseInt(wcProduct.stock_quantity) || 0,
            manageStock: wcProduct.manage_stock,
            stockStatus: wcProduct.stock_status,
            status: wcProduct.status === 'publish' ? 'active' : 'inactive',
            categories: wcProduct.categories.map(cat => cat.name),
            images: wcProduct.images.map(img => img.src),
            weight: wcProduct.weight || '',
            dimensions: {
                length: wcProduct.dimensions?.length || '',
                width: wcProduct.dimensions?.width || '',
                height: wcProduct.dimensions?.height || ''
            },
            attributes: wcProduct.attributes || [],
            variations: wcProduct.variations || [],
            lastModified: wcProduct.date_modified,
            permalink: wcProduct.permalink
        };
        
        // Send to inventory system
        await this.sendToInventorySystem('products', 'upsert', product);
        
        this.log('debug', `Processed product: ${product.name} (${product.sku})`);
    }

    /**
     * Sync inventory levels
     */
    async syncInventory() {
        this.log('info', 'Starting inventory sync...');
        
        try {
            // Get products that need inventory updates
            const productsToUpdate = await this.getInventoryUpdates();
            
            for (const update of productsToUpdate) {
                try {
                    await this.rateLimit('inventory', 50, 60000);
                    
                    await this.makeWooCommerceRequest(`/products/${update.externalId}`, {
                        stock_quantity: update.stock,
                        stock_status: update.stock > 0 ? 'instock' : 'outofstock'
                    }, 'PUT');
                    
                    this.updateSyncStats('inventory', true);
                    this.log('debug', `Updated inventory for product ${update.externalId}: ${update.stock}`);
                } catch (error) {
                    this.log('error', `Failed to update inventory for product ${update.externalId}`, { error: error.message });
                    this.updateSyncStats('inventory', false);
                }
            }
            
            this.log('info', `Inventory sync completed: ${productsToUpdate.length} products updated`);
        } catch (error) {
            this.log('error', 'Inventory sync failed', { error: error.message });
            throw error;
        }
    }

    /**
     * Sync orders from WooCommerce
     */
    async syncOrders() {
        this.log('info', 'Starting order sync...');
        
        try {
            // Get orders from last sync or last 24 hours
            const since = this.lastSync || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
            
            let page = 1;
            let hasMore = true;
            let totalSynced = 0;
            
            while (hasMore) {
                await this.rateLimit('orders', 50, 60000);
                
                const orders = await this.makeWooCommerceRequest('/orders', {
                    per_page: 100,
                    page: page,
                    modified_after: since,
                    orderby: 'date',
                    order: 'desc'
                });
                
                if (orders.length === 0) {
                    hasMore = false;
                    break;
                }
                
                for (const wcOrder of orders) {
                    try {
                        await this.processOrder(wcOrder);
                        this.updateSyncStats('orders', true);
                        totalSynced++;
                    } catch (error) {
                        this.log('error', `Failed to sync order ${wcOrder.id}`, { error: error.message });
                        this.updateSyncStats('orders', false);
                    }
                }
                
                page++;
                
                if (page > 50) {
                    this.log('warning', 'Reached maximum page limit for order sync');
                    break;
                }
            }
            
            this.log('info', `Order sync completed: ${totalSynced} orders synced`);
        } catch (error) {
            this.log('error', 'Order sync failed', { error: error.message });
            throw error;
        }
    }

    /**
     * Process individual order
     */
    async processOrder(wcOrder) {
        const order = {
            externalId: wcOrder.id.toString(),
            platform: 'woocommerce',
            orderNumber: wcOrder.number,
            status: wcOrder.status,
            currency: wcOrder.currency,
            total: parseFloat(wcOrder.total),
            subtotal: parseFloat(wcOrder.subtotal || 0),
            totalTax: parseFloat(wcOrder.total_tax || 0),
            shippingTotal: parseFloat(wcOrder.shipping_total || 0),
            customer: {
                id: wcOrder.customer_id,
                email: wcOrder.billing?.email || '',
                firstName: wcOrder.billing?.first_name || '',
                lastName: wcOrder.billing?.last_name || '',
                phone: wcOrder.billing?.phone || ''
            },
            billing: wcOrder.billing,
            shipping: wcOrder.shipping,
            items: wcOrder.line_items.map(item => ({
                productId: item.product_id,
                variationId: item.variation_id || null,
                name: item.name,
                sku: item.sku || '',
                quantity: item.quantity,
                price: parseFloat(item.price),
                total: parseFloat(item.total)
            })),
            paymentMethod: wcOrder.payment_method,
            paymentMethodTitle: wcOrder.payment_method_title,
            dateCreated: wcOrder.date_created,
            dateModified: wcOrder.date_modified,
            dateCompleted: wcOrder.date_completed,
            notes: wcOrder.customer_note || '',
            metaData: wcOrder.meta_data || []
        };
        
        // Send to inventory system
        await this.sendToInventorySystem('orders', 'upsert', order);
        
        this.log('debug', `Processed order: ${order.orderNumber} (${order.status})`);
    }

    /**
     * Sync customers from WooCommerce
     */
    async syncCustomers() {
        this.log('info', 'Starting customer sync...');
        
        try {
            const since = this.lastSync || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
            
            let page = 1;
            let hasMore = true;
            let totalSynced = 0;
            
            while (hasMore) {
                await this.rateLimit('customers', 50, 60000);
                
                const customers = await this.makeWooCommerceRequest('/customers', {
                    per_page: 100,
                    page: page,
                    orderby: 'registered_date',
                    order: 'desc'
                });
                
                if (customers.length === 0) {
                    hasMore = false;
                    break;
                }
                
                for (const wcCustomer of customers) {
                    try {
                        await this.processCustomer(wcCustomer);
                        this.updateSyncStats('customers', true);
                        totalSynced++;
                    } catch (error) {
                        this.log('error', `Failed to sync customer ${wcCustomer.id}`, { error: error.message });
                        this.updateSyncStats('customers', false);
                    }
                }
                
                page++;
                
                if (page > 20) {
                    this.log('warning', 'Reached maximum page limit for customer sync');
                    break;
                }
            }
            
            this.log('info', `Customer sync completed: ${totalSynced} customers synced`);
        } catch (error) {
            this.log('error', 'Customer sync failed', { error: error.message });
            throw error;
        }
    }

    /**
     * Process individual customer
     */
    async processCustomer(wcCustomer) {
        const customer = {
            externalId: wcCustomer.id.toString(),
            platform: 'woocommerce',
            email: wcCustomer.email,
            firstName: wcCustomer.first_name,
            lastName: wcCustomer.last_name,
            username: wcCustomer.username,
            role: wcCustomer.role,
            billing: wcCustomer.billing,
            shipping: wcCustomer.shipping,
            isPayingCustomer: wcCustomer.is_paying_customer,
            ordersCount: wcCustomer.orders_count || 0,
            totalSpent: parseFloat(wcCustomer.total_spent || 0),
            avatarUrl: wcCustomer.avatar_url,
            dateCreated: wcCustomer.date_created,
            dateModified: wcCustomer.date_modified,
            metaData: wcCustomer.meta_data || []
        };
        
        // Send to inventory system
        await this.sendToInventorySystem('customers', 'upsert', customer);
        
        this.log('debug', `Processed customer: ${customer.email}`);
    }

    /**
     * Make authenticated request to WooCommerce API
     */
    async makeWooCommerceRequest(endpoint, data = null, method = 'GET') {
        const url = new URL(this.apiUrl + endpoint);
        
        // Add authentication parameters
        url.searchParams.append('consumer_key', this.auth.consumer_key);
        url.searchParams.append('consumer_secret', this.auth.consumer_secret);
        
        // Add query parameters for GET requests
        if (method === 'GET' && data) {
            Object.keys(data).forEach(key => {
                url.searchParams.append(key, data[key]);
            });
            data = null;
        }
        
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': `InventoryMS-WooCommerce/${this.manifest.version}`
            }
        };
        
        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }
        
        return await this.makeRequest(url.toString(), options);
    }

    /**
     * Setup webhooks for real-time updates
     */
    async setupWebhooks() {
        this.log('info', 'Setting up webhooks...');
        
        const webhooks = [
            { topic: 'product.created', event: 'product.created' },
            { topic: 'product.updated', event: 'product.updated' },
            { topic: 'order.created', event: 'order.created' },
            { topic: 'order.updated', event: 'order.updated' }
        ];
        
        for (const webhook of webhooks) {
            try {
                const response = await this.makeWooCommerceRequest('/webhooks', {
                    name: `InventoryMS ${webhook.topic}`,
                    topic: webhook.topic,
                    delivery_url: `${this.config.webhookUrl}/${webhook.event}`,
                    secret: this.config.webhookSecret,
                    status: 'active'
                }, 'POST');
                
                this.webhookEndpoints.set(webhook.event, response.id);
                this.log('info', `Webhook created: ${webhook.topic}`);
            } catch (error) {
                this.log('error', `Failed to create webhook ${webhook.topic}`, { error: error.message });
            }
        }
    }

    /**
     * Get inventory updates from local system
     */
    async getInventoryUpdates() {
        // This would typically call your inventory system API
        // For now, return empty array
        return [];
    }

    /**
     * Send data to inventory system
     */
    async sendToInventorySystem(type, action, data) {
        // This would typically send data to your inventory system API
        this.log('debug', `Sending ${type} data to inventory system`, { action, id: data.externalId });
    }
}

module.exports = WooCommercePlugin;
