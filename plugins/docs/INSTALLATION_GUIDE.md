# 🚀 Plugin Installation & Configuration Guide

## Overview
This guide walks you through installing, configuring, and activating e-commerce integration plugins for the Inventory Management System.

## Prerequisites

### System Requirements
- Node.js 16.x or higher
- npm 8.x or higher
- Valid subscription plan
- Active license key

### Supported Platforms
- **WooCommerce** (Premium Plan)
- **Shopify** (Premium Plan)
- **Magento** (Enterprise Plan)
- **Amazon** (Enterprise Plan)
- **eBay** (Premium Plan)

## Installation Methods

### Method 1: Automatic Installation (Recommended)

1. **Access Plugin Manager**
   ```bash
   npm run plugins:install
   ```

2. **Select Plugin**
   - Choose from available plugins
   - Verify license requirements
   - Confirm installation

3. **Configure Plugin**
   - Enter API credentials
   - Set sync preferences
   - Test connection

### Method 2: Manual Installation

1. **Download Plugin**
   ```bash
   cd plugins/
   git clone https://github.com/inventory-ms/plugin-woocommerce.git woocommerce
   ```

2. **Install Dependencies**
   ```bash
   cd woocommerce
   npm install
   ```

3. **Activate Plugin**
   ```bash
   node ../core/PluginManager.js activate woocommerce
   ```

## Platform-Specific Setup

### 🛒 WooCommerce Setup

#### 1. Generate API Keys
1. Go to WooCommerce → Settings → Advanced → REST API
2. Click "Add Key"
3. Set permissions to "Read/Write"
4. Copy Consumer Key and Consumer Secret

#### 2. Plugin Configuration
```json
{
  "storeUrl": "https://yourstore.com",
  "consumerKey": "ck_your_consumer_key",
  "consumerSecret": "cs_your_consumer_secret",
  "webhookSecret": "optional_webhook_secret",
  "syncCategories": true,
  "syncImages": true,
  "defaultStatus": "publish"
}
```

#### 3. Webhook Setup (Optional)
1. Set webhook URL: `https://yourdomain.com/webhooks/woocommerce`
2. Configure webhook secret
3. Enable real-time sync

### 🏪 Shopify Setup

#### 1. Create Private App
1. Go to Apps → App and sales channel settings
2. Click "Develop apps"
3. Create private app with required permissions:
   - Products: Read and write
   - Orders: Read and write
   - Customers: Read and write
   - Inventory: Read and write

#### 2. Plugin Configuration
```json
{
  "shopDomain": "yourstore.myshopify.com",
  "accessToken": "shpat_your_access_token",
  "webhookSecret": "optional_webhook_secret",
  "syncVariants": true,
  "syncMetafields": false,
  "defaultLocation": "primary"
}
```

#### 3. Webhook Setup (Optional)
1. Set webhook URL: `https://yourdomain.com/webhooks/shopify`
2. Configure webhook verification
3. Enable real-time updates

### 🛍️ Magento Setup

#### 1. Generate Admin Token
```bash
curl -X POST "https://yourstore.com/rest/V1/integration/admin/token" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'
```

#### 2. Plugin Configuration
```json
{
  "baseUrl": "https://yourstore.com",
  "adminToken": "your_admin_token",
  "storeCode": "default",
  "websiteCode": "base",
  "syncCategories": true,
  "syncAttributes": true,
  "defaultAttributeSet": "Default"
}
```

### 📦 Amazon Setup

#### 1. Register for MWS/SP-API
1. Register as Amazon seller
2. Apply for API access
3. Get credentials and marketplace IDs

#### 2. Plugin Configuration
```json
{
  "sellerId": "your_seller_id",
  "mwsAuthToken": "your_mws_token",
  "accessKey": "your_access_key",
  "secretKey": "your_secret_key",
  "marketplaceId": "ATVPDKIKX0DER",
  "region": "us-east-1"
}
```

### 🎯 eBay Setup

#### 1. Create Developer Account
1. Register at eBay Developers Program
2. Create application
3. Get API keys and tokens

#### 2. Plugin Configuration
```json
{
  "appId": "your_app_id",
  "devId": "your_dev_id",
  "certId": "your_cert_id",
  "userToken": "your_user_token",
  "sandbox": false,
  "siteId": "0"
}
```

## License Activation

### 1. Purchase Subscription
- Visit: https://inventory-ms.com/pricing
- Choose appropriate plan
- Complete payment

### 2. Activate License
```bash
node plugins/licensing/activate-license.js \
  --key "your-license-key" \
  --user "your-user-id" \
  --device "your-device-id"
```

### 3. Verify Activation
```bash
node plugins/licensing/verify-license.js --plugin woocommerce
```

## Configuration Management

### Environment Variables
```bash
# .env file
LICENSE_SECRET_KEY=your-secret-key
WEBHOOK_BASE_URL=https://yourdomain.com/webhooks
ENCRYPTION_KEY=your-encryption-key
```

### Configuration File
```json
{
  "plugins": {
    "woocommerce": {
      "enabled": true,
      "autoSync": true,
      "syncInterval": 300000,
      "config": {
        "storeUrl": "https://yourstore.com",
        "consumerKey": "ck_key",
        "consumerSecret": "cs_secret"
      }
    }
  }
}
```

## Testing Installation

### 1. Connection Test
```bash
node plugins/test-connection.js --plugin woocommerce
```

### 2. Sync Test
```bash
node plugins/test-sync.js --plugin woocommerce --type products --limit 10
```

### 3. Webhook Test
```bash
curl -X POST "https://yourdomain.com/webhooks/woocommerce/product/created" \
  -H "Content-Type: application/json" \
  -d '{"test": true}'
```

## Monitoring & Logs

### View Plugin Status
```bash
node plugins/status.js
```

### View Logs
```bash
tail -f logs/plugins.log
```

### Monitor Sync Progress
```bash
node plugins/monitor.js --plugin woocommerce
```

## Troubleshooting

### Common Issues

#### 1. Authentication Errors
- Verify API credentials
- Check permissions
- Ensure tokens are not expired

#### 2. Rate Limiting
- Reduce sync frequency
- Implement exponential backoff
- Monitor rate limit headers

#### 3. Sync Failures
- Check network connectivity
- Verify data formats
- Review error logs

#### 4. License Issues
- Verify subscription status
- Check license expiration
- Ensure correct plan for plugin

### Debug Mode
```bash
DEBUG=plugins:* node app.js
```

### Log Levels
```json
{
  "logging": {
    "level": "debug",
    "file": "logs/plugins.log",
    "console": true
  }
}
```

## Performance Optimization

### 1. Sync Optimization
- Use incremental sync
- Implement pagination
- Cache frequently accessed data

### 2. Database Optimization
- Index frequently queried fields
- Use connection pooling
- Optimize query performance

### 3. Memory Management
- Monitor memory usage
- Implement garbage collection
- Use streaming for large datasets

## Security Best Practices

### 1. Credential Management
- Use environment variables
- Encrypt sensitive data
- Rotate keys regularly

### 2. Webhook Security
- Validate webhook signatures
- Use HTTPS endpoints
- Implement rate limiting

### 3. Access Control
- Limit API permissions
- Use least privilege principle
- Monitor access logs

## Backup & Recovery

### 1. Configuration Backup
```bash
cp plugins/config.json plugins/config.backup.json
```

### 2. License Backup
```bash
cp plugins/licensing/licenses.json plugins/licensing/licenses.backup.json
```

### 3. Recovery Process
1. Stop plugin services
2. Restore configuration files
3. Restart services
4. Verify functionality

## Support

### Documentation
- Plugin Development Guide
- API Reference
- FAQ

### Community
- Discord: https://discord.gg/inventory-ms
- Forum: https://forum.inventory-ms.com
- GitHub: https://github.com/inventory-ms

### Professional Support
- Email: <EMAIL>
- Phone: ******-INVENTORY
- Priority support for Enterprise plans
