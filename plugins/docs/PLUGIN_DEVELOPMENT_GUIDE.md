# 🔌 Plugin Development Guide

## Overview
This guide explains how to develop custom plugins for the Inventory Management System. Plugins enable integration with various e-commerce platforms and external services.

## Plugin Architecture

### Base Plugin Class
All plugins must extend the `BasePlugin` class which provides:
- Common functionality and utilities
- Rate limiting and error handling
- Logging and event emission
- Configuration validation
- HTTP request helpers

### Plugin Structure
```
plugins/
├── your-plugin/
│   ├── manifest.json          # Plugin metadata
│   ├── YourPlugin.js          # Main plugin class
│   ├── config.json            # Default configuration
│   ├── README.md              # Plugin documentation
│   └── tests/                 # Unit tests
│       └── YourPlugin.test.js
```

## Creating a New Plugin

### 1. Plugin Manifest
Create a `manifest.json` file with plugin metadata:

```json
{
  "id": "your-plugin",
  "name": "Your Platform Integration",
  "version": "1.0.0",
  "description": "Integration with Your Platform",
  "author": "Your Name",
  "license": "Premium",
  "main": "YourPlugin.js",
  "platform": "YourPlatform",
  "apiVersion": "v1",
  "autoSync": true,
  "syncInterval": 300000,
  "features": [
    "product-sync",
    "order-sync"
  ],
  "configuration": {
    "required": [
      "apiKey",
      "apiSecret"
    ],
    "optional": [
      "webhookUrl"
    ]
  },
  "rateLimit": {
    "requests": 100,
    "window": 60000
  }
}
```

### 2. Plugin Class
Create your main plugin class extending `BasePlugin`:

```javascript
const BasePlugin = require('../core/BasePlugin');

class YourPlugin extends BasePlugin {
    constructor(manifest) {
        super(manifest);
        this.apiUrl = '';
        this.apiKey = '';
    }

    async initialize() {
        this.log('info', 'Initializing Your Plugin...');
        
        // Validate configuration
        this.validateConfig(['apiKey', 'apiSecret']);
        
        // Setup API credentials
        this.apiKey = this.config.apiKey;
        this.apiUrl = 'https://api.yourplatform.com/v1';
        
        // Test connection
        await this.testConnection();
        
        this.isInitialized = true;
        this.log('info', 'Your Plugin initialized successfully');
    }

    async testConnection() {
        try {
            const response = await this.makeRequest(`${this.apiUrl}/status`, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                }
            });
            return response.status === 'ok';
        } catch (error) {
            throw new Error(`Connection failed: ${error.message}`);
        }
    }

    async syncProducts() {
        this.log('info', 'Starting product sync...');
        
        try {
            // Implement product synchronization
            const products = await this.fetchProducts();
            
            for (const product of products) {
                await this.processProduct(product);
                this.updateSyncStats('products', true);
            }
            
            this.log('info', `Product sync completed: ${products.length} products`);
        } catch (error) {
            this.log('error', 'Product sync failed', { error: error.message });
            throw error;
        }
    }

    async syncOrders() {
        // Implement order synchronization
    }

    async syncInventory() {
        // Implement inventory synchronization
    }

    async syncCustomers() {
        // Implement customer synchronization
    }

    async fetchProducts() {
        // Implement API call to fetch products
        return [];
    }

    async processProduct(product) {
        // Process and transform product data
        const transformedProduct = {
            externalId: product.id,
            platform: 'yourplatform',
            name: product.name,
            // ... other fields
        };
        
        await this.sendToInventorySystem('products', 'upsert', transformedProduct);
    }

    async sendToInventorySystem(type, action, data) {
        // Send data to inventory system
        this.log('debug', `Sending ${type} data`, { action, id: data.externalId });
    }
}

module.exports = YourPlugin;
```

## Plugin Configuration

### Required Configuration
Define required configuration fields in the manifest:
```json
"configuration": {
  "required": [
    "apiKey",
    "apiSecret",
    "storeUrl"
  ]
}
```

### Configuration Validation
Use the built-in validation method:
```javascript
this.validateConfig(['apiKey', 'apiSecret']);
```

### Secure Configuration
Encrypt sensitive data:
```javascript
const encryptedKey = this.encrypt(this.config.apiKey, 'encryption-key');
```

## Rate Limiting

### Built-in Rate Limiting
Use the rate limiting helper:
```javascript
await this.rateLimit('products', 100, 60000); // 100 requests per minute
```

### Custom Rate Limiting
Implement platform-specific rate limiting:
```javascript
async handleRateLimit() {
    if (this.rateLimitInfo.remaining <= 5) {
        const waitTime = this.rateLimitInfo.resetTime - Date.now();
        await this.sleep(waitTime);
    }
}
```

## Error Handling

### Logging
Use the built-in logging system:
```javascript
this.log('info', 'Operation completed');
this.log('error', 'Operation failed', { error: error.message });
this.log('debug', 'Debug information', { data: someData });
```

### Error Recovery
Implement retry logic:
```javascript
async makeRequestWithRetry(url, options, retries = 3) {
    for (let attempt = 1; attempt <= retries; attempt++) {
        try {
            return await this.makeRequest(url, options);
        } catch (error) {
            if (attempt === retries) throw error;
            await this.sleep(Math.pow(2, attempt) * 1000);
        }
    }
}
```

## Testing

### Unit Tests
Create unit tests for your plugin:
```javascript
const YourPlugin = require('../YourPlugin');

describe('YourPlugin', () => {
    let plugin;
    
    beforeEach(() => {
        const manifest = require('../manifest.json');
        plugin = new YourPlugin(manifest);
        plugin.config = {
            apiKey: 'test-key',
            apiSecret: 'test-secret'
        };
    });
    
    test('should initialize successfully', async () => {
        await expect(plugin.initialize()).resolves.not.toThrow();
        expect(plugin.isInitialized).toBe(true);
    });
    
    test('should sync products', async () => {
        await plugin.initialize();
        await expect(plugin.syncProducts()).resolves.not.toThrow();
    });
});
```

## Best Practices

### 1. Error Handling
- Always wrap API calls in try-catch blocks
- Implement proper retry logic
- Log errors with context

### 2. Rate Limiting
- Respect platform rate limits
- Implement exponential backoff
- Monitor rate limit headers

### 3. Data Transformation
- Validate data before processing
- Handle missing or null values
- Normalize data formats

### 4. Performance
- Use pagination for large datasets
- Implement efficient caching
- Minimize API calls

### 5. Security
- Encrypt sensitive configuration
- Validate webhook signatures
- Use secure HTTP headers

## Plugin Lifecycle

### 1. Discovery
- Plugin Manager scans plugin directories
- Loads manifest.json files
- Registers available plugins

### 2. Activation
- Validates license
- Loads plugin class
- Calls initialize() method

### 3. Synchronization
- Starts automatic sync if enabled
- Handles webhook events
- Processes data updates

### 4. Deactivation
- Stops sync processes
- Calls cleanup() method
- Removes event listeners

## Webhook Support

### Setting up Webhooks
```javascript
async setupWebhooks() {
    const webhooks = [
        { event: 'product.created', url: `${this.config.webhookUrl}/product/created` },
        { event: 'order.created', url: `${this.config.webhookUrl}/order/created` }
    ];
    
    for (const webhook of webhooks) {
        await this.createWebhook(webhook);
    }
}
```

### Webhook Validation
```javascript
validateWebhook(payload, signature) {
    const expectedSignature = crypto
        .createHmac('sha256', this.config.webhookSecret)
        .update(payload)
        .digest('hex');
    
    return signature === expectedSignature;
}
```

## Publishing Your Plugin

### 1. Documentation
- Complete README.md
- API documentation
- Configuration examples
- Troubleshooting guide

### 2. Testing
- Unit tests with good coverage
- Integration tests
- Performance tests

### 3. Packaging
- Clean code structure
- Proper error handling
- Security considerations

### 4. Submission
- Submit to plugin registry
- Provide license information
- Include support contact
