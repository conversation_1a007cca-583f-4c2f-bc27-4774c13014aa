# 📚 Plugin API Reference

## Overview
This document provides comprehensive API reference for the Plugin System, including core classes, methods, and interfaces.

## Core Classes

### PluginManager

The main class responsible for managing all plugins.

#### Constructor
```javascript
const pluginManager = new PluginManager();
```

#### Methods

##### `initialize()`
Initializes the plugin manager and discovers available plugins.
```javascript
await pluginManager.initialize();
```

##### `activatePlugin(pluginId)`
Activates a specific plugin.
```javascript
await pluginManager.activatePlugin('woocommerce');
```
- **Parameters:**
  - `pluginId` (string): The plugin identifier
- **Returns:** Promise<boolean>
- **Throws:** Error if plugin not found or license invalid

##### `deactivatePlugin(pluginId)`
Deactivates a specific plugin.
```javascript
await pluginManager.deactivatePlugin('woocommerce');
```

##### `getPluginStatus(pluginId)`
Gets the status of a specific plugin.
```javascript
const status = pluginManager.getPluginStatus('woocommerce');
```
- **Returns:** Object with plugin status information

##### `getAllPluginsStatus()`
Gets status of all plugins.
```javascript
const allStatus = pluginManager.getAllPluginsStatus();
```

##### `executePluginMethod(pluginId, method, ...args)`
Executes a method on a specific plugin.
```javascript
await pluginManager.executePluginMethod('woocommerce', 'syncProducts');
```

### BasePlugin

Abstract base class that all plugins must extend.

#### Constructor
```javascript
class YourPlugin extends BasePlugin {
    constructor(manifest) {
        super(manifest);
    }
}
```

#### Abstract Methods

##### `initialize()`
Must be implemented by subclasses to initialize the plugin.
```javascript
async initialize() {
    // Plugin initialization logic
}
```

##### `syncProducts()`
Must be implemented to sync products.
```javascript
async syncProducts() {
    // Product sync logic
}
```

##### `syncOrders()`
Must be implemented to sync orders.
```javascript
async syncOrders() {
    // Order sync logic
}
```

##### `syncInventory()`
Must be implemented to sync inventory.
```javascript
async syncInventory() {
    // Inventory sync logic
}
```

##### `syncCustomers()`
Must be implemented to sync customers.
```javascript
async syncCustomers() {
    // Customer sync logic
}
```

##### `testConnection()`
Must be implemented to test platform connection.
```javascript
async testConnection() {
    // Connection test logic
    return true; // or false
}
```

#### Utility Methods

##### `validateConfig(requiredFields)`
Validates plugin configuration.
```javascript
this.validateConfig(['apiKey', 'apiSecret']);
```

##### `makeRequest(url, options, retries)`
Makes HTTP requests with retry logic.
```javascript
const response = await this.makeRequest('https://api.example.com/data', {
    method: 'GET',
    headers: { 'Authorization': 'Bearer token' }
});
```

##### `rateLimit(key, maxRequests, windowMs)`
Implements rate limiting.
```javascript
await this.rateLimit('products', 100, 60000); // 100 requests per minute
```

##### `log(level, message, data)`
Logs messages with plugin context.
```javascript
this.log('info', 'Operation completed', { count: 10 });
this.log('error', 'Operation failed', { error: error.message });
```

##### `updateSyncStats(type, success)`
Updates synchronization statistics.
```javascript
this.updateSyncStats('products', true);  // Success
this.updateSyncStats('products', false); // Error
```

##### `encrypt(text, key)` / `decrypt(encryptedData, key)`
Encrypts/decrypts sensitive data.
```javascript
const encrypted = this.encrypt('sensitive-data', 'encryption-key');
const decrypted = this.decrypt(encrypted, 'encryption-key');
```

##### `sleep(ms)`
Utility for delays.
```javascript
await this.sleep(1000); // Wait 1 second
```

### LicenseManager

Manages plugin licensing and subscriptions.

#### Methods

##### `validateLicense(pluginId, userId)`
Validates if a license exists for a plugin.
```javascript
const isValid = await licenseManager.validateLicense('woocommerce', 'user123');
```

##### `activateLicense(licenseKey, userId, deviceId)`
Activates a license key.
```javascript
const license = await licenseManager.activateLicense(
    'license-key',
    'user123',
    'device456'
);
```

##### `createSubscription(userId, planId, paymentMethod)`
Creates a new subscription.
```javascript
const { subscription, licenses } = await licenseManager.createSubscription(
    'user123',
    'premium',
    'stripe'
);
```

##### `getUserSubscription(userId)`
Gets user's subscription details.
```javascript
const subscription = licenseManager.getUserSubscription('user123');
```

##### `getPlans()`
Gets available subscription plans.
```javascript
const plans = licenseManager.getPlans();
```

## Data Structures

### Plugin Manifest
```typescript
interface PluginManifest {
    id: string;
    name: string;
    version: string;
    description: string;
    author: string;
    license: 'Free' | 'Premium' | 'Enterprise';
    main: string;
    platform: string;
    apiVersion: string;
    autoSync: boolean;
    syncInterval: number;
    features: string[];
    requirements: Record<string, string>;
    configuration: {
        required: string[];
        optional: string[];
    };
    endpoints: Record<string, string>;
    webhooks: string[];
    rateLimit: {
        requests: number;
        window: number;
    };
    permissions: string[];
}
```

### Product Data Structure
```typescript
interface Product {
    externalId: string;
    platform: string;
    name: string;
    sku: string;
    description: string;
    price: number;
    regularPrice?: number;
    salePrice?: number;
    stock: number;
    manageStock: boolean;
    stockStatus: string;
    status: 'active' | 'inactive';
    categories: string[];
    images: string[];
    weight?: string;
    dimensions?: {
        length: string;
        width: string;
        height: string;
    };
    attributes: any[];
    variations?: any[];
    lastModified: string;
    permalink?: string;
}
```

### Order Data Structure
```typescript
interface Order {
    externalId: string;
    platform: string;
    orderNumber: string;
    status: string;
    currency: string;
    total: number;
    subtotal: number;
    totalTax: number;
    shippingTotal: number;
    customer: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        phone: string;
    };
    billing: any;
    shipping: any;
    items: OrderItem[];
    paymentMethod: string;
    paymentMethodTitle: string;
    dateCreated: string;
    dateModified: string;
    dateCompleted?: string;
    notes: string;
    metaData: any[];
}

interface OrderItem {
    productId: string;
    variationId?: string;
    name: string;
    sku: string;
    quantity: number;
    price: number;
    total: number;
}
```

### Customer Data Structure
```typescript
interface Customer {
    externalId: string;
    platform: string;
    email: string;
    firstName: string;
    lastName: string;
    username?: string;
    phone?: string;
    billing: any;
    shipping: any;
    ordersCount: number;
    totalSpent: number;
    dateCreated: string;
    dateModified: string;
    metaData: any[];
}
```

## Events

### Plugin Events
Plugins emit various events that can be listened to:

```javascript
plugin.on('sync:start', (data) => {
    console.log('Sync started:', data);
});

plugin.on('sync:complete', (data) => {
    console.log('Sync completed:', data);
});

plugin.on('sync:error', (error) => {
    console.error('Sync error:', error);
});

plugin.on('log', (logEntry) => {
    console.log('Plugin log:', logEntry);
});
```

### Available Events
- `sync:start` - Sync process started
- `sync:complete` - Sync process completed
- `sync:error` - Sync process failed
- `product:synced` - Product synchronized
- `order:synced` - Order synchronized
- `customer:synced` - Customer synchronized
- `inventory:updated` - Inventory updated
- `log` - Log message emitted

## Error Handling

### Error Types
```typescript
class PluginError extends Error {
    constructor(message: string, code: string, details?: any) {
        super(message);
        this.name = 'PluginError';
        this.code = code;
        this.details = details;
    }
}

// Common error codes
const ERROR_CODES = {
    PLUGIN_NOT_FOUND: 'PLUGIN_NOT_FOUND',
    INVALID_LICENSE: 'INVALID_LICENSE',
    CONNECTION_FAILED: 'CONNECTION_FAILED',
    RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
    SYNC_FAILED: 'SYNC_FAILED',
    CONFIGURATION_INVALID: 'CONFIGURATION_INVALID'
};
```

### Error Handling Example
```javascript
try {
    await pluginManager.activatePlugin('woocommerce');
} catch (error) {
    if (error.code === 'INVALID_LICENSE') {
        console.error('License validation failed');
    } else if (error.code === 'CONNECTION_FAILED') {
        console.error('Could not connect to platform');
    } else {
        console.error('Unexpected error:', error.message);
    }
}
```

## Configuration

### Plugin Configuration Schema
```json
{
  "type": "object",
  "properties": {
    "enabled": {
      "type": "boolean",
      "default": false
    },
    "autoSync": {
      "type": "boolean",
      "default": true
    },
    "syncInterval": {
      "type": "number",
      "minimum": 60000,
      "default": 300000
    },
    "config": {
      "type": "object",
      "properties": {
        "apiKey": {
          "type": "string",
          "minLength": 1
        },
        "apiSecret": {
          "type": "string",
          "minLength": 1
        }
      },
      "required": ["apiKey", "apiSecret"]
    }
  }
}
```

## Rate Limiting

### Rate Limit Configuration
```javascript
const rateLimitConfig = {
    woocommerce: {
        requests: 100,
        window: 60000, // 1 minute
        burst: 200
    },
    shopify: {
        requests: 40,
        window: 1000, // 1 second
        burst: 80
    }
};
```

### Rate Limit Headers
Plugins should respect platform-specific rate limit headers:
- `X-RateLimit-Limit`
- `X-RateLimit-Remaining`
- `X-RateLimit-Reset`
- `Retry-After`

## Webhooks

### Webhook Configuration
```javascript
const webhookConfig = {
    url: 'https://yourdomain.com/webhooks/{platform}/{event}',
    secret: 'webhook-secret-key',
    events: [
        'product.created',
        'product.updated',
        'order.created',
        'order.updated'
    ]
};
```

### Webhook Validation
```javascript
function validateWebhook(payload, signature, secret) {
    const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(payload)
        .digest('hex');
    
    return signature === expectedSignature;
}
```

## Testing

### Unit Test Example
```javascript
const YourPlugin = require('../YourPlugin');

describe('YourPlugin', () => {
    let plugin;
    
    beforeEach(() => {
        const manifest = require('../manifest.json');
        plugin = new YourPlugin(manifest);
        plugin.config = {
            apiKey: 'test-key',
            apiSecret: 'test-secret'
        };
    });
    
    test('should initialize successfully', async () => {
        await expect(plugin.initialize()).resolves.not.toThrow();
        expect(plugin.isInitialized).toBe(true);
    });
    
    test('should validate configuration', () => {
        expect(() => {
            plugin.validateConfig(['apiKey', 'apiSecret']);
        }).not.toThrow();
    });
    
    test('should handle rate limiting', async () => {
        const start = Date.now();
        await plugin.rateLimit('test', 1, 1000);
        await plugin.rateLimit('test', 1, 1000);
        const duration = Date.now() - start;
        
        expect(duration).toBeGreaterThan(1000);
    });
});
```

## Best Practices

### 1. Error Handling
- Always wrap API calls in try-catch blocks
- Provide meaningful error messages
- Implement proper retry logic

### 2. Rate Limiting
- Respect platform rate limits
- Implement exponential backoff
- Monitor rate limit headers

### 3. Data Validation
- Validate all input data
- Handle missing or null values
- Sanitize user input

### 4. Performance
- Use pagination for large datasets
- Implement efficient caching
- Minimize API calls

### 5. Security
- Encrypt sensitive configuration
- Validate webhook signatures
- Use secure HTTP headers
