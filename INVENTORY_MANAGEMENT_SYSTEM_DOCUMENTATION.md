# 📚 COMPREHENSIVE INVENTORY MANAGEMENT SYSTEM DOCUMENTATION

## Complete Developer Guide & System Architecture

---

## 📋 TABLE OF CONTENTS

1. [System Overview](#system-overview)
2. [Architecture & Design Patterns](#architecture--design-patterns)
3. [Project Structure](#project-structure)
4. [Component Documentation](#component-documentation)
5. [State Management](#state-management)
6. [Styling & UI Framework](#styling--ui-framework)
7. [API Integration Guide](#api-integration-guide)
8. [Database Schema](#database-schema)
9. [Development Setup](#development-setup)
10. [Customization Guide](#customization-guide)
11. [Deployment Guide](#deployment-guide)
12. [Testing Strategy](#testing-strategy)
13. [Performance Optimization](#performance-optimization)
14. [Security Considerations](#security-considerations)
15. [Future Enhancements](#future-enhancements)

---

## 🎯 SYSTEM OVERVIEW

### Project Description
A comprehensive inventory management system built with React, TypeScript, and Bootstrap 5. The system provides complete CRUD operations for product management, real-time dashboard analytics, stock monitoring, and a clean, responsive user interface.

### Key Features
- **Dashboard Analytics**: Real-time statistics, sales charts, low stock alerts
- **Product Management**: Complete CRUD operations with search, sort, and filter
- **Responsive Design**: Mobile-first approach with Bootstrap 5
- **Type Safety**: Full TypeScript implementation
- **Modular Architecture**: Reusable components and clean code structure
- **API Ready**: Designed for easy backend integration

### Technology Stack
```
Frontend:
├── React 18.x
├── TypeScript 5.x
├── Bootstrap 5.3.x
├── React Router 6.x
├── Vite (Build Tool)
└── CSS3 (Custom Styling)

Development Tools:
├── ESLint (Code Quality)
├── Prettier (Code Formatting)
├── Git (Version Control)
└── VS Code (Recommended IDE)
```

### System Requirements
- Node.js 18.x or higher
- npm 9.x or higher
- Modern web browser (Chrome, Firefox, Safari, Edge)
- 4GB RAM minimum
- 1GB free disk space

---

## 🏗️ ARCHITECTURE & DESIGN PATTERNS

### Component Architecture

```
┌─────────────────────────────────────────┐
│                 App.tsx                 │
│           (Main Application)            │
└─────────────────┬───────────────────────┘
                  │
    ┌─────────────┴─────────────┐
    │                           │
┌───▼────┐                 ┌────▼────┐
│ Layout │                 │  Pages  │
│Components│                │Components│
└───┬────┘                 └────┬────┘
    │                           │
┌───▼────┐                 ┌────▼────┐
│Sidebar │                 │Dashboard│
│Header  │                 │Products │
│Layout  │                 │etc.     │
└────────┘                 └─────────┘
```

### Design Patterns Used

#### 1. Component Composition Pattern
```typescript
// Layout components compose smaller components
<SimpleLayout>
  <SimpleSidebar />
  <SimpleHeader />
  <MainContent>
    <Outlet /> // React Router outlet for pages
  </MainContent>
</SimpleLayout>
```

#### 2. Container/Presentational Pattern
```typescript
// Container Component (Products.tsx)
const Products: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  // Business logic here

  return (
    <ProductTable
      products={products}
      onEdit={handleEdit}
      onDelete={handleDelete}
    />
  );
};

// Presentational Component (ProductTable.tsx)
const ProductTable: React.FC<ProductTableProps> = ({
  products,
  onEdit,
  onDelete
}) => {
  // Only UI logic here
  return <table>...</table>;
};
```

#### 3. Custom Hooks Pattern
```typescript
// Custom hook for product management
const useProducts = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);

  const addProduct = async (product: Product) => {
    setLoading(true);
    // API call logic
    setLoading(false);
  };

  return { products, loading, addProduct };
};
```

### SOLID Principles Implementation

#### Single Responsibility Principle (SRP)
- Each component has a single, well-defined purpose
- StatsCard only handles displaying statistics
- ProductForm only handles product form logic
- ProductTable only handles product listing

#### Open/Closed Principle (OCP)
- Components are open for extension but closed for modification
- New product fields can be added without changing existing components
- New dashboard widgets can be added without modifying existing ones

#### Liskov Substitution Principle (LSP)
- All components implementing the same interface are interchangeable
- Different chart components can replace SalesChart without breaking the system

#### Interface Segregation Principle (ISP)
- Components only depend on interfaces they actually use
- ProductTable doesn't depend on form-specific interfaces

#### Dependency Inversion Principle (DIP)
- High-level components don't depend on low-level components
- Both depend on abstractions (TypeScript interfaces)

---

## 📁 PROJECT STRUCTURE

### Complete Directory Structure

```
inventory-management-system/
├── frontend/
│   ├── public/
│   │   ├── index.html
│   │   └── favicon.ico
│   ├── src/
│   │   ├── components/
│   │   │   ├── Layout/
│   │   │   │   ├── SimpleLayout.tsx
│   │   │   │   ├── SimpleSidebar.tsx
│   │   │   │   ├── SimpleHeader.tsx
│   │   │   │   ├── HandoLayout.tsx (Legacy)
│   │   │   │   ├── HandoSidebar.tsx (Legacy)
│   │   │   │   └── HandoHeader.tsx (Legacy)
│   │   │   ├── Dashboard/
│   │   │   │   ├── StatsCard.tsx
│   │   │   │   ├── RecentOrders.tsx
│   │   │   │   ├── LowStockAlert.tsx
│   │   │   │   └── SalesChart.tsx
│   │   │   ├── Products/
│   │   │   │   ├── ProductTable.tsx
│   │   │   │   ├── ProductForm.tsx
│   │   │   │   └── ProductCard.tsx (Future)
│   │   │   ├── Common/
│   │   │   │   ├── LoadingSpinner.tsx
│   │   │   │   ├── ErrorBoundary.tsx
│   │   │   │   └── ConfirmDialog.tsx
│   │   │   └── Auth/
│   │   │       ├── LoginForm.tsx
│   │   │       ├── ProtectedRoute.tsx
│   │   │       └── AuthContext.tsx
│   │   ├── pages/
│   │   │   ├── Dashboard.tsx
│   │   │   ├── Products.tsx
│   │   │   ├── Login.tsx
│   │   │   ├── Analytics.tsx (Future)
│   │   │   ├── CRM.tsx (Future)
│   │   │   └── Settings.tsx (Future)
│   │   ├── hooks/
│   │   │   ├── useAuth.ts
│   │   │   ├── useProducts.ts
│   │   │   ├── useLocalStorage.ts
│   │   │   └── useApi.ts
│   │   ├── services/
│   │   │   ├── api.ts
│   │   │   ├── productService.ts
│   │   │   ├── authService.ts
│   │   │   └── storageService.ts
│   │   ├── types/
│   │   │   ├── index.ts
│   │   │   ├── product.ts
│   │   │   ├── user.ts
│   │   │   └── api.ts
│   │   ├── utils/
│   │   │   ├── constants.ts
│   │   │   ├── helpers.ts
│   │   │   ├── validators.ts
│   │   │   └── formatters.ts
│   │   ├── styles/
│   │   │   ├── index.css
│   │   │   ├── hando.css
│   │   │   └── components.css
│   │   ├── App.tsx
│   │   ├── main.tsx
│   │   └── vite-env.d.ts
│   ├── package.json
│   ├── tsconfig.json
│   ├── vite.config.ts
│   └── README.md
├── backend/ (Future API)
│   ├── src/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── middleware/
│   │   └── services/
│   ├── package.json
│   └── README.md
├── docs/
│   ├── API.md
│   ├── DEPLOYMENT.md
│   └── CONTRIBUTING.md
└── README.md
```

### File Naming Conventions

#### Components
- **PascalCase**: `ProductTable.tsx`, `StatsCard.tsx`
- **Descriptive Names**: Clear purpose indication
- **TypeScript Extension**: `.tsx` for components with JSX

#### Hooks
- **camelCase with 'use' prefix**: `useProducts.ts`, `useAuth.ts`
- **TypeScript Extension**: `.ts` for logic-only files

#### Services
- **camelCase with 'Service' suffix**: `productService.ts`, `authService.ts`
- **Single Responsibility**: One service per domain

#### Types
- **camelCase**: `product.ts`, `user.ts`
- **Interface Exports**: Export interfaces and types

#### Utilities
- **camelCase**: `helpers.ts`, `validators.ts`
- **Pure Functions**: No side effects

---

## 🧩 COMPONENT DOCUMENTATION

### Layout Components

#### SimpleLayout.tsx
```typescript
interface LayoutProps {
  children?: React.ReactNode;
}

const SimpleLayout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSidebarClose = () => {
    setSidebarOpen(false);
  };

  return (
    <div id="simple-layout-wrapper">
      <SimpleSidebar open={sidebarOpen} onClose={handleSidebarClose} />
      <SimpleHeader onMenuToggle={handleSidebarToggle} />
      <div className="simple-main">
        <div className="container-fluid">
          {children || <Outlet />}
        </div>
      </div>
      {sidebarOpen && (
        <div
          className="d-lg-none position-fixed w-100 h-100 bg-dark bg-opacity-50"
          style={{ zIndex: 1040, top: 0, left: 0 }}
          onClick={handleSidebarClose}
        />
      )}
    </div>
  );
};
```

**Features:**
- Responsive sidebar management
- Mobile overlay support
- Clean CSS class structure
- React Router integration
- State management for sidebar visibility

**Props:**
- `children` (optional): React nodes to render in main content area

#### SimpleSidebar.tsx
```typescript
interface SidebarProps {
  open: boolean;
  onClose: () => void;
}

const SimpleSidebar: React.FC<SidebarProps> = ({ open, onClose }) => {
  const location = useLocation();

  const menuItems = [
    { text: 'Dashboard', path: '/dashboard' },
    { text: 'CRM', path: '/crm' },
    { text: 'Analytics', path: '/analytics' },
    { text: 'Ecommerce', path: '/dashboard' },
    { text: 'Products', path: '/products' },
    { text: 'Projects', path: '/projects' },
    { text: 'Tasks', path: '/tasks' },
    { text: 'HRM', path: '/hrm' }
  ];

  const isItemActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className="simple-sidebar">
      {/* Logo */}
      <div className="text-center py-3 border-bottom">
        <Link to="/dashboard" className="text-decoration-none">
          <h4 className="text-primary fw-bold mb-0">Hando</h4>
        </Link>
      </div>

      {/* Navigation Menu */}
      <div className="p-3">
        <h6 className="text-muted text-uppercase fw-bold mb-3">MENU</h6>
        <ul className="list-unstyled mb-0">
          {menuItems.map((item) => {
            const isActive = isItemActive(item.path);
            return (
              <li key={item.text} className="mb-1">
                <Link
                  to={item.path}
                  className={`d-flex align-items-center px-3 py-2 text-decoration-none rounded ${
                    isActive ? 'bg-primary text-white' : 'text-dark'
                  }`}
                >
                  <span>{item.text}</span>
                </Link>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};
```

**Features:**
- Active route highlighting
- Hover effects with CSS transitions
- Clean typography
- Responsive design
- React Router integration

**Props:**
- `open` (boolean): Controls sidebar visibility on mobile
- `onClose` (function): Callback for closing sidebar

#### SimpleHeader.tsx
```typescript
interface HeaderProps {
  onMenuToggle: () => void;
}

const SimpleHeader: React.FC<HeaderProps> = ({ onMenuToggle }) => {
  const [notificationMenuOpen, setNotificationMenuOpen] = useState(false);
  const [profileMenuOpen, setProfileMenuOpen] = useState(false);

  return (
    <header className="simple-header d-flex align-items-center justify-content-between px-3">
      {/* Left side */}
      <div className="d-flex align-items-center">
        <button
          type="button"
          className="btn btn-light btn-sm me-3"
          onClick={onMenuToggle}
        >
          ☰
        </button>

        <form className="d-none d-lg-block">
          <div className="position-relative">
            <input
              type="text"
              className="form-control form-control-sm"
              placeholder="Search..."
              style={{
                paddingLeft: '35px',
                width: '300px',
                border: '1px solid #e9ecef',
                borderRadius: '20px'
              }}
            />
            <span className="position-absolute top-50 translate-middle-y text-muted">
              🔍
            </span>
          </div>
        </form>
      </div>

      {/* Right side */}
      <div className="d-flex align-items-center">
        <button
          type="button"
          className="btn btn-light btn-sm me-2 position-relative"
          onClick={() => setNotificationMenuOpen(!notificationMenuOpen)}
        >
          🔔
          <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
            3
          </span>
        </button>

        <button
          type="button"
          className="btn btn-light btn-sm d-flex align-items-center"
          onClick={() => setProfileMenuOpen(!profileMenuOpen)}
        >
          <img
            className="rounded-circle me-2"
            src="https://via.placeholder.com/32x32/6c5ce7/ffffff?text=A"
            alt="Avatar"
            width="32"
            height="32"
          />
          <span className="d-none d-xl-inline-block">Admin</span>
        </button>
      </div>
    </header>
  );
};
```

**Features:**
- Responsive search bar
- Notification badge
- User profile dropdown
- Mobile menu toggle
- Clean emoji icons

**Props:**
- `onMenuToggle` (function): Callback for toggling mobile sidebar

---

### Dashboard Components

#### StatsCard.tsx
```typescript
interface StatsCardProps {
  title: string;
  value: string | number;
  change: string;
  changeType: 'positive' | 'negative';
  icon: string;
  color: 'primary' | 'success' | 'info' | 'warning' | 'danger';
}

const StatsCard: React.FC<StatsCardProps> = ({
  title, value, change, changeType, icon, color
}) => {
  const colorClasses = {
    primary: 'bg-primary',
    success: 'bg-success',
    info: 'bg-info',
    warning: 'bg-warning',
    danger: 'bg-danger'
  };

  const changeClasses = {
    positive: 'bg-success-subtle text-success',
    negative: 'bg-danger-subtle text-danger'
  };

  return (
    <div className="col-xl-3 col-md-6 mb-4">
      <div className="card h-100">
        <div className="card-body">
          <div className="d-flex align-items-center">
            <div className="flex-grow-1">
              <p className="text-muted mb-1 fs-6">{title}</p>
              <h4 className="mb-0 fs-4 fw-bold">{value}</h4>
              <div className="d-flex align-items-center mt-2">
                <span className={`badge ${changeClasses[changeType]} px-2 py-1`}>
                  <span className="me-1">
                    {changeType === 'positive' ? '↗' : '↘'}
                  </span>
                  {change}
                </span>
                <span className="text-muted fs-6 ms-2">vs last month</span>
              </div>
            </div>
            <div className="flex-shrink-0">
              <div
                className={`${colorClasses[color]} text-white rounded-circle d-flex align-items-center justify-content-center`}
                style={{width: '48px', height: '48px', fontSize: '20px'}}
              >
                {icon}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
```

**Features:**
- Responsive grid layout (4 columns on desktop, 2 on tablet)
- Color-coded icons and themes
- Trend indicators with arrows
- Bootstrap card styling
- Flexible value display (string or number)

**Props:**
- `title` (string): Card title/metric name
- `value` (string | number): Main metric value
- `change` (string): Percentage change indicator
- `changeType` ('positive' | 'negative'): Determines arrow direction and color
- `icon` (string): Emoji icon for the metric
- `color` (Bootstrap color): Theme color for the icon background

**Usage:**
```typescript
<StatsCard
  title="Total Products"
  value="1,234"
  change="+12.5%"
  changeType="positive"
  icon="📦"
  color="primary"
/>
```

#### RecentOrders.tsx
```typescript
interface Order {
  id: string;
  customer: string;
  product: string;
  amount: number;
  status: 'pending' | 'completed' | 'cancelled';
  date: string;
}

interface RecentOrdersProps {
  orders?: Order[];
}

const RecentOrders: React.FC<RecentOrdersProps> = ({ orders = [] }) => {
  const displayOrders = orders.length > 0 ? orders : sampleOrders;

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      completed: 'bg-success text-white',
      pending: 'bg-warning text-dark',
      cancelled: 'bg-danger text-white'
    };

    return (
      <span className={`badge ${statusClasses[status]} px-2 py-1`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  return (
    <div className="card h-100">
      <div className="card-header d-flex justify-content-between align-items-center">
        <h5 className="card-title mb-0">Recent Orders</h5>
        <button className="btn btn-sm btn-outline-primary">View All</button>
      </div>
      <div className="card-body p-0">
        <div className="table-responsive">
          <table className="table table-hover mb-0">
            <thead className="table-light">
              <tr>
                <th className="border-0 px-3 py-2">Order ID</th>
                <th className="border-0 px-3 py-2">Customer</th>
                <th className="border-0 px-3 py-2">Product</th>
                <th className="border-0 px-3 py-2">Amount</th>
                <th className="border-0 px-3 py-2">Status</th>
                <th className="border-0 px-3 py-2">Date</th>
              </tr>
            </thead>
            <tbody>
              {displayOrders.map((order) => (
                <tr key={order.id}>
                  <td className="px-3 py-2">
                    <span className="fw-medium">{order.id}</span>
                  </td>
                  <td className="px-3 py-2">{order.customer}</td>
                  <td className="px-3 py-2">{order.product}</td>
                  <td className="px-3 py-2">
                    <span className="fw-medium">${order.amount.toFixed(2)}</span>
                  </td>
                  <td className="px-3 py-2">{getStatusBadge(order.status)}</td>
                  <td className="px-3 py-2 text-muted">
                    {new Date(order.date).toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};
```

**Features:**
- Responsive table with horizontal scroll
- Status badges with color coding
- Sample data fallback
- Date formatting
- Hover effects on table rows
- Action button for viewing all orders

**Props:**
- `orders` (Order[] | optional): Array of order objects

#### LowStockAlert.tsx
```typescript
interface Product {
  id: string;
  name: string;
  sku: string;
  currentStock: number;
  minStock: number;
  category: string;
}

interface LowStockAlertProps {
  products?: Product[];
}

const LowStockAlert: React.FC<LowStockAlertProps> = ({ products = [] }) => {
  const displayProducts = products.length > 0 ? products : sampleProducts;

  const getStockLevel = (current: number, min: number) => {
    const percentage = (current / min) * 100;
    if (percentage <= 25) return { level: 'critical', color: 'danger' };
    if (percentage <= 50) return { level: 'low', color: 'warning' };
    return { level: 'normal', color: 'success' };
  };

  return (
    <div className="card h-100">
      <div className="card-header d-flex justify-content-between align-items-center">
        <h5 className="card-title mb-0">
          <span className="text-danger me-2">⚠️</span>
          Low Stock Alert
        </h5>
        <span className="badge bg-danger">{displayProducts.length}</span>
      </div>
      <div className="card-body">
        {displayProducts.length === 0 ? (
          <div className="text-center py-4">
            <div className="text-success mb-2" style={{fontSize: '48px'}}>✅</div>
            <p className="text-muted">All products are well stocked!</p>
          </div>
        ) : (
          <div className="list-group list-group-flush">
            {displayProducts.map((product) => {
              const stockInfo = getStockLevel(product.currentStock, product.minStock);
              return (
                <div key={product.id} className="list-group-item border-0 px-0 py-3">
                  <div className="d-flex justify-content-between align-items-start">
                    <div className="flex-grow-1">
                      <h6 className="mb-1 fw-medium">{product.name}</h6>
                      <p className="mb-1 text-muted small">SKU: {product.sku}</p>
                      <span className="badge bg-light text-dark">{product.category}</span>
                    </div>
                    <div className="text-end">
                      <div className="d-flex align-items-center mb-1">
                        <span className="me-2 small text-muted">Stock:</span>
                        <span className={`badge bg-${stockInfo.color}`}>
                          {product.currentStock}
                        </span>
                      </div>
                      <small className="text-muted">Min: {product.minStock}</small>
                    </div>
                  </div>

                  <div className="mt-2">
                    <div className="progress" style={{height: '4px'}}>
                      <div
                        className={`progress-bar bg-${stockInfo.color}`}
                        style={{
                          width: `${Math.min((product.currentStock / product.minStock) * 100, 100)}%`
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {displayProducts.length > 0 && (
          <div className="mt-3">
            <button className="btn btn-outline-primary btn-sm w-100">
              Restock All Items
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
```

**Features:**
- Stock level calculation and color coding
- Progress bars for visual stock representation
- Empty state with positive messaging
- Category badges
- Restock action button
- Responsive list layout

**Props:**
- `products` (Product[] | optional): Array of low stock products

---

## 🔄 STATE MANAGEMENT

### React State Patterns

#### 1. Local Component State
```typescript
// Simple component state for UI interactions
const [sidebarOpen, setSidebarOpen] = useState(false);
const [searchTerm, setSearchTerm] = useState('');
const [loading, setLoading] = useState(false);
```

#### 2. Form State Management
```typescript
// ProductForm.tsx
const [formData, setFormData] = useState<Product>({
  name: '',
  sku: '',
  category: '',
  price: 0,
  stock: 0,
  minStock: 10,
  description: '',
  status: 'active'
});

const [errors, setErrors] = useState<Partial<Product>>({});

const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  const { name, value, type } = e.target;

  setFormData(prev => ({
    ...prev,
    [name]: type === 'number' ? parseFloat(value) || 0 : value
  }));

  // Clear error when user starts typing
  if (errors[name as keyof Product]) {
    setErrors(prev => ({
      ...prev,
      [name]: undefined
    }));
  }
};
```

#### 3. Custom Hooks for State Logic
```typescript
// hooks/useProducts.ts
export const useProducts = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProducts = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await productService.getAll();
      setProducts(response.data);
    } catch (err) {
      setError('Failed to fetch products');
    } finally {
      setLoading(false);
    }
  };

  const addProduct = async (product: Omit<Product, 'id'>) => {
    setLoading(true);
    try {
      const response = await productService.create(product);
      setProducts(prev => [...prev, response.data]);
      return response.data;
    } catch (err) {
      setError('Failed to add product');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateProduct = async (id: string, updates: Partial<Product>) => {
    setLoading(true);
    try {
      const response = await productService.update(id, updates);
      setProducts(prev => prev.map(p => p.id === id ? response.data : p));
      return response.data;
    } catch (err) {
      setError('Failed to update product');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteProduct = async (id: string) => {
    setLoading(true);
    try {
      await productService.delete(id);
      setProducts(prev => prev.filter(p => p.id !== id));
    } catch (err) {
      setError('Failed to delete product');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  return {
    products,
    loading,
    error,
    fetchProducts,
    addProduct,
    updateProduct,
    deleteProduct
  };
};
```

### State Management Best Practices

#### 1. Single Source of Truth
- Keep related state in one place
- Use custom hooks for complex state logic
- Avoid prop drilling with context when needed

#### 2. Immutable Updates
```typescript
// ✅ Correct - Immutable update
setProducts(prev => [...prev, newProduct]);
setProduct(prev => ({ ...prev, name: newName }));

// ❌ Incorrect - Mutating state
products.push(newProduct);
product.name = newName;
```

#### 3. Error Handling
```typescript
const [error, setError] = useState<string | null>(null);

try {
  await apiCall();
  setError(null); // Clear previous errors
} catch (err) {
  setError(err instanceof Error ? err.message : 'An error occurred');
}
```

---

## 🎨 STYLING & UI FRAMEWORK

### Bootstrap 5 Integration

#### CSS Import Structure
```css
/* src/styles/index.css */
@import url('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');
@import './hando.css';

/* Simple Layout Styles */
.simple-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 260px;
  height: 100vh;
  background: #fff;
  border-right: 1px solid #e9ecef;
  z-index: 1000;
}

.simple-header {
  position: fixed;
  top: 0;
  left: 260px;
  right: 0;
  height: 70px;
  background: #fff;
  border-bottom: 1px solid #e9ecef;
  z-index: 999;
}

.simple-main {
  margin-left: 260px;
  margin-top: 70px;
  min-height: calc(100vh - 70px);
  background: #f8f9fa;
  padding: 20px;
}
```

#### Responsive Design
```css
/* Responsive Design */
@media (max-width: 992px) {
  .simple-sidebar {
    display: none;
  }

  .simple-header {
    left: 0;
  }

  .simple-main {
    margin-left: 0;
  }
}
```

### Component Styling Patterns

#### 1. Bootstrap Classes
```typescript
// Use Bootstrap utility classes for consistent styling
<div className="d-flex justify-content-between align-items-center">
  <h5 className="mb-0 fw-bold">Title</h5>
  <button className="btn btn-primary btn-sm">Action</button>
</div>
```

#### 2. Inline Styles for Specific Cases
```typescript
// Use inline styles for dynamic or component-specific styling
<div
  className="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
  style={{width: '48px', height: '48px', fontSize: '20px'}}
>
  {icon}
</div>
```

#### 3. CSS Custom Properties
```css
:root {
  --sidebar-width: 260px;
  --header-height: 70px;
  --primary-color: #0d6efd;
  --border-color: #e9ecef;
}

.simple-sidebar {
  width: var(--sidebar-width);
}

.simple-header {
  height: var(--header-height);
}
```

### Color Scheme
```css
/* Primary Colors */
--bs-primary: #0d6efd;
--bs-success: #198754;
--bs-info: #0dcaf0;
--bs-warning: #ffc107;
--bs-danger: #dc3545;

/* Neutral Colors */
--bs-light: #f8f9fa;
--bs-dark: #212529;
--bs-muted: #6c757d;

/* Background Colors */
--bg-body: #ffffff;
--bg-secondary: #f8f9fa;
--border-color: #e9ecef;
```

---

## 🔌 API INTEGRATION GUIDE

### Service Layer Architecture

#### Base API Service
```typescript
// services/api.ts
class ApiService {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('authToken');
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<{ data: T; status: number }> {
    const url = `${this.baseURL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return { data, status: response.status };
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  async get<T>(endpoint: string): Promise<{ data: T; status: number }> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data: any): Promise<{ data: T; status: number }> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async put<T>(endpoint: string, data: any): Promise<{ data: T; status: number }> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async delete<T>(endpoint: string): Promise<{ data: T; status: number }> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  setToken(token: string) {
    this.token = token;
    localStorage.setItem('authToken', token);
  }

  clearToken() {
    this.token = null;
    localStorage.removeItem('authToken');
  }
}

export const apiService = new ApiService(process.env.REACT_APP_API_URL || 'http://localhost:3001/api');
```

#### Product Service
```typescript
// services/productService.ts
import { apiService } from './api';
import { Product, CreateProductRequest, UpdateProductRequest } from '../types/product';

export class ProductService {
  async getAll(): Promise<{ data: Product[]; status: number }> {
    return apiService.get<Product[]>('/products');
  }

  async getById(id: string): Promise<{ data: Product; status: number }> {
    return apiService.get<Product>(`/products/${id}`);
  }

  async create(product: CreateProductRequest): Promise<{ data: Product; status: number }> {
    return apiService.post<Product>('/products', product);
  }

  async update(id: string, updates: UpdateProductRequest): Promise<{ data: Product; status: number }> {
    return apiService.put<Product>(`/products/${id}`, updates);
  }

  async delete(id: string): Promise<{ data: void; status: number }> {
    return apiService.delete<void>(`/products/${id}`);
  }

  async search(query: string): Promise<{ data: Product[]; status: number }> {
    return apiService.get<Product[]>(`/products/search?q=${encodeURIComponent(query)}`);
  }

  async getLowStock(threshold?: number): Promise<{ data: Product[]; status: number }> {
    const params = threshold ? `?threshold=${threshold}` : '';
    return apiService.get<Product[]>(`/products/low-stock${params}`);
  }
}

export const productService = new ProductService();
```

### API Endpoints Specification

#### Products API
```
GET    /api/products              - Get all products
GET    /api/products/:id          - Get product by ID
POST   /api/products              - Create new product
PUT    /api/products/:id          - Update product
DELETE /api/products/:id          - Delete product
GET    /api/products/search       - Search products
GET    /api/products/low-stock    - Get low stock products
```

#### Request/Response Examples
```typescript
// GET /api/products
Response: {
  "data": [
    {
      "id": "prod_123",
      "name": "Wireless Headphones",
      "sku": "WH-001",
      "category": "Electronics",
      "price": 99.99,
      "stock": 45,
      "minStock": 10,
      "description": "High-quality wireless headphones",
      "status": "active",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ],
  "meta": {
    "total": 1,
    "page": 1,
    "limit": 10
  }
}

// POST /api/products
Request: {
  "name": "Wireless Mouse",
  "sku": "WM-002",
  "category": "Electronics",
  "price": 29.99,
  "stock": 100,
  "minStock": 15,
  "description": "Ergonomic wireless mouse",
  "status": "active"
}

Response: {
  "data": {
    "id": "prod_124",
    "name": "Wireless Mouse",
    "sku": "WM-002",
    "category": "Electronics",
    "price": 29.99,
    "stock": 100,
    "minStock": 15,
    "description": "Ergonomic wireless mouse",
    "status": "active",
    "createdAt": "2024-01-15T11:00:00Z",
    "updatedAt": "2024-01-15T11:00:00Z"
  }
}
```

### Error Handling
```typescript
// types/api.ts
export interface ApiError {
  message: string;
  code: string;
  details?: any;
}

export interface ApiResponse<T> {
  data?: T;
  error?: ApiError;
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
  };
}

// Error handling in components
const handleApiError = (error: any) => {
  if (error.response?.data?.error) {
    const apiError = error.response.data.error;
    setError(`${apiError.message} (${apiError.code})`);
  } else {
    setError('An unexpected error occurred');
  }
};
```

---

## 🗄️ DATABASE SCHEMA

### Product Table
```sql
CREATE TABLE products (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
  name VARCHAR(255) NOT NULL,
  sku VARCHAR(100) UNIQUE NOT NULL,
  category VARCHAR(100) NOT NULL,
  price DECIMAL(10, 2) NOT NULL,
  stock INT NOT NULL DEFAULT 0,
  min_stock INT NOT NULL DEFAULT 10,
  description TEXT,
  status ENUM('active', 'inactive') DEFAULT 'active',
  image_url VARCHAR(500),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_category (category),
  INDEX idx_sku (sku),
  INDEX idx_status (status),
  INDEX idx_stock (stock),
  INDEX idx_created_at (created_at)
);
```

### Orders Table
```sql
CREATE TABLE orders (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
  order_number VARCHAR(50) UNIQUE NOT NULL,
  customer_id VARCHAR(36),
  customer_name VARCHAR(255) NOT NULL,
  customer_email VARCHAR(255),
  total_amount DECIMAL(10, 2) NOT NULL,
  status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending',
  order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_customer_id (customer_id),
  INDEX idx_status (status),
  INDEX idx_order_date (order_date),
  INDEX idx_order_number (order_number)
);
```

### Order Items Table
```sql
CREATE TABLE order_items (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
  order_id VARCHAR(36) NOT NULL,
  product_id VARCHAR(36) NOT NULL,
  product_name VARCHAR(255) NOT NULL,
  product_sku VARCHAR(100) NOT NULL,
  quantity INT NOT NULL,
  unit_price DECIMAL(10, 2) NOT NULL,
  total_price DECIMAL(10, 2) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT,

  INDEX idx_order_id (order_id),
  INDEX idx_product_id (product_id)
);
```

### Categories Table
```sql
CREATE TABLE categories (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
  name VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  parent_id VARCHAR(36) NULL,
  status ENUM('active', 'inactive') DEFAULT 'active',
  sort_order INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,

  INDEX idx_name (name),
  INDEX idx_parent_id (parent_id),
  INDEX idx_status (status)
);
```

### Users Table
```sql
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  role ENUM('admin', 'manager', 'staff') DEFAULT 'staff',
  status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
  last_login TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_username (username),
  INDEX idx_email (email),
  INDEX idx_role (role),
  INDEX idx_status (status)
);
```

### Stock Movements Table
```sql
CREATE TABLE stock_movements (
  id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
  product_id VARCHAR(36) NOT NULL,
  movement_type ENUM('in', 'out', 'adjustment') NOT NULL,
  quantity INT NOT NULL,
  previous_stock INT NOT NULL,
  new_stock INT NOT NULL,
  reference_type ENUM('order', 'purchase', 'adjustment', 'return') NOT NULL,
  reference_id VARCHAR(36),
  notes TEXT,
  user_id VARCHAR(36),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,

  INDEX idx_product_id (product_id),
  INDEX idx_movement_type (movement_type),
  INDEX idx_reference_type (reference_type),
  INDEX idx_created_at (created_at)
);
```

---

## 🚀 DEVELOPMENT SETUP

### Prerequisites
```bash
# Required software
Node.js 18.x or higher
npm 9.x or higher
Git
VS Code (recommended)

# Optional
Docker (for containerization)
MySQL/PostgreSQL (for database)
```

### Installation Steps
```bash
# 1. Clone the repository
git clone https://github.com/your-org/inventory-management-system.git
cd inventory-management-system

# 2. Install frontend dependencies
cd frontend
npm install

# 3. Create environment file
cp .env.example .env

# 4. Configure environment variables
# Edit .env file with your settings
REACT_APP_API_URL=http://localhost:3001/api
REACT_APP_APP_NAME=Inventory Management System

# 5. Start development server
npm run dev

# 6. Open browser
# Navigate to http://localhost:5173
```

### Environment Configuration
```bash
# .env file
REACT_APP_API_URL=http://localhost:3001/api
REACT_APP_APP_NAME=Inventory Management System
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development

# Optional features
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_DEBUG_MODE=true
```

### Development Scripts
```json
{
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint . --ext ts,tsx --fix",
    "type-check": "tsc --noEmit",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage"
  }
}
```

### Code Quality Tools
```bash
# ESLint configuration
npm run lint          # Check for linting errors
npm run lint:fix       # Fix auto-fixable linting errors

# TypeScript type checking
npm run type-check     # Check TypeScript types

# Testing
npm run test           # Run unit tests
npm run test:ui        # Run tests with UI
npm run test:coverage  # Run tests with coverage report
```

---

## 🎨 CUSTOMIZATION GUIDE

### Theme Customization

#### 1. Color Scheme
```css
/* src/styles/theme.css */
:root {
  /* Primary brand colors */
  --brand-primary: #your-primary-color;
  --brand-secondary: #your-secondary-color;
  --brand-accent: #your-accent-color;

  /* Override Bootstrap variables */
  --bs-primary: var(--brand-primary);
  --bs-secondary: var(--brand-secondary);

  /* Custom component colors */
  --sidebar-bg: #ffffff;
  --header-bg: #ffffff;
  --main-bg: #f8f9fa;
}
```

#### 2. Logo and Branding
```typescript
// components/Layout/SimpleSidebar.tsx
<div className="text-center py-3 border-bottom">
  <Link to="/dashboard" className="text-decoration-none">
    {/* Replace with your logo */}
    <img src="/path/to/your/logo.png" alt="Your Brand" height="40" />
    {/* Or custom text */}
    <h4 className="text-primary fw-bold mb-0">Your Brand</h4>
  </Link>
</div>
```

#### 3. Navigation Menu
```typescript
// Customize menu items in SimpleSidebar.tsx
const menuItems = [
  { text: 'Dashboard', path: '/dashboard', icon: '📊' },
  { text: 'Products', path: '/products', icon: '📦' },
  { text: 'Orders', path: '/orders', icon: '📋' },
  { text: 'Customers', path: '/customers', icon: '👥' },
  { text: 'Reports', path: '/reports', icon: '📈' },
  { text: 'Settings', path: '/settings', icon: '⚙️' }
];
```

### Adding New Components

#### 1. Create Component Structure
```bash
# Create new component directory
mkdir src/components/NewFeature

# Create component files
touch src/components/NewFeature/NewFeatureTable.tsx
touch src/components/NewFeature/NewFeatureForm.tsx
touch src/components/NewFeature/index.ts
```

#### 2. Component Template
```typescript
// src/components/NewFeature/NewFeatureTable.tsx
import React, { useState } from 'react';

interface NewFeatureItem {
  id: string;
  name: string;
  // Add your properties
}

interface NewFeatureTableProps {
  items?: NewFeatureItem[];
  onEdit?: (item: NewFeatureItem) => void;
  onDelete?: (id: string) => void;
}

const NewFeatureTable: React.FC<NewFeatureTableProps> = ({
  items = [],
  onEdit,
  onDelete
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  return (
    <div className="card">
      <div className="card-header">
        <div className="row align-items-center">
          <div className="col-md-6">
            <h5 className="card-title mb-0">New Feature</h5>
          </div>
          <div className="col-md-6">
            <div className="d-flex justify-content-end">
              <input
                type="text"
                className="form-control me-2"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <button className="btn btn-primary btn-sm">
                ➕ Add New
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="card-body">
        {/* Your table content */}
      </div>
    </div>
  );
};

export default NewFeatureTable;
```

#### 3. Add New Page
```typescript
// src/pages/NewFeature.tsx
import React, { useState } from 'react';
import NewFeatureTable from '../components/NewFeature/NewFeatureTable';

const NewFeature: React.FC = () => {
  const [items, setItems] = useState([]);

  return (
    <div className="container-fluid">
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-sm-flex align-items-center justify-content-between">
            <div>
              <h4 className="mb-1 fw-bold">New Feature</h4>
              <p className="text-muted mb-0">Manage your new feature items</p>
            </div>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-12">
          <NewFeatureTable items={items} />
        </div>
      </div>
    </div>
  );
};

export default NewFeature;
```

#### 4. Update Routing
```typescript
// src/App.tsx
import NewFeature from './pages/NewFeature';

// Add to routes
<Route path="/new-feature" element={<NewFeature />} />
```

### Custom Hooks

#### 1. Create Custom Hook
```typescript
// src/hooks/useNewFeature.ts
import { useState, useEffect } from 'react';

export const useNewFeature = () => {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchItems = async () => {
    setLoading(true);
    try {
      // API call logic
      const response = await fetch('/api/new-feature');
      const data = await response.json();
      setItems(data);
    } catch (err) {
      setError('Failed to fetch items');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchItems();
  }, []);

  return { items, loading, error, fetchItems };
};
```

---

## 🔒 SECURITY CONSIDERATIONS

### Authentication & Authorization
```typescript
// Implement JWT token validation
const validateToken = (token: string): boolean => {
  try {
    const decoded = jwt.decode(token);
    return decoded && decoded.exp > Date.now() / 1000;
  } catch {
    return false;
  }
};

// Role-based access control
const hasPermission = (userRole: string, requiredRole: string): boolean => {
  const roleHierarchy = {
    'admin': 3,
    'manager': 2,
    'staff': 1
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
};
```

### Input Validation
```typescript
// Validate product data
const validateProduct = (product: Product): string[] => {
  const errors: string[] = [];

  if (!product.name || product.name.trim().length < 2) {
    errors.push('Product name must be at least 2 characters');
  }

  if (!product.sku || !/^[A-Z0-9-]+$/.test(product.sku)) {
    errors.push('SKU must contain only uppercase letters, numbers, and hyphens');
  }

  if (product.price <= 0) {
    errors.push('Price must be greater than 0');
  }

  return errors;
};
```

### Data Sanitization
```typescript
// Sanitize user input
const sanitizeInput = (input: string): string => {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 255); // Limit length
};
```

---

## 🚀 FUTURE ENHANCEMENTS

### Phase 2: Advanced Features
- **Multi-warehouse Management**: Support for multiple warehouse locations
- **Barcode Scanning**: Mobile barcode scanning for inventory updates
- **Advanced Reporting**: Custom report builder with charts and exports
- **Automated Reordering**: Automatic purchase order generation
- **Supplier Management**: Complete supplier relationship management

### Phase 3: E-commerce Integration
- **WooCommerce Sync**: Real-time inventory synchronization
- **Magento Integration**: Product and stock level sync
- **Shopify Connection**: Automated inventory updates
- **Multi-channel Management**: Centralized inventory across platforms

### Phase 4: Enterprise Features
- **Tally ERP Integration**: Accounting system synchronization
- **API Gateway**: RESTful API for third-party integrations
- **Mobile Apps**: React Native mobile applications
- **POS System**: Point of sale integration
- **Subscription Licensing**: Multi-tenant SaaS architecture

### Technology Roadmap
```
Current: React + TypeScript + Bootstrap
Phase 2: + Node.js + Express + MySQL
Phase 3: + Redis + WebSockets + Docker
Phase 4: + Microservices + Kubernetes + GraphQL
```

### Performance Optimizations
- **Code Splitting**: Lazy loading of components
- **Caching**: Redis caching for frequently accessed data
- **CDN Integration**: Static asset delivery optimization
- **Database Optimization**: Query optimization and indexing
- **Real-time Updates**: WebSocket implementation for live data

---

## 📞 SUPPORT & CONTRIBUTION

### Getting Help
- **Documentation**: Refer to this comprehensive guide
- **Issues**: Report bugs on GitHub Issues
- **Discussions**: Join community discussions
- **Email**: Contact support team

### Contributing
1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### Code Standards
- Follow TypeScript best practices
- Use ESLint and Prettier for code formatting
- Write unit tests for new features
- Update documentation for changes
- Follow semantic versioning

---

## 📄 LICENSE

This project is licensed under the MIT License - see the LICENSE file for details.

---

**© 2024 Inventory Management System. All rights reserved.**

*This documentation provides a complete guide for understanding, customizing, and extending the inventory management system. For the latest updates and additional resources, visit the project repository.*