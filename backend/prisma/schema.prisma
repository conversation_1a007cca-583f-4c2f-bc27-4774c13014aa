// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String   @unique
  firstName   String
  lastName    String
  password    String
  role        UserRole @default(USER)
  isActive    Boolean  @default(true)
  lastLogin   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  createdProducts Product[]
  createdOrders   Order[]
  sessions        UserSession[]

  @@map("users")
}

model UserSession {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

enum UserRole {
  SUPER_ADMIN
  ADMIN
  MANAGER
  USER
  VENDOR
  CUSTOMER
}

// Product Management
model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  parentId    String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]

  @@map("categories")
}

model Brand {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  logo        String?
  website     String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  products Product[]

  @@map("brands")
}

model Product {
  id          String      @id @default(cuid())
  sku         String      @unique
  name        String
  description String?
  categoryId  String
  brandId     String?
  type        ProductType @default(SIMPLE)
  status      ProductStatus @default(ACTIVE)
  
  // Pricing
  costPrice     Decimal @default(0)
  sellingPrice  Decimal @default(0)
  mrp           Decimal @default(0)
  
  // Physical attributes
  weight        Decimal?
  dimensions    String?
  
  // SEO and metadata
  slug          String?  @unique
  metaTitle     String?
  metaDescription String?
  
  // Tracking
  trackInventory Boolean @default(true)
  lowStockAlert  Int?    @default(10)
  
  // Timestamps
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  createdById   String

  // Relations
  category      Category @relation(fields: [categoryId], references: [id])
  brand         Brand?   @relation(fields: [brandId], references: [id])
  createdBy     User     @relation(fields: [createdById], references: [id])
  
  variants      ProductVariant[]
  inventory     InventoryItem[]
  orderItems    OrderItem[]
  images        ProductImage[]
  attributes    ProductAttribute[]

  @@map("products")
}

model ProductVariant {
  id        String   @id @default(cuid())
  productId String
  sku       String   @unique
  name      String
  price     Decimal
  
  // Variant attributes (size, color, etc.)
  attributes Json?
  
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  inventory InventoryItem[]
  orderItems OrderItem[]

  @@map("product_variants")
}

model ProductImage {
  id        String   @id @default(cuid())
  productId String
  url       String
  altText   String?
  sortOrder Int      @default(0)
  isPrimary Boolean  @default(false)
  createdAt DateTime @default(now())

  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductAttribute {
  id        String   @id @default(cuid())
  productId String
  name      String
  value     String
  createdAt DateTime @default(now())

  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_attributes")
}

enum ProductType {
  SIMPLE
  VARIABLE
  GROUPED
  EXTERNAL
}

enum ProductStatus {
  ACTIVE
  INACTIVE
  DRAFT
  ARCHIVED
}

// Inventory Management
model Warehouse {
  id        String   @id @default(cuid())
  name      String   @unique
  code      String   @unique
  address   String
  city      String
  state     String
  country   String
  zipCode   String
  phone     String?
  email     String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  inventory InventoryItem[]
  orders    Order[]

  @@map("warehouses")
}

model InventoryItem {
  id              String   @id @default(cuid())
  productId       String
  variantId       String?
  warehouseId     String
  quantity        Int      @default(0)
  reservedQty     Int      @default(0)
  availableQty    Int      @default(0)
  reorderLevel    Int      @default(0)
  maxStockLevel   Int?
  
  // Batch tracking
  batchNumber     String?
  expiryDate      DateTime?
  manufacturingDate DateTime?
  
  lastUpdated     DateTime @default(now())
  createdAt       DateTime @default(now())

  product   Product         @relation(fields: [productId], references: [id])
  variant   ProductVariant? @relation(fields: [variantId], references: [id])
  warehouse Warehouse       @relation(fields: [warehouseId], references: [id])
  
  movements InventoryMovement[]

  @@unique([productId, variantId, warehouseId, batchNumber])
  @@map("inventory_items")
}

model InventoryMovement {
  id            String        @id @default(cuid())
  inventoryId   String
  type          MovementType
  quantity      Int
  reference     String?       // Order ID, Transfer ID, etc.
  reason        String?
  notes         String?
  createdAt     DateTime      @default(now())

  inventory InventoryItem @relation(fields: [inventoryId], references: [id])

  @@map("inventory_movements")
}

enum MovementType {
  IN
  OUT
  TRANSFER
  ADJUSTMENT
  RETURN
  DAMAGE
  EXPIRED
}

// Order Management
model Customer {
  id        String   @id @default(cuid())
  email     String   @unique
  firstName String
  lastName  String
  phone     String?
  company   String?
  
  // Default billing address
  billingAddress Json?
  
  // Default shipping address  
  shippingAddress Json?
  
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  orders Order[]

  @@map("customers")
}

model Order {
  id              String      @id @default(cuid())
  orderNumber     String      @unique
  customerId      String
  warehouseId     String
  status          OrderStatus @default(PENDING)
  
  // Pricing
  subtotal        Decimal     @default(0)
  taxAmount       Decimal     @default(0)
  shippingAmount  Decimal     @default(0)
  discountAmount  Decimal     @default(0)
  totalAmount     Decimal     @default(0)
  
  // Addresses
  billingAddress  Json
  shippingAddress Json
  
  // Tracking
  trackingNumber  String?
  shippedAt       DateTime?
  deliveredAt     DateTime?
  
  // Metadata
  notes           String?
  internalNotes   String?
  
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt
  createdById     String

  customer  Customer  @relation(fields: [customerId], references: [id])
  warehouse Warehouse @relation(fields: [warehouseId], references: [id])
  createdBy User      @relation(fields: [createdById], references: [id])
  
  items     OrderItem[]
  payments  Payment[]

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  orderId   String
  productId String
  variantId String?
  quantity  Int
  unitPrice Decimal
  totalPrice Decimal
  
  product Product         @relation(fields: [productId], references: [id])
  variant ProductVariant? @relation(fields: [variantId], references: [id])
  order   Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("order_items")
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
  RETURNED
}

// Payment Management
model Payment {
  id            String        @id @default(cuid())
  orderId       String
  amount        Decimal
  method        PaymentMethod
  status        PaymentStatus @default(PENDING)
  transactionId String?
  gatewayResponse Json?
  paidAt        DateTime?
  createdAt     DateTime      @default(now())

  order Order @relation(fields: [orderId], references: [id])

  @@map("payments")
}

enum PaymentMethod {
  CASH
  CARD
  UPI
  NET_BANKING
  WALLET
  COD
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  CANCELLED
}

// Vendor Management
model Vendor {
  id              String   @id @default(cuid())
  name            String
  email           String   @unique
  phone           String
  company         String?
  gstNumber       String?
  panNumber       String?
  
  // Address
  address         Json
  
  // Banking details
  bankDetails     Json?
  
  // Terms
  paymentTerms    String?
  creditLimit     Decimal?
  
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  purchaseOrders PurchaseOrder[]

  @@map("vendors")
}

model PurchaseOrder {
  id              String              @id @default(cuid())
  poNumber        String              @unique
  vendorId        String
  status          PurchaseOrderStatus @default(DRAFT)
  
  subtotal        Decimal             @default(0)
  taxAmount       Decimal             @default(0)
  totalAmount     Decimal             @default(0)
  
  expectedDate    DateTime?
  receivedDate    DateTime?
  
  notes           String?
  
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt

  vendor Vendor              @relation(fields: [vendorId], references: [id])
  items  PurchaseOrderItem[]

  @@map("purchase_orders")
}

model PurchaseOrderItem {
  id              String  @id @default(cuid())
  purchaseOrderId String
  productId       String
  variantId       String?
  quantity        Int
  unitPrice       Decimal
  totalPrice      Decimal
  receivedQty     Int     @default(0)
  
  purchaseOrder PurchaseOrder   @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)

  @@map("purchase_order_items")
}

enum PurchaseOrderStatus {
  DRAFT
  SENT
  CONFIRMED
  PARTIALLY_RECEIVED
  RECEIVED
  CANCELLED
}

// Client Management (SAAS)
model Client {
  id            String      @id @default(cuid())
  companyName   String
  contactName   String
  email         String      @unique
  phone         String?
  domain        String      @unique
  plan          ClientPlan  @default(PROFESSIONAL)
  billingCycle  BillingCycle @default(MONTHLY)
  amount        Decimal
  currency      String      @default("USD")
  maxUsers      Int         @default(10)
  maxStorage    Int         @default(25) // in GB
  usedUsers     Int         @default(0)
  usedStorage   Int         @default(0)
  status        ClientStatus @default(TRIAL)
  licenseKey    String      @unique
  features      Json        // Array of enabled features
  expiresAt     DateTime
  lastAccess    DateTime?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // Relations
  users         ClientUser[]
  warehouses    ClientWarehouse[]
  posTerminals  POSTerminal[]
  transactions  PaymentTransaction[]
  settings      ClientSettings?

  @@map("clients")
}

model ClientUser {
  id          String     @id @default(cuid())
  clientId    String
  username    String
  email       String
  firstName   String
  lastName    String
  role        ClientRole @default(VIEWER)
  permissions Json       // Array of permissions
  isActive    Boolean    @default(true)
  lastLogin   DateTime?
  avatar      String?
  phone       String?
  department  String?
  position    String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  client Client @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@unique([clientId, email])
  @@map("client_users")
}

model ClientWarehouse {
  id        String          @id @default(cuid())
  clientId  String
  name      String
  code      String
  address   String
  phone     String?
  email     String?
  managerId String?
  isActive  Boolean         @default(true)
  type      WarehouseType   @default(MAIN)
  capacity  Json?           // { totalSpace, usedSpace, unit }
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt

  client Client              @relation(fields: [clientId], references: [id], onDelete: Cascade)
  zones  ClientWarehouseZone[]
  posTerminals POSTerminal[]

  @@unique([clientId, code])
  @@map("client_warehouses")
}

model ClientWarehouseZone {
  id          String        @id @default(cuid())
  warehouseId String
  name        String
  code        String
  type        ZoneType      @default(STORAGE)
  capacity    Int?
  isActive    Boolean       @default(true)
  createdAt   DateTime      @default(now())

  warehouse ClientWarehouse @relation(fields: [warehouseId], references: [id], onDelete: Cascade)

  @@unique([warehouseId, code])
  @@map("client_warehouse_zones")
}

model POSTerminal {
  id           String      @id @default(cuid())
  clientId     String
  warehouseId  String?
  name         String
  code         String
  location     String
  isActive     Boolean     @default(true)
  status       TerminalStatus @default(OFFLINE)
  cashierId    String?
  lastTransaction DateTime?
  dailySales   Json?       // { amount, transactions, date }
  hardware     Json        // Hardware configuration
  settings     Json        // Terminal settings
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  client    Client           @relation(fields: [clientId], references: [id], onDelete: Cascade)
  warehouse ClientWarehouse? @relation(fields: [warehouseId], references: [id])

  @@unique([clientId, code])
  @@map("pos_terminals")
}

model ClientSettings {
  id            String   @id @default(cuid())
  clientId      String   @unique
  companyInfo   Json     // Company details
  branding      Json     // Branding settings
  features      Json     // Feature toggles
  integrations  Json     // Integration settings
  notifications Json     // Notification preferences
  security      Json     // Security settings
  limits        Json     // Usage limits
  updatedAt     DateTime @updatedAt

  client Client @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@map("client_settings")
}

enum ClientPlan {
  STARTER
  PROFESSIONAL
  ENTERPRISE
  CUSTOM
}

enum BillingCycle {
  MONTHLY
  YEARLY
}

enum ClientStatus {
  TRIAL
  ACTIVE
  SUSPENDED
  EXPIRED
}

enum ClientRole {
  CLIENT_ADMIN
  WAREHOUSE_MANAGER
  WAREHOUSE_STAFF
  POS_MANAGER
  POS_CASHIER
  INVENTORY_MANAGER
  SALES_MANAGER
  ACCOUNTANT
  VIEWER
}

enum WarehouseType {
  MAIN
  BRANCH
  STORAGE
  DISTRIBUTION
}

enum ZoneType {
  RECEIVING
  STORAGE
  PICKING
  SHIPPING
  RETURNS
}

enum TerminalStatus {
  ONLINE
  OFFLINE
  MAINTENANCE
}

// Payment Gateway Management
model PaymentGateway {
  id                  String              @id @default(cuid())
  name                String              @unique
  type                PaymentGatewayType
  enabled             Boolean             @default(false)
  isDefault           Boolean             @default(false)
  configuration       Json                // Gateway-specific config
  supportedCurrencies Json                // Array of currency codes
  supportedCountries  Json                // Array of country codes
  features            Json                // Array of features
  status              GatewayStatus       @default(INACTIVE)
  lastTested          DateTime?
  createdAt           DateTime            @default(now())
  updatedAt           DateTime            @updatedAt

  // Relations
  transactions PaymentTransaction[]
  webhooks     PaymentWebhook[]

  @@map("payment_gateways")
}

model PaymentTransaction {
  id                    String            @id @default(cuid())
  gatewayId             String
  gatewayTransactionId  String?
  clientId              String?
  invoiceId             String?
  amount                Decimal
  currency              String
  status                TransactionStatus @default(PENDING)
  paymentMethod         String
  description           String?
  metadata              Json?
  gatewayResponse       Json?
  failureReason         String?
  processedAt           DateTime?
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt

  gateway PaymentGateway @relation(fields: [gatewayId], references: [id])
  client  Client?        @relation(fields: [clientId], references: [id])

  @@map("payment_transactions")
}

model PaymentWebhook {
  id            String    @id @default(cuid())
  gatewayId     String
  url           String
  events        Json      // Array of event types
  secret        String?
  enabled       Boolean   @default(true)
  lastTriggered DateTime?
  status        WebhookStatus @default(ACTIVE)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  gateway PaymentGateway @relation(fields: [gatewayId], references: [id], onDelete: Cascade)

  @@map("payment_webhooks")
}

model PaymentSettings {
  id                      String   @id @default(cuid())
  defaultGateway          String
  defaultCurrency         String   @default("USD")
  allowedCurrencies       Json     // Array of currencies
  minimumAmount           Decimal  @default(0)
  maximumAmount           Decimal  @default(100000)
  autoCapture             Boolean  @default(true)
  requireBillingAddress   Boolean  @default(true)
  requireShippingAddress  Boolean  @default(false)
  enableSaveCard          Boolean  @default(true)
  enableRecurringPayments Boolean  @default(true)
  paymentTimeout          Int      @default(30) // minutes
  retryFailedPayments     Boolean  @default(true)
  maxRetryAttempts        Int      @default(3)
  notificationSettings    Json     // Notification preferences
  updatedAt               DateTime @updatedAt

  @@map("payment_settings")
}

enum PaymentGatewayType {
  STRIPE
  PAYPAL
  RAZORPAY
  SQUARE
  BRAINTREE
  AUTHORIZE_NET
  PAYU
  MOLLIE
  BANK_TRANSFER
  CASH_ON_DELIVERY
}

enum GatewayStatus {
  ACTIVE
  INACTIVE
  TESTING
  ERROR
}

enum TransactionStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
  PARTIALLY_REFUNDED
}

enum WebhookStatus {
  ACTIVE
  INACTIVE
  FAILED
}
