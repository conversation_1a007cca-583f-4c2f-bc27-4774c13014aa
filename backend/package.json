{"name": "inventory-backend", "version": "1.0.0", "description": "Backend API for Inventory Management System", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "ts-node prisma/seed.ts", "db:reset": "prisma migrate reset", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^5.7.1", "@types/pg": "^8.15.4", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "bull": "^4.12.2", "compression": "^1.7.4", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "fast-xml-parser": "^4.3.2", "helmet": "^7.1.0", "ioredis": "^5.3.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "pdf-lib": "^1.17.1", "pg": "^8.16.0", "prisma": "^5.7.1", "qrcode": "^1.5.3", "redis": "^4.6.10", "sharp": "^0.33.0", "socket.io": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "tsconfig-paths": "^4.2.0", "uuid": "^9.0.1", "winston": "^3.11.0", "xlsx": "^0.18.5", "zod": "^3.25.47"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@types/qrcode": "^1.5.5", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["inventory", "management", "api", "nodejs", "express", "typescript", "postgresql", "redis"], "author": "Your Company", "license": "MIT"}