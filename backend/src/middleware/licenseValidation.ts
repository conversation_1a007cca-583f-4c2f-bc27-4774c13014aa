import { Request, Response, NextFunction } from 'express';

interface LicenseInfo {
  id: string;
  companyName: string;
  domain: string;
  plan: string;
  status: 'active' | 'suspended' | 'expired' | 'trial';
  licenseKey: string;
  expiresAt: string;
  features: string[];
  maxUsers: number;
  maxStorage: number;
}

// Mock license database - in production, this would be a real database
const licenses: LicenseInfo[] = [
  {
    id: '1',
    companyName: 'TechCorp Solutions',
    domain: 'techcorp.com',
    plan: 'professional',
    status: 'active',
    licenseKey: 'TC-PRO-2024-001',
    expiresAt: '2025-01-15',
    features: ['inventory', 'reports', 'tally-sync', 'api-access'],
    maxUsers: 10,
    maxStorage: 25
  },
  {
    id: '2',
    companyName: 'StartupXYZ',
    domain: 'startupxyz.com',
    plan: 'starter',
    status: 'active',
    licenseKey: 'SX-STR-2024-002',
    expiresAt: '2024-12-20',
    features: ['inventory', 'basic-reports'],
    maxUsers: 3,
    maxStorage: 5
  },
  {
    id: '3',
    companyName: 'Enterprise Corp',
    domain: 'enterprise.com',
    plan: 'enterprise',
    status: 'active',
    licenseKey: 'EC-ENT-2024-003',
    expiresAt: '2025-02-10',
    features: ['inventory', 'advanced-reports', 'tally-sync', 'api-access', 'multi-location', 'custom-fields'],
    maxUsers: 50,
    maxStorage: 100
  },
  {
    id: '4',
    companyName: 'Trial Company',
    domain: 'trialcompany.com',
    plan: 'professional',
    status: 'trial',
    licenseKey: 'TC-TRL-2024-004',
    expiresAt: '2024-12-20',
    features: ['inventory', 'reports', 'tally-sync'],
    maxUsers: 10,
    maxStorage: 25
  }
];

/**
 * Extract domain from request
 */
function extractDomain(req: Request): string {
  // Try to get domain from various sources
  const host = req.get('host') || req.get('x-forwarded-host') || '';
  const origin = req.get('origin') || '';
  const referer = req.get('referer') || '';

  // Extract domain from host header
  if (host) {
    return host.split(':')[0]; // Remove port if present
  }

  // Extract domain from origin header
  if (origin) {
    try {
      const url = new URL(origin);
      return url.hostname;
    } catch (e) {
      // Invalid URL
    }
  }

  // Extract domain from referer header
  if (referer) {
    try {
      const url = new URL(referer);
      return url.hostname;
    } catch (e) {
      // Invalid URL
    }
  }

  return '';
}

/**
 * Check if domain matches license domain
 */
function isDomainValid(requestDomain: string, licenseDomain: string, validationType: string = 'strict'): boolean {
  if (!requestDomain || !licenseDomain) {
    return false;
  }

  // Remove www prefix for comparison
  const normalizeRequestDomain = requestDomain.replace(/^www\./, '');
  const normalizeLicenseDomain = licenseDomain.replace(/^www\./, '');

  switch (validationType) {
    case 'strict':
      return normalizeRequestDomain === normalizeLicenseDomain;
    
    case 'subdomain':
      return normalizeRequestDomain === normalizeLicenseDomain || 
             normalizeRequestDomain.endsWith('.' + normalizeLicenseDomain);
    
    case 'wildcard':
      // Support wildcard domains like *.example.com
      if (normalizeLicenseDomain.startsWith('*.')) {
        const baseDomain = normalizeLicenseDomain.substring(2);
        return normalizeRequestDomain === baseDomain || 
               normalizeRequestDomain.endsWith('.' + baseDomain);
      }
      return normalizeRequestDomain === normalizeLicenseDomain;
    
    default:
      return normalizeRequestDomain === normalizeLicenseDomain;
  }
}

/**
 * Check if license is expired
 */
function isLicenseExpired(expiresAt: string): boolean {
  const expiryDate = new Date(expiresAt);
  const now = new Date();
  return now > expiryDate;
}

/**
 * Check if feature is allowed for the license
 */
function isFeatureAllowed(license: LicenseInfo, feature: string): boolean {
  return license.features.includes(feature);
}

/**
 * Middleware to validate license and domain
 */
export const validateLicense = (requiredFeature?: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Skip validation for localhost and development
      const requestDomain = extractDomain(req);
      if (requestDomain === 'localhost' || requestDomain === '127.0.0.1' || requestDomain.includes('localhost')) {
        // In development, allow all requests
        if (process.env.NODE_ENV === 'development') {
          return next();
        }
      }

      // Get license key from headers or query params
      const licenseKey = req.get('x-license-key') || req.query.license_key as string;
      
      if (!licenseKey) {
        return res.status(401).json({
          success: false,
          error: 'LICENSE_KEY_MISSING',
          message: 'License key is required'
        });
      }

      // Find license by key
      const license = licenses.find(l => l.licenseKey === licenseKey);
      
      if (!license) {
        return res.status(401).json({
          success: false,
          error: 'INVALID_LICENSE',
          message: 'Invalid license key'
        });
      }

      // Check if license is expired
      if (isLicenseExpired(license.expiresAt)) {
        return res.status(403).json({
          success: false,
          error: 'LICENSE_EXPIRED',
          message: 'License has expired',
          expiresAt: license.expiresAt
        });
      }

      // Check license status
      if (license.status === 'suspended') {
        return res.status(403).json({
          success: false,
          error: 'LICENSE_SUSPENDED',
          message: 'License has been suspended'
        });
      }

      if (license.status === 'expired') {
        return res.status(403).json({
          success: false,
          error: 'LICENSE_EXPIRED',
          message: 'License has expired'
        });
      }

      // Validate domain
      if (!isDomainValid(requestDomain, license.domain, 'subdomain')) {
        return res.status(403).json({
          success: false,
          error: 'DOMAIN_MISMATCH',
          message: 'Request domain does not match licensed domain',
          requestDomain,
          licensedDomain: license.domain
        });
      }

      // Check feature access if required
      if (requiredFeature && !isFeatureAllowed(license, requiredFeature)) {
        return res.status(403).json({
          success: false,
          error: 'FEATURE_NOT_ALLOWED',
          message: `Feature '${requiredFeature}' is not included in your plan`,
          plan: license.plan,
          availableFeatures: license.features
        });
      }

      // Add license info to request for use in controllers
      (req as any).license = license;

      next();
    } catch (error) {
      console.error('License validation error:', error);
      res.status(500).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Error validating license'
      });
    }
  };
};

/**
 * Middleware to check usage limits
 */
export const checkUsageLimits = (limitType: 'users' | 'storage') => {
  return (req: Request, res: Response, next: NextFunction) => {
    const license = (req as any).license as LicenseInfo;
    
    if (!license) {
      return res.status(401).json({
        success: false,
        error: 'LICENSE_REQUIRED',
        message: 'License validation required'
      });
    }

    // In a real implementation, you would check actual usage from database
    // For now, we'll simulate usage checks
    
    if (limitType === 'users') {
      // Simulate user count check
      const currentUsers = 5; // This would come from database
      if (currentUsers >= license.maxUsers) {
        return res.status(403).json({
          success: false,
          error: 'USER_LIMIT_EXCEEDED',
          message: 'Maximum number of users reached',
          currentUsers,
          maxUsers: license.maxUsers
        });
      }
    }

    if (limitType === 'storage') {
      // Simulate storage check
      const currentStorage = 10; // This would come from database (in GB)
      if (currentStorage >= license.maxStorage) {
        return res.status(403).json({
          success: false,
          error: 'STORAGE_LIMIT_EXCEEDED',
          message: 'Storage limit exceeded',
          currentStorage: `${currentStorage}GB`,
          maxStorage: `${license.maxStorage}GB`
        });
      }
    }

    next();
  };
};

/**
 * Get license info endpoint
 */
export const getLicenseInfo = (req: Request, res: Response) => {
  const license = (req as any).license as LicenseInfo;
  
  if (!license) {
    return res.status(401).json({
      success: false,
      error: 'LICENSE_REQUIRED',
      message: 'License validation required'
    });
  }

  // Calculate days until expiry
  const expiryDate = new Date(license.expiresAt);
  const now = new Date();
  const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

  res.json({
    success: true,
    license: {
      companyName: license.companyName,
      domain: license.domain,
      plan: license.plan,
      status: license.status,
      expiresAt: license.expiresAt,
      daysUntilExpiry,
      features: license.features,
      limits: {
        maxUsers: license.maxUsers,
        maxStorage: license.maxStorage
      }
    }
  });
};

// Export licenses for testing
export { licenses };
