import axios from 'axios';
import { XMLBuilder, XMLParser } from 'fast-xml-parser';

export interface TallyConfig {
  serverUrl: string;
  port: number;
  companyName: string;
  username?: string;
  password?: string;
}

export interface TallyLedger {
  name: string;
  parent: string;
  openingBalance: number;
  address?: string;
  phone?: string;
  email?: string;
  gstNumber?: string;
}

export interface TallyVoucher {
  voucherType: string;
  date: string;
  reference: string;
  narration: string;
  ledgerEntries: TallyLedgerEntry[];
}

export interface TallyLedgerEntry {
  ledgerName: string;
  amount: number;
  isDebit: boolean;
}

export interface TallyStockItem {
  name: string;
  parent: string;
  baseUnit: string;
  openingBalance: number;
  openingValue: number;
  gstRate?: number;
  hsnCode?: string;
}

export class TallyService {
  private config: TallyConfig;
  private xmlBuilder: XMLBuilder;
  private xmlParser: XMLParser;

  constructor(config: TallyConfig) {
    this.config = config;
    this.xmlBuilder = new XMLBuilder({
      ignoreAttributes: false,
      format: true,
      suppressEmptyNode: true
    });
    this.xmlParser = new XMLParser({
      ignoreAttributes: false,
      parseAttributeValue: true
    });
  }

  /**
   * Test connection to Tally server
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.makeRequest('GET', '/');
      return response.status === 200;
    } catch (error) {
      console.error('Tally connection test failed:', error);
      return false;
    }
  }

  /**
   * Get company information from Tally
   */
  async getCompanyInfo(): Promise<any> {
    const xmlRequest = this.buildXMLRequest('Export', 'Company', {});
    return this.sendXMLRequest(xmlRequest);
  }

  /**
   * Create or update ledger in Tally
   */
  async createLedger(ledger: TallyLedger): Promise<any> {
    const ledgerXML = {
      TALLYMESSAGE: {
        '@_xmlns:UDF': 'TallyUDF',
        LEDGER: {
          '@_NAME': ledger.name,
          '@_RESERVEDNAME': '',
          OLDAUDITENTRYIDS: {
            '@_LIST': 'Number',
            OLDAUDITENTRYIDS: '-1'
          },
          GUID: this.generateGUID(),
          PARENT: ledger.parent,
          OPENINGBALANCE: ledger.openingBalance,
          ISBILLWISEON: 'No',
          ISCOSTCENTRESON: 'No',
          ...(ledger.address && {
            'ADDRESS.LIST': {
              '@_TYPE': 'String',
              ADDRESS: ledger.address
            }
          }),
          ...(ledger.phone && { PHONE: ledger.phone }),
          ...(ledger.email && { EMAIL: ledger.email }),
          ...(ledger.gstNumber && { PARTYGSTIN: ledger.gstNumber })
        }
      }
    };

    const xmlRequest = this.buildXMLRequest('Import', 'All Masters', ledgerXML);
    return this.sendXMLRequest(xmlRequest);
  }

  /**
   * Create voucher in Tally
   */
  async createVoucher(voucher: TallyVoucher): Promise<any> {
    const voucherXML = {
      TALLYMESSAGE: {
        '@_xmlns:UDF': 'TallyUDF',
        VOUCHER: {
          '@_REMOTEID': this.generateGUID(),
          '@_VCHKEY': this.generateGUID(),
          '@_VCHTYPE': voucher.voucherType,
          DATE: this.formatDate(voucher.date),
          REFERENCE: voucher.reference,
          NARRATION: voucher.narration,
          VOUCHERTYPENAME: voucher.voucherType,
          VOUCHERNUMBER: voucher.reference,
          'ALLLEDGERENTRIES.LIST': voucher.ledgerEntries.map(entry => ({
            LEDGERNAME: entry.ledgerName,
            ISDEEMEDPOSITIVE: entry.isDebit ? 'Yes' : 'No',
            AMOUNT: entry.isDebit ? entry.amount : -entry.amount
          }))
        }
      }
    };

    const xmlRequest = this.buildXMLRequest('Import', 'All Masters', voucherXML);
    return this.sendXMLRequest(xmlRequest);
  }

  /**
   * Create stock item in Tally
   */
  async createStockItem(stockItem: TallyStockItem): Promise<any> {
    const stockItemXML = {
      TALLYMESSAGE: {
        '@_xmlns:UDF': 'TallyUDF',
        STOCKITEM: {
          '@_NAME': stockItem.name,
          '@_RESERVEDNAME': '',
          OLDAUDITENTRYIDS: {
            '@_LIST': 'Number',
            OLDAUDITENTRYIDS: '-1'
          },
          GUID: this.generateGUID(),
          PARENT: stockItem.parent,
          BASEUNITS: stockItem.baseUnit,
          OPENINGBALANCE: stockItem.openingBalance,
          OPENINGVALUE: stockItem.openingValue,
          OPENINGRATE: stockItem.openingBalance > 0 ? stockItem.openingValue / stockItem.openingBalance : 0,
          ...(stockItem.gstRate && {
            'GSTDETAILS.LIST': {
              APPLICABLEFROM: '20170701',
              CALCULATIONTYPE: 'On Value',
              HSNCODE: stockItem.hsnCode || '',
              TAXABILITY: 'Taxable',
              ISREVERSECHARGEAPPLICABLE: 'No',
              GSTRATE: stockItem.gstRate
            }
          })
        }
      }
    };

    const xmlRequest = this.buildXMLRequest('Import', 'All Masters', stockItemXML);
    return this.sendXMLRequest(xmlRequest);
  }

  /**
   * Sync inventory data to Tally
   */
  async syncInventoryData(inventoryData: any): Promise<any> {
    const results = {
      ledgers: { success: 0, failed: 0, errors: [] as string[] },
      vouchers: { success: 0, failed: 0, errors: [] as string[] },
      stockItems: { success: 0, failed: 0, errors: [] as string[] }
    };

    // Sync customers as ledgers
    if (inventoryData.customers) {
      for (const customer of inventoryData.customers) {
        try {
          const ledger: TallyLedger = {
            name: customer.name,
            parent: 'Sundry Debtors',
            openingBalance: customer.balance || 0,
            address: customer.address,
            phone: customer.phone,
            email: customer.email,
            gstNumber: customer.gstNumber
          };
          await this.createLedger(ledger);
          results.ledgers.success++;
        } catch (error) {
          results.ledgers.failed++;
          results.ledgers.errors.push(`Customer ${customer.name}: ${error}`);
        }
      }
    }

    // Sync vendors as ledgers
    if (inventoryData.vendors) {
      for (const vendor of inventoryData.vendors) {
        try {
          const ledger: TallyLedger = {
            name: vendor.name,
            parent: 'Sundry Creditors',
            openingBalance: vendor.balance || 0,
            address: vendor.address,
            phone: vendor.phone,
            email: vendor.email,
            gstNumber: vendor.gstNumber
          };
          await this.createLedger(ledger);
          results.ledgers.success++;
        } catch (error) {
          results.ledgers.failed++;
          results.ledgers.errors.push(`Vendor ${vendor.name}: ${error}`);
        }
      }
    }

    // Sync products as stock items
    if (inventoryData.products) {
      for (const product of inventoryData.products) {
        try {
          const stockItem: TallyStockItem = {
            name: product.name,
            parent: product.category || 'Primary',
            baseUnit: product.unit || 'Nos',
            openingBalance: product.quantity || 0,
            openingValue: (product.quantity || 0) * (product.cost || 0),
            gstRate: product.gstRate,
            hsnCode: product.hsnCode
          };
          await this.createStockItem(stockItem);
          results.stockItems.success++;
        } catch (error) {
          results.stockItems.failed++;
          results.stockItems.errors.push(`Product ${product.name}: ${error}`);
        }
      }
    }

    // Sync sales as vouchers
    if (inventoryData.sales) {
      for (const sale of inventoryData.sales) {
        try {
          const voucher: TallyVoucher = {
            voucherType: 'Sales',
            date: sale.date,
            reference: sale.invoiceNumber,
            narration: `Sale to ${sale.customerName}`,
            ledgerEntries: [
              {
                ledgerName: sale.customerName,
                amount: sale.total,
                isDebit: true
              },
              {
                ledgerName: 'Sales',
                amount: sale.total,
                isDebit: false
              }
            ]
          };
          await this.createVoucher(voucher);
          results.vouchers.success++;
        } catch (error) {
          results.vouchers.failed++;
          results.vouchers.errors.push(`Sale ${sale.invoiceNumber}: ${error}`);
        }
      }
    }

    return results;
  }

  /**
   * Build XML request for Tally
   */
  private buildXMLRequest(requestType: string, reportName: string, data: any = {}): string {
    const envelope = {
      ENVELOPE: {
        HEADER: {
          TALLYREQUEST: `${requestType} Data`
        },
        BODY: {
          [`${requestType.toUpperCase()}DATA`]: {
            REQUESTDESC: {
              REPORTNAME: reportName
            },
            REQUESTDATA: data
          }
        }
      }
    };

    return this.xmlBuilder.build(envelope);
  }

  /**
   * Send XML request to Tally
   */
  private async sendXMLRequest(xmlData: string): Promise<any> {
    try {
      const response = await this.makeRequest('POST', '/', xmlData, {
        'Content-Type': 'application/xml'
      });
      return this.xmlParser.parse(response.data);
    } catch (error) {
      throw new Error(`Tally XML request failed: ${error}`);
    }
  }

  /**
   * Make HTTP request to Tally server
   */
  private async makeRequest(method: string, path: string, data?: any, headers?: any): Promise<any> {
    const url = `http://${this.config.serverUrl}:${this.config.port}${path}`;
    
    const config: any = {
      method,
      url,
      timeout: 30000,
      headers: {
        'User-Agent': 'Inventory-Management-System/1.0',
        ...headers
      }
    };

    if (data) {
      config.data = data;
    }

    if (this.config.username && this.config.password) {
      config.auth = {
        username: this.config.username,
        password: this.config.password
      };
    }

    return axios(config);
  }

  /**
   * Generate GUID for Tally objects
   */
  private generateGUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Format date for Tally (YYYYMMDD)
   */
  private formatDate(date: string): string {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  }
}
