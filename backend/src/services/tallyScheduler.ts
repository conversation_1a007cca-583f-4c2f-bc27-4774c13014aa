import * as cron from 'node-cron';
import { TallyService, TallyConfig } from './tallyService';
// import { AppDataSource } from '../config/database';
// import { Product } from '../entities/Product';
// import { Customer } from '../entities/Customer';
// import { Order } from '../entities/Order';

export interface ScheduledSyncConfig {
  tallyConfig: TallyConfig;
  syncInterval: number; // in minutes
  modules: {
    ledgers: boolean;
    vouchers: boolean;
    stockItems: boolean;
    stockGroups: boolean;
    units: boolean;
    godowns: boolean;
    costCentres: boolean;
    budgets: boolean;
  };
  autoSync: boolean;
}

export class TallyScheduler {
  private static instance: TallyScheduler;
  private scheduledTasks: Map<string, cron.ScheduledTask> = new Map();
  private syncConfigs: Map<string, ScheduledSyncConfig> = new Map();
  private syncLogs: Array<any> = [];

  private constructor() {}

  public static getInstance(): TallyScheduler {
    if (!TallyScheduler.instance) {
      TallyScheduler.instance = new TallyScheduler();
    }
    return TallyScheduler.instance;
  }

  /**
   * Schedule automatic synchronization
   */
  public scheduleSync(configId: string, config: ScheduledSyncConfig): boolean {
    try {
      // Stop existing task if any
      this.stopSync(configId);

      if (!config.autoSync) {
        return true;
      }

      // Convert minutes to cron expression
      const cronExpression = this.minutesToCron(config.syncInterval);
      
      // Create scheduled task
      const task = cron.schedule(cronExpression, async () => {
        await this.performScheduledSync(configId, config);
      }, {
        scheduled: false,
        timezone: 'Asia/Kolkata' // Adjust timezone as needed
      });

      // Start the task
      task.start();

      // Store the task and config
      this.scheduledTasks.set(configId, task);
      this.syncConfigs.set(configId, config);

      this.addLog(configId, 'Scheduler', 'Schedule', 'success', 
        `Automatic sync scheduled every ${config.syncInterval} minutes`, 0);

      return true;
    } catch (error) {
      console.error('Error scheduling sync:', error);
      this.addLog(configId, 'Scheduler', 'Schedule', 'error', 
        `Failed to schedule sync: ${error}`, 0);
      return false;
    }
  }

  /**
   * Stop scheduled synchronization
   */
  public stopSync(configId: string): boolean {
    try {
      const task = this.scheduledTasks.get(configId);
      if (task) {
        task.stop();
        task.destroy();
        this.scheduledTasks.delete(configId);
        this.syncConfigs.delete(configId);
        
        this.addLog(configId, 'Scheduler', 'Stop', 'success', 
          'Automatic sync stopped', 0);
      }
      return true;
    } catch (error) {
      console.error('Error stopping sync:', error);
      this.addLog(configId, 'Scheduler', 'Stop', 'error', 
        `Failed to stop sync: ${error}`, 0);
      return false;
    }
  }

  /**
   * Get sync status for a configuration
   */
  public getSyncStatus(configId: string): any {
    const task = this.scheduledTasks.get(configId);
    const config = this.syncConfigs.get(configId);
    
    return {
      isScheduled: !!task,
      isRunning: task ? task.getStatus() === 'scheduled' : false,
      config: config || null,
      nextRun: this.getNextRunTime(configId),
      lastRun: this.getLastRunTime(configId)
    };
  }

  /**
   * Get all sync logs
   */
  public getSyncLogs(configId?: string): any[] {
    if (configId) {
      return this.syncLogs.filter(log => log.configId === configId);
    }
    return this.syncLogs;
  }

  /**
   * Perform manual synchronization
   */
  public async performManualSync(configId: string, config: ScheduledSyncConfig): Promise<any> {
    return this.performScheduledSync(configId, config);
  }

  /**
   * Perform scheduled synchronization
   */
  private async performScheduledSync(configId: string, config: ScheduledSyncConfig): Promise<any> {
    const startTime = new Date();
    let results: any = null;

    try {
      this.addLog(configId, 'Sync', 'Start', 'info', 
        'Starting scheduled synchronization', 0);

      // Initialize Tally service
      const tallyService = new TallyService(config.tallyConfig);

      // Test connection
      const isConnected = await tallyService.testConnection();
      if (!isConnected) {
        throw new Error('Cannot connect to Tally server');
      }

      // Gather inventory data
      const inventoryData = await this.gatherInventoryData(config.modules);

      // Sync data to Tally
      results = await tallyService.syncInventoryData(inventoryData);

      // Calculate totals
      const totalSuccess = Object.values(results).reduce((sum: number, module: any) => 
        sum + (module.success || 0), 0);
      const totalFailed = Object.values(results).reduce((sum: number, module: any) => 
        sum + (module.failed || 0), 0);

      this.addLog(configId, 'Sync', 'Complete', 'success', 
        `Sync completed: ${totalSuccess} successful, ${totalFailed} failed`, 
        totalSuccess + totalFailed);

      // Log individual module results
      Object.entries(results).forEach(([module, result]: [string, any]) => {
        if (result.success > 0) {
          this.addLog(configId, module.charAt(0).toUpperCase() + module.slice(1), 'Export', 'success',
            `Successfully exported ${result.success} ${module}`, result.success);
        }
        if (result.failed > 0) {
          this.addLog(configId, module.charAt(0).toUpperCase() + module.slice(1), 'Export', 'error',
            `Failed to export ${result.failed} ${module}`, result.failed);
        }
      });

      return results;

    } catch (error) {
      console.error('Scheduled sync error:', error);
      this.addLog(configId, 'Sync', 'Error', 'error', 
        `Sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 0);
      throw error;
    }
  }

  /**
   * Gather inventory data from database (using mock data for testing)
   */
  private async gatherInventoryData(modules: any): Promise<any> {
    const data: any = {};

    try {
      // Get customers if enabled
      if (modules.ledgers) {
        // Mock customer data for testing
        data.customers = [
          {
            name: 'ABC Corporation',
            balance: 5000,
            address: '123 Business Street, City, State 12345',
            phone: '******-0123',
            email: '<EMAIL>',
            gstNumber: '29ABCDE1234F1Z5'
          },
          {
            name: 'XYZ Enterprises',
            balance: 3200,
            address: '456 Commerce Ave, City, State 67890',
            phone: '******-0456',
            email: '<EMAIL>',
            gstNumber: '29XYZAB5678G2W6'
          }
        ];

        // Mock vendor data
        data.vendors = [
          {
            name: 'Supplier One Ltd',
            balance: -2500,
            address: '789 Supply Chain Rd, City, State 11111',
            phone: '******-0789',
            email: '<EMAIL>',
            gstNumber: '29SUPPL1234H3X7'
          }
        ];
      }

      // Get products if enabled
      if (modules.stockItems) {
        // Mock product data for testing
        data.products = [
          {
            name: 'Laptop Computer',
            category: 'Electronics',
            unit: 'Nos',
            quantity: 25,
            cost: 45000,
            gstRate: 18,
            hsnCode: '8471'
          },
          {
            name: 'Office Chair',
            category: 'Furniture',
            unit: 'Nos',
            quantity: 50,
            cost: 8500,
            gstRate: 18,
            hsnCode: '9401'
          }
        ];
      }

      // Get sales/orders if enabled
      if (modules.vouchers) {
        // Mock sales data for testing
        data.sales = [
          {
            invoiceNumber: 'INV-2024-001',
            date: '2024-12-06',
            customerName: 'ABC Corporation',
            total: 53100
          }
        ];
      }

      return data;
    } catch (error) {
      console.error('Error gathering inventory data:', error);
      throw new Error('Failed to gather inventory data');
    }
  }

  /**
   * Convert minutes to cron expression
   */
  private minutesToCron(minutes: number): string {
    if (minutes < 60) {
      // Every X minutes
      return `*/${minutes} * * * *`;
    } else if (minutes === 60) {
      // Every hour
      return '0 * * * *';
    } else if (minutes % 60 === 0) {
      // Every X hours
      const hours = minutes / 60;
      return `0 */${hours} * * *`;
    } else if (minutes === 1440) {
      // Daily
      return '0 0 * * *';
    } else {
      // Default to every hour for complex intervals
      return '0 * * * *';
    }
  }

  /**
   * Get next run time for a scheduled task
   */
  private getNextRunTime(configId: string): string | null {
    const task = this.scheduledTasks.get(configId);
    if (!task) return null;

    // This is a simplified implementation
    // In a real scenario, you'd calculate based on the cron expression
    const config = this.syncConfigs.get(configId);
    if (!config) return null;

    const now = new Date();
    const nextRun = new Date(now.getTime() + config.syncInterval * 60000);
    return nextRun.toISOString();
  }

  /**
   * Get last run time for a scheduled task
   */
  private getLastRunTime(configId: string): string | null {
    const logs = this.getSyncLogs(configId);
    const lastSyncLog = logs
      .filter(log => log.action === 'Complete' && log.status === 'success')
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0];
    
    return lastSyncLog ? lastSyncLog.timestamp : null;
  }

  /**
   * Add log entry
   */
  private addLog(configId: string, module: string, action: string, status: string, message: string, recordsProcessed: number): void {
    const logEntry = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      configId,
      timestamp: new Date().toISOString(),
      module,
      action,
      status,
      message,
      recordsProcessed
    };

    this.syncLogs.unshift(logEntry);

    // Keep only last 1000 log entries
    if (this.syncLogs.length > 1000) {
      this.syncLogs = this.syncLogs.slice(0, 1000);
    }
  }

  /**
   * Get all scheduled tasks status
   */
  public getAllTasksStatus(): any[] {
    const statuses: any[] = [];
    
    this.scheduledTasks.forEach((task, configId) => {
      statuses.push({
        configId,
        ...this.getSyncStatus(configId)
      });
    });

    return statuses;
  }

  /**
   * Stop all scheduled tasks
   */
  public stopAllTasks(): void {
    this.scheduledTasks.forEach((task, configId) => {
      this.stopSync(configId);
    });
  }
}

// Export singleton instance
export const tallyScheduler = TallyScheduler.getInstance();
