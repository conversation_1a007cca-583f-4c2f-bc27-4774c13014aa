import * as cron from 'node-cron';

interface Client {
  id: string;
  companyName: string;
  contactName: string;
  email: string;
  domain: string;
  plan: string;
  status: 'active' | 'suspended' | 'expired' | 'trial';
  licenseKey: string;
  expiresAt: string;
}

interface EmailTemplate {
  subject: string;
  body: string;
}

interface NotificationSettings {
  enabled: boolean;
  daysBeforeExpiry: number[];
  templates: {
    warning: EmailTemplate;
    expired: EmailTemplate;
    suspended: EmailTemplate;
  };
}

export class EmailNotificationService {
  private static instance: EmailNotificationService;
  private notificationSettings: NotificationSettings;
  private scheduledTask: cron.ScheduledTask | null = null;

  private constructor() {
    this.notificationSettings = {
      enabled: true,
      daysBeforeExpiry: [30, 7, 1],
      templates: {
        warning: {
          subject: 'License Expiry Warning - {COMPANY_NAME}',
          body: `Dear {CONTACT_NAME},

Your license for Inventory Management System will expire in {DAYS_LEFT} days on {EXPIRY_DATE}.

Please renew your license to continue using our services without interruption.

License Details:
- Company: {COMPANY_NAME}
- License Key: {LICENSE_KEY}
- Domain: {DOMAIN}
- Plan: {PLAN}
- Expiry Date: {EXPIRY_DATE}

To renew your license, please contact our sales team or visit our customer portal.

Best regards,
Inventory Management System Team`
        },
        expired: {
          subject: 'License Expired - Immediate Action Required - {COMPANY_NAME}',
          body: `Dear {CONTACT_NAME},

Your license for Inventory Management System has expired on {EXPIRY_DATE}.

Your account has been suspended and access to the system is now restricted.

License Details:
- Company: {COMPANY_NAME}
- License Key: {LICENSE_KEY}
- Domain: {DOMAIN}
- Plan: {PLAN}
- Expired On: {EXPIRY_DATE}

Please renew your license immediately to restore full access to the system.

Contact our sales team for immediate assistance:
- Email: <EMAIL>
- Phone: ******-INVENTORY

Best regards,
Inventory Management System Team`
        },
        suspended: {
          subject: 'Account Suspended - {COMPANY_NAME}',
          body: `Dear {CONTACT_NAME},

Your account for Inventory Management System has been suspended.

License Details:
- Company: {COMPANY_NAME}
- License Key: {LICENSE_KEY}
- Domain: {DOMAIN}
- Plan: {PLAN}

Please contact our support team to resolve this issue.

Best regards,
Inventory Management System Team`
        }
      }
    };
  }

  public static getInstance(): EmailNotificationService {
    if (!EmailNotificationService.instance) {
      EmailNotificationService.instance = new EmailNotificationService();
    }
    return EmailNotificationService.instance;
  }

  /**
   * Start the notification scheduler
   */
  public startScheduler(): void {
    if (this.scheduledTask) {
      this.scheduledTask.stop();
    }

    // Run daily at 9:00 AM
    this.scheduledTask = cron.schedule('0 9 * * *', async () => {
      await this.checkAndSendNotifications();
    }, {
      scheduled: true,
      timezone: 'UTC'
    });

    console.log('Email notification scheduler started');
  }

  /**
   * Stop the notification scheduler
   */
  public stopScheduler(): void {
    if (this.scheduledTask) {
      this.scheduledTask.stop();
      this.scheduledTask = null;
    }
    console.log('Email notification scheduler stopped');
  }

  /**
   * Check all clients and send notifications
   */
  public async checkAndSendNotifications(): Promise<void> {
    if (!this.notificationSettings.enabled) {
      return;
    }

    try {
      const clients = await this.getClients();
      const today = new Date();

      for (const client of clients) {
        const expiryDate = new Date(client.expiresAt);
        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

        // Check if we need to send expiry warning
        if (this.notificationSettings.daysBeforeExpiry.includes(daysUntilExpiry) && 
            (client.status === 'active' || client.status === 'trial')) {
          await this.sendExpiryWarning(client, daysUntilExpiry);
        }

        // Check if license has expired
        if (daysUntilExpiry <= 0 && client.status !== 'expired') {
          await this.sendExpiredNotification(client);
          // Update client status to expired (in real implementation)
          console.log(`Client ${client.companyName} license expired`);
        }

        // Check if client is suspended
        if (client.status === 'suspended') {
          await this.sendSuspendedNotification(client);
        }
      }
    } catch (error) {
      console.error('Error checking notifications:', error);
    }
  }

  /**
   * Send expiry warning email
   */
  private async sendExpiryWarning(client: Client, daysLeft: number): Promise<void> {
    const template = this.notificationSettings.templates.warning;
    const subject = this.replaceTemplateVariables(template.subject, client, daysLeft);
    const body = this.replaceTemplateVariables(template.body, client, daysLeft);

    await this.sendEmail(client.email, subject, body);
    console.log(`Expiry warning sent to ${client.companyName} (${daysLeft} days left)`);
  }

  /**
   * Send expired notification email
   */
  private async sendExpiredNotification(client: Client): Promise<void> {
    const template = this.notificationSettings.templates.expired;
    const subject = this.replaceTemplateVariables(template.subject, client, 0);
    const body = this.replaceTemplateVariables(template.body, client, 0);

    await this.sendEmail(client.email, subject, body);
    console.log(`Expired notification sent to ${client.companyName}`);
  }

  /**
   * Send suspended notification email
   */
  private async sendSuspendedNotification(client: Client): Promise<void> {
    const template = this.notificationSettings.templates.suspended;
    const subject = this.replaceTemplateVariables(template.subject, client, 0);
    const body = this.replaceTemplateVariables(template.body, client, 0);

    await this.sendEmail(client.email, subject, body);
    console.log(`Suspended notification sent to ${client.companyName}`);
  }

  /**
   * Replace template variables with actual values
   */
  private replaceTemplateVariables(template: string, client: Client, daysLeft: number): string {
    return template
      .replace(/{COMPANY_NAME}/g, client.companyName)
      .replace(/{CONTACT_NAME}/g, client.contactName)
      .replace(/{EMAIL}/g, client.email)
      .replace(/{DOMAIN}/g, client.domain)
      .replace(/{PLAN}/g, client.plan.charAt(0).toUpperCase() + client.plan.slice(1))
      .replace(/{LICENSE_KEY}/g, client.licenseKey)
      .replace(/{EXPIRY_DATE}/g, client.expiresAt)
      .replace(/{DAYS_LEFT}/g, daysLeft.toString());
  }

  /**
   * Send email (mock implementation)
   */
  private async sendEmail(to: string, subject: string, body: string): Promise<void> {
    // In a real implementation, you would use a service like SendGrid, AWS SES, or Nodemailer
    console.log('=== EMAIL NOTIFICATION ===');
    console.log(`To: ${to}`);
    console.log(`Subject: ${subject}`);
    console.log(`Body:\n${body}`);
    console.log('========================');

    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Get all clients (mock implementation)
   */
  private async getClients(): Promise<Client[]> {
    // In a real implementation, this would fetch from database
    return [
      {
        id: '1',
        companyName: 'TechCorp Solutions',
        contactName: 'John Smith',
        email: '<EMAIL>',
        domain: 'techcorp.com',
        plan: 'professional',
        status: 'active',
        licenseKey: 'TC-PRO-2024-001',
        expiresAt: '2025-01-15'
      },
      {
        id: '2',
        companyName: 'StartupXYZ',
        contactName: 'Sarah Johnson',
        email: '<EMAIL>',
        domain: 'startupxyz.com',
        plan: 'starter',
        status: 'active',
        licenseKey: 'SX-STR-2024-002',
        expiresAt: '2024-12-20' // Expires soon
      },
      {
        id: '4',
        companyName: 'Trial Company',
        contactName: 'Lisa Wilson',
        email: '<EMAIL>',
        domain: 'trialcompany.com',
        plan: 'professional',
        status: 'trial',
        licenseKey: 'TC-TRL-2024-004',
        expiresAt: '2024-12-20' // Expires soon
      }
    ];
  }

  /**
   * Update notification settings
   */
  public updateSettings(settings: Partial<NotificationSettings>): void {
    this.notificationSettings = { ...this.notificationSettings, ...settings };
    console.log('Notification settings updated');
  }

  /**
   * Get current notification settings
   */
  public getSettings(): NotificationSettings {
    return this.notificationSettings;
  }

  /**
   * Send test email
   */
  public async sendTestEmail(email: string): Promise<void> {
    const subject = 'Test Email - Inventory Management System';
    const body = `This is a test email from the Inventory Management System notification service.

If you received this email, the notification system is working correctly.

Timestamp: ${new Date().toISOString()}

Best regards,
Inventory Management System Team`;

    await this.sendEmail(email, subject, body);
  }

  /**
   * Manually trigger notification check
   */
  public async triggerNotificationCheck(): Promise<void> {
    console.log('Manually triggering notification check...');
    await this.checkAndSendNotifications();
  }
}

// Export singleton instance
export const emailNotificationService = EmailNotificationService.getInstance();
