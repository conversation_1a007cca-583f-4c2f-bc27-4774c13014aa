import { PrismaClient } from '@prisma/client';

// Global Prisma instance
declare global {
  var __prisma: PrismaClient | undefined;
}

// Create Prisma client with proper configuration
const createPrismaClient = () => {
  return new PrismaClient({
    log: process.env['NODE_ENV'] === 'development' ? ['query', 'error', 'warn'] : ['error'],
    errorFormat: 'pretty',
  });
};

// Use global instance in development to prevent multiple connections
const prisma = globalThis.__prisma || createPrismaClient();

if (process.env['NODE_ENV'] === 'development') {
  globalThis.__prisma = prisma;
}

// Database connection management
export class DatabaseService {
  private static instance: DatabaseService;
  public prisma: PrismaClient;

  private constructor() {
    this.prisma = prisma;
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  // Connect to database
  async connect(): Promise<void> {
    try {
      await this.prisma.$connect();
      console.log('✅ Database connected successfully');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw error;
    }
  }

  // Disconnect from database
  async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      console.log('✅ Database disconnected successfully');
    } catch (error) {
      console.error('❌ Database disconnection failed:', error);
      throw error;
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.error('❌ Database health check failed:', error);
      return false;
    }
  }

  // Transaction wrapper
  async transaction<T>(
    fn: (prisma: any) => Promise<T>
  ): Promise<T> {
    return await this.prisma.$transaction(fn);
  }

  // Seed database with initial data
  async seed(): Promise<void> {
    try {
      console.log('🌱 Starting database seeding...');

      // Create default admin user
      await this.prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          username: 'admin',
          firstName: 'System',
          lastName: 'Administrator',
          password: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', // password: admin123
          role: 'SUPER_ADMIN',
          isActive: true,
        },
      });

      // Create default categories
      const categories = [
        { name: 'Electronics', description: 'Electronic devices and accessories' },
        { name: 'Clothing', description: 'Apparel and fashion items' },
        { name: 'Home & Garden', description: 'Home improvement and garden supplies' },
        { name: 'Books', description: 'Books and educational materials' },
        { name: 'Sports', description: 'Sports equipment and accessories' },
        { name: 'Furniture', description: 'Furniture and home decor' },
      ];

      for (const category of categories) {
        await this.prisma.category.upsert({
          where: { name: category.name },
          update: {},
          create: category,
        });
      }

      // Create default brands
      const brands = [
        { name: 'Apple', description: 'Technology company' },
        { name: 'Samsung', description: 'Electronics manufacturer' },
        { name: 'Nike', description: 'Sports apparel and equipment' },
        { name: 'IKEA', description: 'Furniture and home accessories' },
      ];

      for (const brand of brands) {
        await this.prisma.brand.upsert({
          where: { name: brand.name },
          update: {},
          create: brand,
        });
      }

      // Create default warehouse
      await this.prisma.warehouse.upsert({
        where: { code: 'WH001' },
        update: {},
        create: {
          name: 'Main Warehouse',
          code: 'WH001',
          address: '123 Industrial Blvd',
          city: 'Business City',
          state: 'State',
          country: 'Country',
          zipCode: '12345',
          phone: '+****************',
          email: '<EMAIL>',
          isActive: true,
        },
      });

      // Create default customer
      await this.prisma.customer.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          phone: '+****************',
          company: 'Example Corp',
          billingAddress: {
            name: 'John Doe',
            company: 'Example Corp',
            address1: '456 Customer St',
            city: 'Customer City',
            state: 'State',
            zipCode: '67890',
            country: 'Country',
          },
          isActive: true,
        },
      });

      // Create default vendor
      await this.prisma.vendor.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          name: 'Example Vendor',
          email: '<EMAIL>',
          phone: '+****************',
          company: 'Vendor Corp',
          address: {
            address1: '789 Vendor Ave',
            city: 'Vendor City',
            state: 'State',
            zipCode: '54321',
            country: 'Country',
          },
          paymentTerms: 'Net 30',
          creditLimit: 50000,
          isActive: true,
        },
      });

      // Create sample client
      const sampleClient = await this.prisma.client.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          companyName: 'Acme Corporation',
          contactName: 'Jane Smith',
          email: '<EMAIL>',
          phone: '+****************',
          domain: 'acme.inventoryms.com',
          plan: 'PROFESSIONAL',
          billingCycle: 'MONTHLY',
          amount: 99.99,
          currency: 'USD',
          maxUsers: 25,
          maxStorage: 100,
          usedUsers: 5,
          usedStorage: 15,
          status: 'ACTIVE',
          licenseKey: 'ACME-PROF-2024-' + Math.random().toString(36).substring(2, 15).toUpperCase(),
          features: [
            'inventory_management',
            'pos_system',
            'warehouse_management',
            'reporting',
            'api_access'
          ],
          expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        },
      });

      // Create default payment gateway (temporarily disabled)
      // await this.prisma.paymentGateway.upsert({
      //   where: { name: 'Stripe Test' },
      //   update: {},
      //   create: {
      //     name: 'Stripe Test',
      //     type: 'STRIPE',
      //     enabled: true,
      //     isDefault: true,
      //     configuration: {
      //       publishableKey: 'pk_test_...',
      //       secretKey: 'sk_test_...',
      //       environment: 'sandbox',
      //       currency: 'USD',
      //       minimumAmount: 50,
      //       maximumAmount: 100000,
      //     },
      //     supportedCurrencies: ['USD', 'EUR', 'GBP'],
      //     supportedCountries: ['US', 'CA', 'GB', 'AU'],
      //     features: [
      //       { name: 'Credit Cards', enabled: true },
      //       { name: 'Digital Wallets', enabled: true },
      //       { name: 'Recurring Payments', enabled: true },
      //     ],
      //     status: 'ACTIVE',
      //   },
      // });

      // Create payment settings (temporarily disabled)
      // await this.prisma.paymentSettings.upsert({
      //   where: { id: 'default' },
      //   update: {},
      //   create: {
      //     id: 'default',
      //     defaultGateway: 'stripe',
      //     defaultCurrency: 'USD',
      //     allowedCurrencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
      //     minimumAmount: 1.00,
      //     maximumAmount: 100000.00,
      //     autoCapture: true,
      //     requireBillingAddress: true,
      //     requireShippingAddress: false,
      //     enableSaveCard: true,
      //     enableRecurringPayments: true,
      //     paymentTimeout: 30,
      //     retryFailedPayments: true,
      //     maxRetryAttempts: 3,
      //     notificationSettings: {
      //       emailOnSuccess: true,
      //       emailOnFailure: true,
      //       smsOnSuccess: false,
      //       smsOnFailure: false,
      //     },
      //   },
      // });

      console.log('✅ Database seeding completed successfully');
      console.log(`👤 Admin user created: <EMAIL> (password: admin123)`);
      console.log(`🏢 Sample client created: ${sampleClient.companyName}`);
      console.log(`🔑 License key: ${sampleClient.licenseKey}`);

    } catch (error) {
      console.error('❌ Database seeding failed:', error);
      throw error;
    }
  }

  // Clear all data (for testing)
  async clearDatabase(): Promise<void> {
    if (process.env['NODE_ENV'] === 'production') {
      throw new Error('Cannot clear database in production');
    }

    try {
      console.log('🧹 Clearing database...');

      // Delete in reverse order of dependencies
      await this.prisma.paymentTransaction.deleteMany();
      await this.prisma.paymentWebhook.deleteMany();
      await this.prisma.paymentGateway.deleteMany();
      await this.prisma.paymentSettings.deleteMany();
      
      await this.prisma.clientWarehouseZone.deleteMany();
      await this.prisma.pOSTerminal.deleteMany();
      await this.prisma.clientWarehouse.deleteMany();
      await this.prisma.clientSettings.deleteMany();
      await this.prisma.clientUser.deleteMany();
      await this.prisma.client.deleteMany();

      await this.prisma.inventoryMovement.deleteMany();
      await this.prisma.inventoryItem.deleteMany();
      await this.prisma.purchaseOrderItem.deleteMany();
      await this.prisma.purchaseOrder.deleteMany();
      await this.prisma.vendor.deleteMany();
      
      await this.prisma.payment.deleteMany();
      await this.prisma.orderItem.deleteMany();
      await this.prisma.order.deleteMany();
      await this.prisma.customer.deleteMany();
      
      await this.prisma.productAttribute.deleteMany();
      await this.prisma.productImage.deleteMany();
      await this.prisma.productVariant.deleteMany();
      await this.prisma.product.deleteMany();
      await this.prisma.brand.deleteMany();
      await this.prisma.category.deleteMany();
      
      await this.prisma.warehouse.deleteMany();
      await this.prisma.userSession.deleteMany();
      await this.prisma.user.deleteMany();

      console.log('✅ Database cleared successfully');
    } catch (error) {
      console.error('❌ Database clearing failed:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const db = DatabaseService.getInstance();
export { prisma };
export default db;
