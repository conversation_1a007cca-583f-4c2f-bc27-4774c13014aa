import { Request, Response } from 'express';
import { TallyService, TallyConfig } from '../services/tallyService';
// import { AppDataSource } from '../config/database';
// import { Product } from '../entities/Product';
// import { Customer } from '../entities/Customer';
// import { Order } from '../entities/Order';

export class TallyController {
  private tallyService: TallyService | null = null;

  /**
   * Test connection to Tally server
   */
  async testConnection(req: Request, res: Response): Promise<void> {
    try {
      const config: TallyConfig = req.body;
      
      if (!config.serverUrl || !config.port) {
        return res.status(400).json({
          success: false,
          message: 'Server URL and port are required'
        });
      }

      const tallyService = new TallyService(config);
      const isConnected = await tallyService.testConnection();

      if (isConnected) {
        // Store the service instance for future use
        this.tallyService = tallyService;
        
        res.json({
          success: true,
          message: 'Successfully connected to Tally server',
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to connect to Tally server'
        });
      }
    } catch (error) {
      console.error('Tally connection test error:', error);
      res.status(500).json({
        success: false,
        message: 'Connection test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get Tally company information
   */
  async getCompanyInfo(_req: Request, res: Response): Promise<void> {
    try {
      if (!this.tallyService) {
        return res.status(400).json({
          success: false,
          message: 'Tally service not initialized. Please test connection first.'
        });
      }

      const companyInfo = await this.tallyService.getCompanyInfo();
      
      res.json({
        success: true,
        data: companyInfo,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get company info error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get company information',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Perform manual synchronization
   */
  async manualSync(req: Request, res: Response): Promise<void> {
    try {
      const config: TallyConfig = req.body.config;
      const modules = req.body.modules || {};

      if (!config) {
        return res.status(400).json({
          success: false,
          message: 'Tally configuration is required'
        });
      }

      const tallyService = new TallyService(config);
      
      // Test connection first
      const isConnected = await tallyService.testConnection();
      if (!isConnected) {
        return res.status(500).json({
          success: false,
          message: 'Cannot connect to Tally server'
        });
      }

      // Gather data from database
      const inventoryData = await this.gatherInventoryData(modules);
      
      // Sync data to Tally
      const syncResults = await tallyService.syncInventoryData(inventoryData);

      res.json({
        success: true,
        message: 'Synchronization completed',
        results: syncResults,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Manual sync error:', error);
      res.status(500).json({
        success: false,
        message: 'Synchronization failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get synchronization status
   */
  async getSyncStatus(_req: Request, res: Response): Promise<void> {
    try {
      // This would typically come from a database or cache
      const status = {
        isConnected: this.tallyService !== null,
        lastSync: '2024-12-06 08:30:00', // This should be stored in database
        nextSync: '2024-12-06 09:30:00',
        syncInProgress: false,
        totalRecords: 1250,
        syncedRecords: 1250,
        errors: []
      };

      res.json({
        success: true,
        data: status,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get sync status error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get sync status',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get synchronization logs
   */
  async getSyncLogs(_req: Request, res: Response): Promise<void> {
    try {
      // This would typically come from a database
      const logs = [
        {
          id: '1',
          timestamp: '2024-12-06 08:30:00',
          module: 'Ledgers',
          action: 'Export',
          status: 'success',
          message: 'Successfully exported 45 ledger accounts to Tally',
          recordsProcessed: 45
        },
        {
          id: '2',
          timestamp: '2024-12-06 08:25:00',
          module: 'Vouchers',
          action: 'Export',
          status: 'success',
          message: 'Successfully exported 128 vouchers to Tally',
          recordsProcessed: 128
        },
        {
          id: '3',
          timestamp: '2024-12-06 08:20:00',
          module: 'Stock Items',
          action: 'Export',
          status: 'warning',
          message: 'Exported 89 stock items with 2 warnings',
          recordsProcessed: 89
        }
      ];

      res.json({
        success: true,
        data: logs,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get sync logs error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get sync logs',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Schedule automatic synchronization
   */
  async scheduleSync(req: Request, res: Response): Promise<void> {
    try {
      const { config, interval } = req.body;

      if (!config || !interval) {
        return res.status(400).json({
          success: false,
          message: 'Configuration and interval are required'
        });
      }

      // Here you would implement the scheduling logic
      // This could use node-cron or a job queue like Bull
      
      res.json({
        success: true,
        message: `Automatic sync scheduled every ${interval} minutes`,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Schedule sync error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to schedule synchronization',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Gather inventory data from database (using mock data for testing)
   */
  private async gatherInventoryData(modules: any) {
    const data: any = {};

    try {
      // Get customers if enabled
      if (modules.ledgers) {
        // Mock customer data for testing
        data.customers = [
          {
            name: 'ABC Corporation',
            balance: 5000,
            address: '123 Business Street, City, State 12345',
            phone: '******-0123',
            email: '<EMAIL>',
            gstNumber: '29ABCDE1234F1Z5'
          },
          {
            name: 'XYZ Enterprises',
            balance: 3200,
            address: '456 Commerce Ave, City, State 67890',
            phone: '******-0456',
            email: '<EMAIL>',
            gstNumber: '29XYZAB5678G2W6'
          }
        ];

        // Mock vendor data
        data.vendors = [
          {
            name: 'Supplier One Ltd',
            balance: -2500,
            address: '789 Supply Chain Rd, City, State 11111',
            phone: '******-0789',
            email: '<EMAIL>',
            gstNumber: '29SUPPL1234H3X7'
          }
        ];
      }

      // Get products if enabled
      if (modules.stockItems) {
        // Mock product data for testing
        data.products = [
          {
            name: 'Laptop Computer',
            category: 'Electronics',
            unit: 'Nos',
            quantity: 25,
            cost: 45000,
            gstRate: 18,
            hsnCode: '8471'
          },
          {
            name: 'Office Chair',
            category: 'Furniture',
            unit: 'Nos',
            quantity: 50,
            cost: 8500,
            gstRate: 18,
            hsnCode: '9401'
          },
          {
            name: 'Printer Paper',
            category: 'Stationery',
            unit: 'Ream',
            quantity: 100,
            cost: 250,
            gstRate: 12,
            hsnCode: '4802'
          }
        ];
      }

      // Get sales/orders if enabled
      if (modules.vouchers) {
        // Mock sales data for testing
        data.sales = [
          {
            invoiceNumber: 'INV-2024-001',
            date: '2024-12-06',
            customerName: 'ABC Corporation',
            total: 53100
          },
          {
            invoiceNumber: 'INV-2024-002',
            date: '2024-12-05',
            customerName: 'XYZ Enterprises',
            total: 10030
          }
        ];
      }

      return data;
    } catch (error) {
      console.error('Error gathering inventory data:', error);
      throw new Error('Failed to gather inventory data');
    }
  }

  /**
   * Create ledger in Tally
   */
  async createLedger(req: Request, res: Response): Promise<void> {
    try {
      if (!this.tallyService) {
        return res.status(400).json({
          success: false,
          message: 'Tally service not initialized'
        });
      }

      const ledgerData = req.body;
      const result = await this.tallyService.createLedger(ledgerData);

      res.json({
        success: true,
        message: 'Ledger created successfully',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Create ledger error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create ledger',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Create voucher in Tally
   */
  async createVoucher(req: Request, res: Response): Promise<void> {
    try {
      if (!this.tallyService) {
        return res.status(400).json({
          success: false,
          message: 'Tally service not initialized'
        });
      }

      const voucherData = req.body;
      const result = await this.tallyService.createVoucher(voucherData);

      res.json({
        success: true,
        message: 'Voucher created successfully',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Create voucher error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create voucher',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Create stock item in Tally
   */
  async createStockItem(req: Request, res: Response): Promise<void> {
    try {
      if (!this.tallyService) {
        return res.status(400).json({
          success: false,
          message: 'Tally service not initialized'
        });
      }

      const stockItemData = req.body;
      const result = await this.tallyService.createStockItem(stockItemData);

      res.json({
        success: true,
        message: 'Stock item created successfully',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Create stock item error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create stock item',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}
