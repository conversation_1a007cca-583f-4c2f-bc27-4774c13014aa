import { Request, Response } from 'express';
import { emailNotificationService } from '../services/emailNotificationService';

interface Client {
  id: string;
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  domain: string;
  plan: 'starter' | 'professional' | 'enterprise' | 'custom';
  status: 'active' | 'suspended' | 'expired' | 'trial';
  licenseKey: string;
  createdAt: string;
  expiresAt: string;
  lastAccess: string;
  features: string[];
  maxUsers: number;
  usedUsers: number;
  maxStorage: number;
  usedStorage: number;
  billingCycle: 'monthly' | 'yearly';
  amount: number;
  currency: string;
}

// Mock client database
let clients: Client[] = [
  {
    id: '1',
    companyName: 'TechCorp Solutions',
    contactName: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0123',
    domain: 'techcorp.com',
    plan: 'professional',
    status: 'active',
    licenseKey: 'TC-PRO-2024-001',
    createdAt: '2024-01-15',
    expiresAt: '2025-01-15',
    lastAccess: '2024-12-06 10:30:00',
    features: ['inventory', 'reports', 'tally-sync', 'api-access'],
    maxUsers: 10,
    usedUsers: 7,
    maxStorage: 25,
    usedStorage: 18.5,
    billingCycle: 'yearly',
    amount: 790,
    currency: 'USD'
  },
  {
    id: '2',
    companyName: 'StartupXYZ',
    contactName: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '******-0456',
    domain: 'startupxyz.com',
    plan: 'starter',
    status: 'active',
    licenseKey: 'SX-STR-2024-002',
    createdAt: '2024-03-20',
    expiresAt: '2024-12-20',
    lastAccess: '2024-12-05 15:45:00',
    features: ['inventory', 'basic-reports'],
    maxUsers: 3,
    usedUsers: 2,
    maxStorage: 5,
    usedStorage: 3.2,
    billingCycle: 'monthly',
    amount: 29,
    currency: 'USD'
  },
  {
    id: '3',
    companyName: 'Enterprise Corp',
    contactName: 'Michael Brown',
    email: '<EMAIL>',
    phone: '******-0789',
    domain: 'enterprise.com',
    plan: 'enterprise',
    status: 'active',
    licenseKey: 'EC-ENT-2024-003',
    createdAt: '2024-02-10',
    expiresAt: '2025-02-10',
    lastAccess: '2024-12-06 09:15:00',
    features: ['inventory', 'advanced-reports', 'tally-sync', 'api-access', 'multi-location', 'custom-fields'],
    maxUsers: 50,
    usedUsers: 35,
    maxStorage: 100,
    usedStorage: 67.8,
    billingCycle: 'yearly',
    amount: 1990,
    currency: 'USD'
  },
  {
    id: '4',
    companyName: 'Trial Company',
    contactName: 'Lisa Wilson',
    email: '<EMAIL>',
    phone: '******-0321',
    domain: 'trialcompany.com',
    plan: 'professional',
    status: 'trial',
    licenseKey: 'TC-TRL-2024-004',
    createdAt: '2024-11-20',
    expiresAt: '2024-12-20',
    lastAccess: '2024-12-06 11:00:00',
    features: ['inventory', 'reports', 'tally-sync'],
    maxUsers: 10,
    usedUsers: 3,
    maxStorage: 25,
    usedStorage: 2.1,
    billingCycle: 'monthly',
    amount: 0,
    currency: 'USD'
  }
];

export class ClientController {
  /**
   * Get all clients
   */
  async getAllClients(req: Request, res: Response): Promise<void> {
    try {
      const { search, status, plan } = req.query;

      let filteredClients = [...clients];

      // Apply search filter
      if (search) {
        const searchTerm = (search as string).toLowerCase();
        filteredClients = filteredClients.filter(client =>
          client.companyName.toLowerCase().includes(searchTerm) ||
          client.contactName.toLowerCase().includes(searchTerm) ||
          client.email.toLowerCase().includes(searchTerm) ||
          client.domain.toLowerCase().includes(searchTerm)
        );
      }

      // Apply status filter
      if (status && status !== 'all') {
        filteredClients = filteredClients.filter(client => client.status === status);
      }

      // Apply plan filter
      if (plan && plan !== 'all') {
        filteredClients = filteredClients.filter(client => client.plan === plan);
      }

      res.json({
        success: true,
        data: filteredClients,
        total: filteredClients.length,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get clients error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get clients',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get client by ID
   */
  async getClientById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const client = clients.find(c => c.id === id);

      if (!client) {
        res.status(404).json({
          success: false,
          message: 'Client not found'
        });
        return;
      }

      res.json({
        success: true,
        data: client,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get client error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get client',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Create new client
   */
  async createClient(req: Request, res: Response): Promise<void> {
    try {
      const clientData = req.body;

      // Generate license key
      const licenseKey = this.generateLicenseKey(clientData.companyName, clientData.plan);

      // Calculate expiry date
      const expiresAt = new Date();
      if (clientData.billingCycle === 'yearly') {
        expiresAt.setFullYear(expiresAt.getFullYear() + 1);
      } else {
        expiresAt.setMonth(expiresAt.getMonth() + 1);
      }

      const newClient: Client = {
        id: (clients.length + 1).toString(),
        ...clientData,
        licenseKey,
        createdAt: new Date().toISOString().split('T')[0],
        expiresAt: expiresAt.toISOString().split('T')[0],
        lastAccess: new Date().toISOString(),
        usedUsers: 0,
        usedStorage: 0
      };

      clients.push(newClient);

      res.status(201).json({
        success: true,
        data: newClient,
        message: 'Client created successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Create client error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create client',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Update client
   */
  async updateClient(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const clientIndex = clients.findIndex(c => c.id === id);
      if (clientIndex === -1) {
        res.status(404).json({
          success: false,
          message: 'Client not found'
        });
        return;
      }

      clients[clientIndex] = { ...clients[clientIndex], ...updateData };

      res.json({
        success: true,
        data: clients[clientIndex],
        message: 'Client updated successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Update client error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update client',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Extend client license
   */
  async extendLicense(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { months } = req.body;

      const clientIndex = clients.findIndex(c => c.id === id);
      if (clientIndex === -1) {
        res.status(404).json({
          success: false,
          message: 'Client not found'
        });
        return;
      }

      const client = clients[clientIndex];
      if (!client) {
        res.status(404).json({
          success: false,
          message: 'Client not found'
        });
        return;
      }

      const currentExpiry = new Date(client.expiresAt);
      const newExpiry = new Date(currentExpiry);
      newExpiry.setMonth(newExpiry.getMonth() + months);

      const updatedExpiryDate = newExpiry.toISOString().split('T')[0];
      if (!updatedExpiryDate) {
        res.status(500).json({
          success: false,
          message: 'Failed to calculate expiry date'
        });
        return;
      }

      clients[clientIndex] = {
        ...client,
        expiresAt: updatedExpiryDate,
        status: 'active'
      };

      res.json({
        success: true,
        data: clients[clientIndex],
        message: `License extended by ${months} months`,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Extend license error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to extend license',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Suspend client
   */
  async suspendClient(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const clientIndex = clients.findIndex(c => c.id === id);
      if (clientIndex === -1) {
        res.status(404).json({
          success: false,
          message: 'Client not found'
        });
        return;
      }

      const client = clients[clientIndex];
      if (!client) {
        res.status(404).json({
          success: false,
          message: 'Client not found'
        });
        return;
      }

      client.status = 'suspended';

      res.json({
        success: true,
        data: clients[clientIndex],
        message: 'Client suspended successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Suspend client error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to suspend client',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Activate client
   */
  async activateClient(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const clientIndex = clients.findIndex(c => c.id === id);
      if (clientIndex === -1) {
        res.status(404).json({
          success: false,
          message: 'Client not found'
        });
        return;
      }

      const client = clients[clientIndex];
      if (!client) {
        res.status(404).json({
          success: false,
          message: 'Client not found'
        });
        return;
      }

      client.status = 'active';

      res.json({
        success: true,
        data: clients[clientIndex],
        message: 'Client activated successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Activate client error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to activate client',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Send test email
   */
  async sendTestEmail(req: Request, res: Response): Promise<void> {
    try {
      const { email } = req.body;

      await emailNotificationService.sendTestEmail(email);

      res.json({
        success: true,
        message: 'Test email sent successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Send test email error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to send test email',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Trigger notification check
   */
  async triggerNotifications(_req: Request, res: Response): Promise<void> {
    try {
      await emailNotificationService.triggerNotificationCheck();

      res.json({
        success: true,
        message: 'Notification check triggered successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Trigger notifications error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to trigger notifications',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Generate license key
   */
  private generateLicenseKey(companyName: string, plan: string): string {
    const prefix = companyName.substring(0, 2).toUpperCase();
    const planCode = plan.substring(0, 3).toUpperCase();
    const year = new Date().getFullYear();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${prefix}-${planCode}-${year}-${random}`;
  }
}
