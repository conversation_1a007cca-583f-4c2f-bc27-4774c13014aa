import { Request, Response } from 'express';
import {
  PaymentGateway,
  PaymentGatewayType,
  CreatePaymentGatewayRequest,
  UpdatePaymentGatewayRequest,
  PaymentGatewayTestResponse,
  ProcessPaymentRequest,
  ProcessPaymentResponse,
  PaymentTransaction,
  PaymentSettings
} from '../types/payment';

export class PaymentGatewayController {
  // Sample data storage (replace with actual database)
  private gateways: PaymentGateway[] = [
    {
      id: '1',
      name: 'Stripe Production',
      type: 'stripe',
      enabled: true,
      isDefault: true,
      configuration: {
        publishableKey: 'pk_live_...',
        secretKey: 'sk_live_...',
        environment: 'production',
        currency: 'USD',
        minimumAmount: 50,
        maximumAmount: 100000
      },
      supportedCurrencies: ['USD', 'EUR', 'GBP'],
      supportedCountries: ['US', 'CA', 'GB', 'AU'],
      features: [
        { name: 'Credit Cards', enabled: true },
        { name: 'Digital Wallets', enabled: true },
        { name: 'Recurring Payments', enabled: true }
      ],
      status: 'active',
      lastTested: new Date('2024-01-15T10:30:00Z'),
      createdAt: new Date('2024-01-01T00:00:00Z'),
      updatedAt: new Date('2024-01-15T10:30:00Z')
    },
    {
      id: '2',
      name: 'PayPal Business',
      type: 'paypal',
      enabled: true,
      isDefault: false,
      configuration: {
        clientId: 'AeA1QIZXiflr8_...',
        clientSecret: 'EGnHDxD_qRPdaLdHgGlliB...',
        environment: 'production',
        currency: 'USD'
      },
      supportedCurrencies: ['USD', 'EUR', 'GBP'],
      supportedCountries: ['US', 'CA', 'GB', 'AU'],
      features: [
        { name: 'PayPal Payments', enabled: true },
        { name: 'Express Checkout', enabled: true }
      ],
      status: 'active',
      createdAt: new Date('2024-01-01T00:00:00Z'),
      updatedAt: new Date('2024-01-10T00:00:00Z')
    }
  ];

  private transactions: PaymentTransaction[] = [];
  private settings: PaymentSettings = {
    defaultGateway: '1',
    defaultCurrency: 'USD',
    allowedCurrencies: ['USD', 'EUR', 'GBP'],
    minimumAmount: 50,
    maximumAmount: 100000,
    autoCapture: true,
    requireBillingAddress: true,
    requireShippingAddress: false,
    enableSaveCard: true,
    enableRecurringPayments: true,
    paymentTimeout: 30,
    retryFailedPayments: true,
    maxRetryAttempts: 3,
    notificationSettings: {
      emailOnSuccess: true,
      emailOnFailure: true,
      smsOnSuccess: false,
      smsOnFailure: false
    }
  };

  async getAllGateways(_req: Request, res: Response): Promise<void> {
    res.json({
      success: true,
      data: this.gateways,
      total: this.gateways.length
    });
  }

  async getGatewayById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const gateway = this.gateways.find(g => g.id === id);

    if (!gateway) {
      res.status(404).json({
        success: false,
        message: 'Payment gateway not found'
      });
      return;
    }

    res.json({
      success: true,
      data: gateway
    });
  }

  async createGateway(req: Request, res: Response): Promise<void> {
    const gatewayData: CreatePaymentGatewayRequest = req.body;

    // Validate required fields
    if (!gatewayData.name || !gatewayData.type) {
      res.status(400).json({
        success: false,
        message: 'Name and type are required'
      });
      return;
    }

    // If setting as default, update other gateways
    if (gatewayData.isDefault) {
      this.gateways = this.gateways.map(g => ({ ...g, isDefault: false }));
    }

    const newGateway: PaymentGateway = {
      id: (this.gateways.length + 1).toString(),
      ...gatewayData,
      status: 'inactive',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.gateways.push(newGateway);

    res.status(201).json({
      success: true,
      data: newGateway,
      message: 'Payment gateway created successfully'
    });
  }

  async updateGateway(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const updateData: Partial<UpdatePaymentGatewayRequest> = req.body;

    const gatewayIndex = this.gateways.findIndex(g => g.id === id);
    if (gatewayIndex === -1) {
      res.status(404).json({
        success: false,
        message: 'Payment gateway not found'
      });
      return;
    }

    // If setting as default, update other gateways
    if (updateData.isDefault) {
      this.gateways = this.gateways.map(g => ({ ...g, isDefault: false }));
    }

    this.gateways[gatewayIndex] = {
      ...this.gateways[gatewayIndex],
      ...updateData,
      updatedAt: new Date()
    };

    res.json({
      success: true,
      data: this.gateways[gatewayIndex],
      message: 'Payment gateway updated successfully'
    });
  }

  async deleteGateway(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const gatewayIndex = this.gateways.findIndex(g => g.id === id);

    if (gatewayIndex === -1) {
      res.status(404).json({
        success: false,
        message: 'Payment gateway not found'
      });
      return;
    }

    // Check if it's the default gateway
    if (this.gateways[gatewayIndex].isDefault) {
      res.status(400).json({
        success: false,
        message: 'Cannot delete the default payment gateway'
      });
      return;
    }

    this.gateways.splice(gatewayIndex, 1);

    res.json({
      success: true,
      message: 'Payment gateway deleted successfully'
    });
  }

  async testGateway(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const gateway = this.gateways.find(g => g.id === id);

    if (!gateway) {
      res.status(404).json({
        success: false,
        message: 'Payment gateway not found'
      });
      return;
    }

    // Simulate gateway testing
    await new Promise(resolve => setTimeout(resolve, 2000));

    const testResult: PaymentGatewayTestResponse = {
      success: true,
      gatewayId: id,
      testResults: {
        connection: true,
        authentication: true,
        webhook: true,
        testTransaction: true
      },
      message: 'All tests passed successfully'
    };

    // Update gateway status and last tested time
    const gatewayIndex = this.gateways.findIndex(g => g.id === id);
    this.gateways[gatewayIndex] = {
      ...this.gateways[gatewayIndex],
      status: 'active',
      lastTested: new Date()
    };

    res.json({
      success: true,
      data: testResult
    });
  }

  async toggleGateway(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const gatewayIndex = this.gateways.findIndex(g => g.id === id);

    if (gatewayIndex === -1) {
      res.status(404).json({
        success: false,
        message: 'Payment gateway not found'
      });
      return;
    }

    this.gateways[gatewayIndex] = {
      ...this.gateways[gatewayIndex],
      enabled: !this.gateways[gatewayIndex].enabled,
      updatedAt: new Date()
    };

    res.json({
      success: true,
      data: this.gateways[gatewayIndex],
      message: `Payment gateway ${this.gateways[gatewayIndex].enabled ? 'enabled' : 'disabled'} successfully`
    });
  }

  async setDefaultGateway(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const gateway = this.gateways.find(g => g.id === id);

    if (!gateway) {
      res.status(404).json({
        success: false,
        message: 'Payment gateway not found'
      });
      return;
    }

    // Update all gateways
    this.gateways = this.gateways.map(g => ({
      ...g,
      isDefault: g.id === id,
      updatedAt: new Date()
    }));

    res.json({
      success: true,
      message: 'Default payment gateway updated successfully'
    });
  }

  async processPayment(req: Request, res: Response): Promise<void> {
    const paymentData: ProcessPaymentRequest = req.body;

    // Validate payment data
    if (!paymentData.gatewayId || !paymentData.amount || !paymentData.currency) {
      res.status(400).json({
        success: false,
        message: 'Gateway ID, amount, and currency are required'
      });
      return;
    }

    const gateway = this.gateways.find(g => g.id === paymentData.gatewayId);
    if (!gateway || !gateway.enabled) {
      res.status(400).json({
        success: false,
        message: 'Payment gateway not found or disabled'
      });
      return;
    }

    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 1500));

    const transaction: PaymentTransaction = {
      id: (this.transactions.length + 1).toString(),
      gatewayId: paymentData.gatewayId,
      gatewayTransactionId: `txn_${Date.now()}`,
      clientId: paymentData.clientId,
      amount: paymentData.amount,
      currency: paymentData.currency,
      status: 'completed',
      paymentMethod: paymentData.paymentMethod,
      description: paymentData.description,
      metadata: paymentData.metadata,
      processedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.transactions.push(transaction);

    const response: ProcessPaymentResponse = {
      success: true,
      transactionId: transaction.id,
      gatewayTransactionId: transaction.gatewayTransactionId,
      status: transaction.status,
      amount: transaction.amount,
      currency: transaction.currency,
      paymentMethod: transaction.paymentMethod,
      message: 'Payment processed successfully'
    };

    res.json({
      success: true,
      data: response
    });
  }

  async refundPayment(req: Request, res: Response): Promise<void> {
    const { transactionId } = req.params;
    const { amount } = req.body;

    const transaction = this.transactions.find(t => t.id === transactionId);
    if (!transaction) {
      res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
      return;
    }

    // Simulate refund processing
    await new Promise(resolve => setTimeout(resolve, 1000));

    const refundAmount = amount || transaction.amount;
    const isPartialRefund = refundAmount < transaction.amount;

    // Update transaction status
    const transactionIndex = this.transactions.findIndex(t => t.id === transactionId);
    this.transactions[transactionIndex] = {
      ...this.transactions[transactionIndex],
      status: isPartialRefund ? 'partially_refunded' : 'refunded',
      updatedAt: new Date()
    };

    res.json({
      success: true,
      data: {
        transactionId,
        refundAmount,
        status: isPartialRefund ? 'partially_refunded' : 'refunded',
        message: 'Refund processed successfully'
      }
    });
  }

  async getTransactions(req: Request, res: Response): Promise<void> {
    const { page = 1, limit = 10, status, gatewayId } = req.query;
    
    let filteredTransactions = this.transactions;

    if (status) {
      filteredTransactions = filteredTransactions.filter(t => t.status === status);
    }

    if (gatewayId) {
      filteredTransactions = filteredTransactions.filter(t => t.gatewayId === gatewayId);
    }

    const startIndex = (Number(page) - 1) * Number(limit);
    const endIndex = startIndex + Number(limit);
    const paginatedTransactions = filteredTransactions.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: paginatedTransactions,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: filteredTransactions.length,
        pages: Math.ceil(filteredTransactions.length / Number(limit))
      }
    });
  }

  async getTransactionById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const transaction = this.transactions.find(t => t.id === id);

    if (!transaction) {
      res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
      return;
    }

    res.json({
      success: true,
      data: transaction
    });
  }

  async handleWebhook(req: Request, res: Response): Promise<void> {
    const { gatewayType } = req.params;
    const payload = req.body;

    // Log webhook for debugging
    console.log(`Received webhook from ${gatewayType}:`, payload);

    // Process webhook based on gateway type
    // This would contain gateway-specific webhook handling logic

    res.json({
      success: true,
      message: 'Webhook processed successfully'
    });
  }

  async getPaymentSettings(_req: Request, res: Response): Promise<void> {
    res.json({
      success: true,
      data: this.settings
    });
  }

  async updatePaymentSettings(req: Request, res: Response): Promise<void> {
    const updateData: Partial<PaymentSettings> = req.body;

    this.settings = {
      ...this.settings,
      ...updateData
    };

    res.json({
      success: true,
      data: this.settings,
      message: 'Payment settings updated successfully'
    });
  }

  async getGatewayTemplates(_req: Request, res: Response): Promise<void> {
    // Return gateway templates/configurations
    const templates = [
      {
        type: 'stripe',
        name: 'Stripe',
        description: 'Accept payments online with Stripe',
        requiredFields: ['publishableKey', 'secretKey', 'environment'],
        optionalFields: ['webhookSecret']
      },
      {
        type: 'paypal',
        name: 'PayPal',
        description: 'Accept payments through PayPal',
        requiredFields: ['clientId', 'clientSecret', 'environment'],
        optionalFields: ['webhookId']
      }
    ];

    res.json({
      success: true,
      data: templates
    });
  }

  async validateGatewayConfig(req: Request, res: Response): Promise<void> {
    const { type, configuration } = req.body;

    // Validate configuration based on gateway type
    let isValid = true;
    const errors: string[] = [];

    if (type === 'stripe') {
      if (!configuration.publishableKey) errors.push('Publishable key is required');
      if (!configuration.secretKey) errors.push('Secret key is required');
    } else if (type === 'paypal') {
      if (!configuration.clientId) errors.push('Client ID is required');
      if (!configuration.clientSecret) errors.push('Client secret is required');
    }

    isValid = errors.length === 0;

    res.json({
      success: true,
      data: {
        valid: isValid,
        errors
      }
    });
  }

  async getPaymentAnalytics(req: Request, res: Response): Promise<void> {
    // Generate sample analytics data
    const analytics = {
      totalTransactions: this.transactions.length,
      totalAmount: this.transactions.reduce((sum, t) => sum + t.amount, 0),
      successRate: 95.5,
      averageTransactionAmount: this.transactions.length > 0 
        ? this.transactions.reduce((sum, t) => sum + t.amount, 0) / this.transactions.length 
        : 0,
      gatewayPerformance: this.gateways.map(g => ({
        gatewayId: g.id,
        name: g.name,
        transactions: this.transactions.filter(t => t.gatewayId === g.id).length,
        amount: this.transactions.filter(t => t.gatewayId === g.id).reduce((sum, t) => sum + t.amount, 0)
      }))
    };

    res.json({
      success: true,
      data: analytics
    });
  }

  async getTransactionReport(req: Request, res: Response): Promise<void> {
    const { startDate, endDate, format = 'json' } = req.query;

    // Filter transactions by date range if provided
    let filteredTransactions = this.transactions;

    if (startDate) {
      filteredTransactions = filteredTransactions.filter(
        t => t.createdAt >= new Date(startDate as string)
      );
    }

    if (endDate) {
      filteredTransactions = filteredTransactions.filter(
        t => t.createdAt <= new Date(endDate as string)
      );
    }

    const report = {
      period: {
        startDate: startDate || 'All time',
        endDate: endDate || 'All time'
      },
      summary: {
        totalTransactions: filteredTransactions.length,
        totalAmount: filteredTransactions.reduce((sum, t) => sum + t.amount, 0),
        successfulTransactions: filteredTransactions.filter(t => t.status === 'completed').length,
        failedTransactions: filteredTransactions.filter(t => t.status === 'failed').length
      },
      transactions: filteredTransactions
    };

    res.json({
      success: true,
      data: report
    });
  }
}
