import { Router } from 'express';
import { ClientController } from '../controllers/clientController';
import { validateLicense, checkUsageLimits, getLicenseInfo } from '../middleware/licenseValidation';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const clientController = new ClientController();

// Apply authentication middleware to all routes
router.use(authenticateToken);

/**
 * @route GET /api/clients
 * @desc Get all clients with optional filtering
 * @access Private (Admin only)
 * @query search - Search term for company, contact, email, or domain
 * @query status - Filter by status (active, trial, suspended, expired)
 * @query plan - Filter by plan (starter, professional, enterprise, custom)
 */
router.get('/', (req, res) => {
  clientController.getAllClients(req, res);
});

/**
 * @route GET /api/clients/:id
 * @desc Get client by ID
 * @access Private (Admin only)
 */
router.get('/:id', (req, res) => {
  clientController.getClientById(req, res);
});

/**
 * @route POST /api/clients
 * @desc Create new client
 * @access Private (Admin only)
 * @body {
 *   companyName: string,
 *   contactName: string,
 *   email: string,
 *   phone: string,
 *   domain: string,
 *   plan: 'starter' | 'professional' | 'enterprise' | 'custom',
 *   billingCycle: 'monthly' | 'yearly',
 *   amount: number,
 *   currency: string,
 *   features: string[]
 * }
 */
router.post('/', (req, res) => {
  clientController.createClient(req, res);
});

/**
 * @route PUT /api/clients/:id
 * @desc Update client
 * @access Private (Admin only)
 */
router.put('/:id', (req, res) => {
  clientController.updateClient(req, res);
});

/**
 * @route POST /api/clients/:id/extend
 * @desc Extend client license
 * @access Private (Admin only)
 * @body { months: number }
 */
router.post('/:id/extend', (req, res) => {
  clientController.extendLicense(req, res);
});

/**
 * @route POST /api/clients/:id/suspend
 * @desc Suspend client
 * @access Private (Admin only)
 */
router.post('/:id/suspend', (req, res) => {
  clientController.suspendClient(req, res);
});

/**
 * @route POST /api/clients/:id/activate
 * @desc Activate client
 * @access Private (Admin only)
 */
router.post('/:id/activate', (req, res) => {
  clientController.activateClient(req, res);
});

/**
 * @route POST /api/clients/test-email
 * @desc Send test email
 * @access Private (Admin only)
 * @body { email: string }
 */
router.post('/test-email', (req, res) => {
  clientController.sendTestEmail(req, res);
});

/**
 * @route POST /api/clients/trigger-notifications
 * @desc Manually trigger notification check
 * @access Private (Admin only)
 */
router.post('/trigger-notifications', (req, res) => {
  clientController.triggerNotifications(req, res);
});

// License validation routes (for client applications)

/**
 * @route GET /api/clients/license/info
 * @desc Get license information for the requesting domain
 * @access Public (with license validation)
 * @headers x-license-key - License key for validation
 */
router.get('/license/info', validateLicense(), getLicenseInfo);

/**
 * @route GET /api/clients/license/validate
 * @desc Validate license and domain
 * @access Public (with license validation)
 * @headers x-license-key - License key for validation
 */
router.get('/license/validate', validateLicense(), (_req, res) => {
  res.json({
    success: true,
    message: 'License is valid',
    timestamp: new Date().toISOString()
  });
});

/**
 * @route GET /api/clients/license/validate-feature/:feature
 * @desc Validate license and check feature access
 * @access Public (with license validation)
 * @headers x-license-key - License key for validation
 */
router.get('/license/validate-feature/:feature', (req, res, next) => {
  const { feature } = req.params;
  validateLicense(feature)(req, res, next);
}, (req, res) => {
  res.json({
    success: true,
    message: `Feature '${req.params.feature}' is available`,
    timestamp: new Date().toISOString()
  });
});

/**
 * @route POST /api/clients/license/check-usage/:type
 * @desc Check usage limits (users or storage)
 * @access Public (with license validation)
 * @headers x-license-key - License key for validation
 * @params type - 'users' or 'storage'
 */
router.post('/license/check-usage/:type', validateLicense(), (req, res, next): any => {
  const { type } = req.params;
  if (type !== 'users' && type !== 'storage') {
    return res.status(400).json({
      success: false,
      message: 'Invalid usage type. Must be "users" or "storage"'
    });
  }
  return checkUsageLimits(type as 'users' | 'storage')(req, res, next);
}, (req, res) => {
  res.json({
    success: true,
    message: `${req.params['type']} limit check passed`,
    timestamp: new Date().toISOString()
  });
});

export default router;
