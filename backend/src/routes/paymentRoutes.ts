import { Router, Request, Response } from 'express';
import { authenticateToken, requireRole } from '../middleware/auth';
import { PaymentGatewayController } from '../controllers/paymentController';

const router = Router();
const paymentController = new PaymentGatewayController();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Payment Gateway Management Routes
router.get('/gateways', requireRole(['admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const gateways = await paymentController.getAllGateways(req, res);
    return gateways;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch payment gateways',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.get('/gateways/:id', requireRole(['admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const gateway = await paymentController.getGatewayById(req, res);
    return gateway;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch payment gateway',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.post('/gateways', requireRole(['admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const gateway = await paymentController.createGateway(req, res);
    return gateway;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to create payment gateway',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.put('/gateways/:id', requireRole(['admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const gateway = await paymentController.updateGateway(req, res);
    return gateway;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to update payment gateway',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.delete('/gateways/:id', requireRole(['admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const result = await paymentController.deleteGateway(req, res);
    return result;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to delete payment gateway',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Gateway Testing Routes
router.post('/gateways/:id/test', requireRole(['admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const result = await paymentController.testGateway(req, res);
    return result;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to test payment gateway',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.post('/gateways/:id/toggle', requireRole(['admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const result = await paymentController.toggleGateway(req, res);
    return result;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to toggle payment gateway',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.post('/gateways/:id/set-default', requireRole(['admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const result = await paymentController.setDefaultGateway(req, res);
    return result;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to set default payment gateway',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Payment Processing Routes
router.post('/process', requireRole(['admin', 'client_admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const result = await paymentController.processPayment(req, res);
    return result;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to process payment',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.post('/refund/:transactionId', requireRole(['admin', 'client_admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const result = await paymentController.refundPayment(req, res);
    return result;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to process refund',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Transaction Management Routes
router.get('/transactions', requireRole(['admin', 'client_admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const transactions = await paymentController.getTransactions(req, res);
    return transactions;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch transactions',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.get('/transactions/:id', requireRole(['admin', 'client_admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const transaction = await paymentController.getTransactionById(req, res);
    return transaction;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch transaction',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Webhook Routes (no authentication required)
router.post('/webhooks/:gatewayType', async (req: Request, res: Response): Promise<any> => {
  try {
    const result = await paymentController.handleWebhook(req, res);
    return result;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to process webhook',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Payment Settings Routes
router.get('/settings', requireRole(['admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const settings = await paymentController.getPaymentSettings(req, res);
    return settings;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch payment settings',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.put('/settings', requireRole(['admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const settings = await paymentController.updatePaymentSettings(req, res);
    return settings;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to update payment settings',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Gateway Templates and Configuration
router.get('/gateway-templates', requireRole(['admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const templates = await paymentController.getGatewayTemplates(req, res);
    return templates;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch gateway templates',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.post('/validate-config', requireRole(['admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const result = await paymentController.validateGatewayConfig(req, res);
    return result;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to validate gateway configuration',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Analytics and Reports
router.get('/analytics', requireRole(['admin', 'client_admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const analytics = await paymentController.getPaymentAnalytics(req, res);
    return analytics;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch payment analytics',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.get('/reports/transactions', requireRole(['admin', 'client_admin']), async (req: Request, res: Response): Promise<any> => {
  try {
    const report = await paymentController.getTransactionReport(req, res);
    return report;
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to generate transaction report',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
