import { Router } from 'express';
import { ApiResponse } from '../../../shared/src';

const router = Router();

// Simple health check for auth routes
router.get('/health', (_req, res) => {
  const response: ApiResponse = {
    success: true,
    message: 'Auth routes are working',
    timestamp: new Date().toISOString(),
  };
  res.status(200).json(response);
});

// POST /api/auth/login - Simple login endpoint
router.post('/login', (req, res) => {
  const { email, password } = req.body;
  
  // Simple demo authentication
  if (email === '<EMAIL>' && password === 'admin123') {
    const response: ApiResponse = {
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: '1',
          email: '<EMAIL>',
          username: 'admin',
          firstName: 'Admin',
          lastName: 'User',
          role: 'ADMIN',
          isActive: true,
          lastLogin: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        token: 'demo-jwt-token-12345',
      },
      timestamp: new Date().toISOString(),
    };
    return res.status(200).json(response);
  }
  
  return res.status(401).json({
    success: false,
    message: 'Invalid email or password',
    timestamp: new Date().toISOString(),
  });
});

// POST /api/auth/register - Simple register endpoint
router.post('/register', (req, res) => {
  const { email, username, firstName, lastName } = req.body;
  
  const response: ApiResponse = {
    success: true,
    message: 'User registered successfully',
    data: {
      user: {
        id: '2',
        email,
        username,
        firstName,
        lastName,
        role: 'USER',
        isActive: true,
        lastLogin: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      token: 'demo-jwt-token-67890',
    },
    timestamp: new Date().toISOString(),
  };
  
  res.status(201).json(response);
});

// POST /api/auth/logout - Logout endpoint
router.post('/logout', (_req, res) => {
  const response: ApiResponse = {
    success: true,
    message: 'Logout successful',
    timestamp: new Date().toISOString(),
  };
  res.status(200).json(response);
});

// GET /api/auth/me - Get current user
router.get('/me', (req, res) => {
  const authHeader = req.headers['authorization'];
  
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'Authorization header required',
      timestamp: new Date().toISOString(),
    });
  }
  
  const response: ApiResponse = {
    success: true,
    message: 'User profile retrieved successfully',
    data: {
      user: {
        id: '1',
        email: '<EMAIL>',
        username: 'admin',
        firstName: 'Admin',
        lastName: 'User',
        role: 'ADMIN',
        isActive: true,
        lastLogin: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    },
    timestamp: new Date().toISOString(),
  };

  return res.status(200).json(response);
});

export default router;
