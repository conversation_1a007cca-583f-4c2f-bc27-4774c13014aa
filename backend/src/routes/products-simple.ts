import { Router } from 'express';
import { ApiResponse } from '../../../shared/src';
import db from '../db/connection';

const router = Router();

// GET /api/products - Get all products
router.get('/', async (_req, res) => {
  try {
    const result = await db.getProducts();
    
    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch products',
        error: (result as any).error,
        timestamp: new Date().toISOString(),
      });
    }

    const response: ApiResponse = {
      success: true,
      message: 'Products retrieved successfully',
      data: result.data,
      timestamp: new Date().toISOString(),
    };

    return res.json(response);
  } catch (error) {
    console.error('Error fetching products:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString(),
    });
  }
});

// GET /api/products/:id - Get product by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await db.query('SELECT * FROM products WHERE id = $1', [id]);
    
    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch product',
        error: (result as any).error,
        timestamp: new Date().toISOString(),
      });
    }

    if (!result.data || result.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Product not found',
        timestamp: new Date().toISOString(),
      });
    }

    const response: ApiResponse = {
      success: true,
      message: 'Product retrieved successfully',
      data: result.data[0],
      timestamp: new Date().toISOString(),
    };

    return res.json(response);
  } catch (error) {
    console.error('Error fetching product:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString(),
    });
  }
});

// POST /api/products - Create new product
router.post('/', async (req, res) => {
  try {
    const { name, description, sku, price, cost, category, brand } = req.body;

    // Basic validation
    if (!name || !sku || price === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Name, SKU, and price are required',
        timestamp: new Date().toISOString(),
      });
    }

    const result = await db.createProduct({
      name,
      description: description || '',
      sku,
      price: parseFloat(price) || 0,
      cost: parseFloat(cost) || 0,
      category: category || '',
      brand: brand || ''
    });

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to create product',
        error: (result as any).error,
        timestamp: new Date().toISOString(),
      });
    }

    const response: ApiResponse = {
      success: true,
      message: 'Product created successfully',
      data: result.data,
      timestamp: new Date().toISOString(),
    };

    return res.status(201).json(response);
  } catch (error) {
    console.error('Error creating product:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString(),
    });
  }
});

// GET /api/products/health - Health check for products API
router.get('/health', (_req, res) => {
  const response: ApiResponse = {
    success: true,
    message: 'Products API is working',
    timestamp: new Date().toISOString(),
  };
  return res.json(response);
});

export default router;
