import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { authenticateToken } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';
import { ApiResponse, PaginatedResponse } from '../../../shared/src';

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const createProductSchema = z.object({
  sku: z.string().min(1, 'SKU is required'),
  name: z.string().min(1, 'Product name is required'),
  description: z.string().optional(),
  categoryId: z.string().uuid('Invalid category ID'),
  brandId: z.string().uuid('Invalid brand ID').optional(),
  type: z.enum(['SIMPLE', 'VARIABLE', 'GROUPED', 'EXTERNAL']),
  status: z.enum(['ACTIVE', 'INACTIVE', 'DRAFT', 'ARCHIVED']),
  costPrice: z.number().min(0, 'Cost price must be positive'),
  sellingPrice: z.number().min(0, 'Selling price must be positive'),
  mrp: z.number().min(0, 'MRP must be positive'),
  weight: z.number().min(0).optional(),
  dimensions: z.string().optional(),
  trackInventory: z.boolean(),
  lowStockAlert: z.number().min(0).optional(),
});

const updateProductSchema = createProductSchema.partial();

const querySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().min(1)).optional(),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).optional(),
  search: z.string().optional(),
  category: z.string().uuid().optional(),
  brand: z.string().uuid().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'DRAFT', 'ARCHIVED']).optional(),
  type: z.enum(['SIMPLE', 'VARIABLE', 'GROUPED', 'EXTERNAL']).optional(),
  sortBy: z.enum(['name', 'sku', 'createdAt', 'updatedAt', 'sellingPrice']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

// GET /api/products - Get all products with pagination and filters
router.get('/', authenticateToken, validateRequest({ query: querySchema }), async (req, res) => {
  try {
    const {
      page = '1',
      limit = '20',
      search,
      category,
      brand,
      status,
      type,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Convert string parameters to numbers
    const pageNum = parseInt(page as string, 10) || 1;
    const limitNum = parseInt(limit as string, 10) || 20;
    const sortByStr = sortBy as string;
    const sortOrderStr = sortOrder as string;

    const skip = (pageNum - 1) * limitNum;

    // Build where clause
    const where: any = {};
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { sku: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (category) where.categoryId = category;
    if (brand) where.brandId = brand;
    if (status) where.status = status;
    if (type) where.type = type;

    // Get total count
    const total = await prisma.product.count({ where });

    // Get products
    const products = await prisma.product.findMany({
      where,
      skip,
      take: limitNum,
      orderBy: { [sortByStr]: sortOrderStr },
      include: {
        category: {
          select: { id: true, name: true }
        },
        brand: {
          select: { id: true, name: true }
        },
        createdBy: {
          select: { id: true, firstName: true, lastName: true }
        }
      }
    });

    const totalPages = Math.ceil(total / limitNum);

    const response: PaginatedResponse = {
      success: true,
      message: 'Products retrieved successfully',
      data: products,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages,
        hasNext: pageNum < totalPages,
        hasPrev: pageNum > 1,
      },
      timestamp: new Date().toISOString(),
    };

    return res.json(response);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch products',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    });
  }
});

// GET /api/products/:id - Get product by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const product = await prisma.product.findUnique({
      where: { id: id },
      include: {
        category: {
          select: { id: true, name: true }
        },
        brand: {
          select: { id: true, name: true }
        },
        createdBy: {
          select: { id: true, firstName: true, lastName: true }
        }
      }
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found',
        timestamp: new Date().toISOString(),
      });
    }

    const response: ApiResponse = {
      success: true,
      message: 'Product retrieved successfully',
      data: product,
      timestamp: new Date().toISOString(),
    };

    return res.json(response);
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    });
  }
});

// POST /api/products - Create new product
router.post('/', authenticateToken, validateRequest({ body: createProductSchema }), async (req, res) => {
  try {
    const productData = req.body;
    const userId = req.user?.id;

    // Check if SKU already exists
    const existingProduct = await prisma.product.findUnique({
      where: { sku: productData.sku }
    });

    if (existingProduct) {
      return res.status(400).json({
        success: false,
        message: 'Product with this SKU already exists',
        timestamp: new Date().toISOString(),
      });
    }

    // Verify category exists
    const category = await prisma.category.findUnique({
      where: { id: productData.categoryId }
    });

    if (!category) {
      return res.status(400).json({
        success: false,
        message: 'Category not found',
        timestamp: new Date().toISOString(),
      });
    }

    // Verify brand exists (if provided)
    if (productData.brandId) {
      const brand = await prisma.brand.findUnique({
        where: { id: productData.brandId }
      });

      if (!brand) {
        return res.status(400).json({
          success: false,
          message: 'Brand not found',
          timestamp: new Date().toISOString(),
        });
      }
    }

    const product = await prisma.product.create({
      data: {
        ...productData,
        createdById: userId,
      },
      include: {
        category: {
          select: { id: true, name: true }
        },
        brand: {
          select: { id: true, name: true }
        },
        createdBy: {
          select: { id: true, firstName: true, lastName: true }
        }
      }
    });

    const response: ApiResponse = {
      success: true,
      message: 'Product created successfully',
      data: product,
      timestamp: new Date().toISOString(),
    };

    return res.status(201).json(response);
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create product',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    });
  }
});

// PUT /api/products/:id - Update product
router.put('/:id', authenticateToken, validateRequest({ body: updateProductSchema }), async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: id }
    });

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: 'Product not found',
        timestamp: new Date().toISOString(),
      });
    }

    // Check if SKU already exists (if updating SKU)
    if (updateData.sku && updateData.sku !== existingProduct.sku) {
      const skuExists = await prisma.product.findUnique({
        where: { sku: updateData.sku }
      });

      if (skuExists) {
        return res.status(400).json({
          success: false,
          message: 'Product with this SKU already exists',
          timestamp: new Date().toISOString(),
        });
      }
    }

    const product = await prisma.product.update({
      where: { id: id },
      data: updateData,
      include: {
        category: {
          select: { id: true, name: true }
        },
        brand: {
          select: { id: true, name: true }
        },
        createdBy: {
          select: { id: true, firstName: true, lastName: true }
        }
      }
    });

    const response: ApiResponse = {
      success: true,
      message: 'Product updated successfully',
      data: product,
      timestamp: new Date().toISOString(),
    };

    return res.json(response);
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update product',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    });
  }
});

// DELETE /api/products/:id - Delete product
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: id }
    });

    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: 'Product not found',
        timestamp: new Date().toISOString(),
      });
    }

    await prisma.product.delete({
      where: { id: id }
    });

    const response: ApiResponse = {
      success: true,
      message: 'Product deleted successfully',
      timestamp: new Date().toISOString(),
    };

    return res.json(response);
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete product',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    });
  }
});

export default router;
