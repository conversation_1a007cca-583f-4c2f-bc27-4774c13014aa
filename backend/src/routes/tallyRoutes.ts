import { Router } from 'express';
import { TallyController } from '../controllers/tallyController';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const tallyController = new TallyController();

// Apply authentication middleware to all routes
router.use(authenticateToken);

/**
 * @route POST /api/tally/test-connection
 * @desc Test connection to Tally server
 * @access Private
 */
router.post('/test-connection', (req, res) => {
  tallyController.testConnection(req, res);
});

/**
 * @route GET /api/tally/company-info
 * @desc Get Tally company information
 * @access Private
 */
router.get('/company-info', (req, res) => {
  tallyController.getCompanyInfo(req, res);
});

/**
 * @route POST /api/tally/manual-sync
 * @desc Perform manual synchronization with Tally
 * @access Private
 */
router.post('/manual-sync', (req, res) => {
  tallyController.manualSync(req, res);
});

/**
 * @route GET /api/tally/sync-status
 * @desc Get synchronization status
 * @access Private
 */
router.get('/sync-status', (req, res) => {
  tallyController.getSyncStatus(req, res);
});

/**
 * @route GET /api/tally/sync-logs
 * @desc Get synchronization logs
 * @access Private
 */
router.get('/sync-logs', (req, res) => {
  tallyController.getSyncLogs(req, res);
});

/**
 * @route POST /api/tally/schedule-sync
 * @desc Schedule automatic synchronization
 * @access Private
 */
router.post('/schedule-sync', (req, res) => {
  tallyController.scheduleSync(req, res);
});

/**
 * @route POST /api/tally/ledger
 * @desc Create ledger in Tally
 * @access Private
 */
router.post('/ledger', (req, res) => {
  tallyController.createLedger(req, res);
});

/**
 * @route POST /api/tally/voucher
 * @desc Create voucher in Tally
 * @access Private
 */
router.post('/voucher', (req, res) => {
  tallyController.createVoucher(req, res);
});

/**
 * @route POST /api/tally/stock-item
 * @desc Create stock item in Tally
 * @access Private
 */
router.post('/stock-item', (req, res) => {
  tallyController.createStockItem(req, res);
});

export default router;
