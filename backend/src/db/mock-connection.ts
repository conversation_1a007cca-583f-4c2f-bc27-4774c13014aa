// Mock database connection for testing when PostgreSQL auth fails
// This simulates database operations with in-memory data

interface User {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  role: string;
  is_active: boolean;
  created_at: Date;
}

interface Product {
  id: string;
  name: string;
  description: string;
  sku: string;
  price: number;
  cost: number;
  category: string;
  brand: string;
  created_at: Date;
}

// Mock data
const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    username: 'admin',
    first_name: 'Admin',
    last_name: 'User',
    role: 'ADMIN',
    is_active: true,
    created_at: new Date(),
  },
  {
    id: '2',
    email: '<EMAIL>',
    username: 'user',
    first_name: 'Test',
    last_name: 'User',
    role: 'USER',
    is_active: true,
    created_at: new Date(),
  }
];

const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Test Product 1',
    description: 'A test product for the inventory system',
    sku: 'TEST-001',
    price: 99.99,
    cost: 50.00,
    category: 'Electronics',
    brand: 'TestBrand',
    created_at: new Date(),
  },
  {
    id: '2',
    name: 'Test Product 2',
    description: 'Another test product',
    sku: 'TEST-002',
    price: 149.99,
    cost: 75.00,
    category: 'Electronics',
    brand: 'TestBrand',
    created_at: new Date(),
  },
  {
    id: '3',
    name: 'Sample Widget',
    description: 'A sample widget for testing',
    sku: 'WIDGET-001',
    price: 29.99,
    cost: 15.00,
    category: 'Widgets',
    brand: 'SampleCorp',
    created_at: new Date(),
  }
];

export const mockDb = {
  // Test connection
  async testConnection() {
    try {
      // Simulate connection test
      await new Promise(resolve => setTimeout(resolve, 100));
      return { 
        success: true, 
        data: { 
          current_time: new Date().toISOString(),
          message: 'Mock database connection successful'
        } 
      };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  // Get users
  async getUsers() {
    try {
      await new Promise(resolve => setTimeout(resolve, 50));
      return { success: true, data: mockUsers };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  // Get user by email
  async getUserByEmail(email: string) {
    try {
      await new Promise(resolve => setTimeout(resolve, 50));
      const user = mockUsers.find(u => u.email === email);
      return { success: true, data: user || null };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  // Get products
  async getProducts() {
    try {
      await new Promise(resolve => setTimeout(resolve, 50));
      return { success: true, data: mockProducts };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  // Create product
  async createProduct(product: any) {
    try {
      await new Promise(resolve => setTimeout(resolve, 100));
      const newProduct: Product = {
        id: (mockProducts.length + 1).toString(),
        name: product.name,
        description: product.description || '',
        sku: product.sku,
        price: parseFloat(product.price) || 0,
        cost: parseFloat(product.cost) || 0,
        category: product.category || '',
        brand: product.brand || '',
        created_at: new Date(),
      };
      mockProducts.push(newProduct);
      return { success: true, data: newProduct };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  // Raw query method
  async query(text: string, params?: any[]) {
    try {
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // Simulate some common queries
      if (text.includes('SELECT COUNT(*) as count FROM users')) {
        return { success: true, data: [{ count: mockUsers.length }] };
      }
      
      if (text.includes('SELECT COUNT(*) as count FROM products')) {
        return { success: true, data: [{ count: mockProducts.length }] };
      }
      
      if (text.includes('SELECT * FROM products WHERE id = $1')) {
        const id = params?.[0];
        const product = mockProducts.find(p => p.id === id);
        return { success: true, data: product ? [product] : [] };
      }
      
      // Default response
      return { success: true, data: [] };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
};

export default mockDb;
