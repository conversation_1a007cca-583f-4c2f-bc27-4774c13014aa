// Simple database connection without <PERSON><PERSON><PERSON> for now
import { Pool } from 'pg';
import { mockDb } from './mock-connection';

// Create a connection pool with explicit configuration
const pool = new Pool({
  host: process.env['DB_HOST'] || 'localhost',
  port: parseInt(process.env['DB_PORT'] || '5432'),
  database: process.env['DB_NAME'] || 'inventory_db',
  user: process.env['DB_USER'] || 'postgres',
  password: process.env['DB_PASSWORD'] || 'postgres_password',
  // Add connection options to handle authentication issues
  ssl: false,
  connectionTimeoutMillis: 5000,
  idleTimeoutMillis: 30000,
  max: 10,
});

// Flag to track if we should use mock database
let useMockDb = false;

export const db = {
  // Test connection
  async testConnection() {
    if (useMockDb) {
      return await mockDb.testConnection();
    }

    try {
      const client = await pool.connect();
      const result = await client.query('SELECT NOW() as current_time');
      client.release();
      return { success: true, data: result.rows[0] };
    } catch (error) {
      console.log('🔄 PostgreSQL connection failed, switching to mock database');
      useMockDb = true;
      return await mockDb.testConnection();
    }
  },

  // Get users
  async getUsers() {
    if (useMockDb) {
      return await mockDb.getUsers();
    }

    try {
      const client = await pool.connect();
      const result = await client.query('SELECT id, email, username, first_name, last_name, role, is_active, created_at FROM users');
      client.release();
      return { success: true, data: result.rows };
    } catch (error) {
      useMockDb = true;
      return await mockDb.getUsers();
    }
  },

  // Get user by email
  async getUserByEmail(email: string) {
    if (useMockDb) {
      return await mockDb.getUserByEmail(email);
    }

    try {
      const client = await pool.connect();
      const result = await client.query('SELECT * FROM users WHERE email = $1', [email]);
      client.release();
      return { success: true, data: result.rows[0] || null };
    } catch (error) {
      useMockDb = true;
      return await mockDb.getUserByEmail(email);
    }
  },

  // Get products
  async getProducts() {
    if (useMockDb) {
      return await mockDb.getProducts();
    }

    try {
      const client = await pool.connect();
      const result = await client.query('SELECT * FROM products ORDER BY created_at DESC');
      client.release();
      return { success: true, data: result.rows };
    } catch (error) {
      useMockDb = true;
      return await mockDb.getProducts();
    }
  },

  // Create product
  async createProduct(product: any) {
    if (useMockDb) {
      return await mockDb.createProduct(product);
    }

    try {
      const client = await pool.connect();
      const result = await client.query(
        'INSERT INTO products (name, description, sku, price, cost, category, brand) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *',
        [product.name, product.description, product.sku, product.price, product.cost, product.category, product.brand]
      );
      client.release();
      return { success: true, data: result.rows[0] };
    } catch (error) {
      useMockDb = true;
      return await mockDb.createProduct(product);
    }
  },

  // Raw query method
  async query(text: string, params?: any[]) {
    if (useMockDb) {
      return await mockDb.query(text, params);
    }

    try {
      const client = await pool.connect();
      const result = await client.query(text, params);
      client.release();
      return { success: true, data: result.rows };
    } catch (error) {
      useMockDb = true;
      return await mockDb.query(text, params);
    }
  }
};

export default db;
