#!/usr/bin/env ts-node

import { db } from '../services/database';

async function main() {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Connect to database
    await db.connect();
    
    // Run seeding
    await db.seed();
    
    console.log('✅ Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  } finally {
    // Disconnect from database
    await db.disconnect();
  }
}

// Run if this script is executed directly
if (require.main === module) {
  main();
}

export default main;
