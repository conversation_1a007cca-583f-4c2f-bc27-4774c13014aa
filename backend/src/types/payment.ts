// Payment Gateway Backend Types

export interface PaymentGateway {
  id: string;
  name: string;
  type: PaymentGatewayType;
  enabled: boolean;
  isDefault: boolean;
  configuration: PaymentGatewayConfig;
  supportedCurrencies: string[];
  supportedCountries: string[];
  features: PaymentFeature[];
  status: 'active' | 'inactive' | 'testing' | 'error';
  lastTested?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export type PaymentGatewayType = 
  | 'stripe' 
  | 'paypal' 
  | 'razorpay' 
  | 'square' 
  | 'braintree' 
  | 'authorize_net' 
  | 'payu' 
  | 'mollie' 
  | 'bank_transfer' 
  | 'cash_on_delivery';

export interface PaymentGatewayConfig {
  // Common fields
  apiKey?: string;
  secretKey?: string;
  webhookSecret?: string;
  environment: 'sandbox' | 'production';
  
  // Stripe specific
  publishableKey?: string;
  
  // PayPal specific
  clientId?: string;
  clientSecret?: string;
  
  // Razorpay specific
  keyId?: string;
  keySecret?: string;
  
  // Bank Transfer specific
  bankName?: string;
  accountNumber?: string;
  routingNumber?: string;
  swiftCode?: string;
  
  // Additional settings
  currency?: string;
  minimumAmount?: number;
  maximumAmount?: number;
  processingFee?: number;
  processingFeeType?: 'fixed' | 'percentage';
}

export interface PaymentFeature {
  name: string;
  enabled: boolean;
  description?: string;
}

export interface PaymentTransaction {
  id: string;
  gatewayId: string;
  gatewayTransactionId?: string;
  clientId: string;
  invoiceId?: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: string;
  description?: string;
  metadata?: Record<string, any>;
  gatewayResponse?: Record<string, any>;
  failureReason?: string;
  processedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export type PaymentStatus = 
  | 'pending' 
  | 'processing' 
  | 'completed' 
  | 'failed' 
  | 'cancelled' 
  | 'refunded' 
  | 'partially_refunded';

export interface PaymentWebhook {
  id: string;
  gatewayId: string;
  url: string;
  events: string[];
  secret?: string;
  enabled: boolean;
  lastTriggered?: Date;
  status: 'active' | 'inactive' | 'failed';
  createdAt: Date;
  updatedAt: Date;
}

export interface PaymentSettings {
  defaultGateway: string;
  defaultCurrency: string;
  allowedCurrencies: string[];
  minimumAmount: number;
  maximumAmount: number;
  autoCapture: boolean;
  requireBillingAddress: boolean;
  requireShippingAddress: boolean;
  enableSaveCard: boolean;
  enableRecurringPayments: boolean;
  paymentTimeout: number; // in minutes
  retryFailedPayments: boolean;
  maxRetryAttempts: number;
  notificationSettings: {
    emailOnSuccess: boolean;
    emailOnFailure: boolean;
    smsOnSuccess: boolean;
    smsOnFailure: boolean;
  };
}

// Request/Response Types
export interface CreatePaymentGatewayRequest {
  name: string;
  type: PaymentGatewayType;
  enabled: boolean;
  isDefault: boolean;
  configuration: Partial<PaymentGatewayConfig>;
  supportedCurrencies: string[];
  features: PaymentFeature[];
}

export interface UpdatePaymentGatewayRequest extends Partial<CreatePaymentGatewayRequest> {
  id: string;
}

export interface PaymentGatewayTestRequest {
  gatewayId: string;
  testType: 'connection' | 'authentication' | 'webhook' | 'transaction';
  testData?: Record<string, any>;
}

export interface PaymentGatewayTestResponse {
  success: boolean;
  gatewayId: string;
  testResults: {
    connection: boolean;
    authentication: boolean;
    webhook?: boolean;
    testTransaction?: boolean;
  };
  message?: string;
  error?: string;
  details?: Record<string, any>;
}

export interface ProcessPaymentRequest {
  gatewayId: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  clientId: string;
  description?: string;
  metadata?: Record<string, any>;
  billingAddress?: {
    name: string;
    email: string;
    phone?: string;
    line1: string;
    line2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
}

export interface ProcessPaymentResponse {
  success: boolean;
  transactionId?: string;
  gatewayTransactionId?: string;
  status: PaymentStatus;
  amount: number;
  currency: string;
  paymentMethod: string;
  message?: string;
  error?: string;
  redirectUrl?: string;
  gatewayResponse?: Record<string, any>;
}

// Database Models (for ORM)
export interface PaymentGatewayModel {
  id: string;
  name: string;
  type: PaymentGatewayType;
  enabled: boolean;
  is_default: boolean;
  configuration: string; // JSON string
  supported_currencies: string; // JSON array
  supported_countries: string; // JSON array
  features: string; // JSON array
  status: string;
  last_tested?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface PaymentTransactionModel {
  id: string;
  gateway_id: string;
  gateway_transaction_id?: string;
  client_id: string;
  invoice_id?: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  payment_method: string;
  description?: string;
  metadata?: string; // JSON string
  gateway_response?: string; // JSON string
  failure_reason?: string;
  processed_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface PaymentWebhookModel {
  id: string;
  gateway_id: string;
  url: string;
  events: string; // JSON array
  secret?: string;
  enabled: boolean;
  last_triggered?: Date;
  status: string;
  created_at: Date;
  updated_at: Date;
}

// Gateway Service Interface
export interface PaymentGatewayService {
  initialize(config: PaymentGatewayConfig): Promise<boolean>;
  testConnection(): Promise<boolean>;
  processPayment(request: ProcessPaymentRequest): Promise<ProcessPaymentResponse>;
  refundPayment(transactionId: string, amount?: number): Promise<ProcessPaymentResponse>;
  getTransaction(transactionId: string): Promise<PaymentTransaction | null>;
  handleWebhook(payload: any, signature?: string): Promise<void>;
  validateConfig(config: PaymentGatewayConfig): boolean;
}

// Constants
export const PAYMENT_GATEWAY_TYPES: PaymentGatewayType[] = [
  'stripe',
  'paypal',
  'razorpay',
  'square',
  'braintree',
  'authorize_net',
  'payu',
  'mollie',
  'bank_transfer',
  'cash_on_delivery'
];

export const SUPPORTED_CURRENCIES = [
  'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD',
  'MXN', 'SGD', 'HKD', 'NOK', 'TRY', 'RUB', 'INR', 'BRL', 'ZAR', 'KRW'
];

export const PAYMENT_STATUSES: PaymentStatus[] = [
  'pending',
  'processing',
  'completed',
  'failed',
  'cancelled',
  'refunded',
  'partially_refunded'
];
