import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { Server } from 'socket.io';

// Import routes
import authRoutes from './routes/auth';
import productRoutes from './routes/products-simple';
// import tallyRoutes from './routes/tallyRoutes';
import clientRoutes from './routes/clientRoutes'
// import paymentRoutes from './routes/paymentRoutes';
import db from './db/connection';
import { db as prismaDb } from './services/database';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env['CORS_ORIGIN']?.split(',') || ['http://localhost:5173'],
    credentials: true,
  },
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env['CORS_ORIGIN']?.split(',') || ['http://localhost:5173'],
  credentials: true,
}));
app.use(compression());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/api/health', async (_req, res) => {
  const dbHealth = await db.testConnection();
  const prismaHealth = await prismaDb.healthCheck();

  res.status(200).json({
    status: 'OK',
    message: 'Inventory Management System API is running',
    database: dbHealth.success ? 'Connected' : 'Disconnected',
    prisma: prismaHealth ? 'Connected' : 'Disconnected',
    dbError: dbHealth.success ? undefined : (dbHealth as any).error,
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env['NODE_ENV'] || 'development',
  });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/products', productRoutes);
app.use('/api/clients', clientRoutes);
// app.use('/api/payments', paymentRoutes);
// app.use('/api/tally', tallyRoutes);

// Simple Tally API endpoints for testing
app.post('/api/tally/test-connection', (_req, res) => {
  setTimeout(() => {
    res.json({
      success: true,
      message: 'Successfully connected to Tally server!',
      timestamp: new Date().toISOString()
    });
  }, 1000);
});

app.post('/api/tally/manual-sync', (_req, res) => {
  setTimeout(() => {
    res.json({
      success: true,
      message: 'Synchronization completed',
      results: {
        ledgers: { success: 45, failed: 0, errors: [] },
        vouchers: { success: 128, failed: 0, errors: [] },
        stockItems: { success: 89, failed: 0, errors: [] }
      },
      timestamp: new Date().toISOString()
    });
  }, 2000);
});

app.get('/api/tally/sync-status', (_req, res) => {
  res.json({
    success: true,
    data: {
      isConnected: true,
      lastSync: '2024-12-06 08:30:00',
      nextSync: '2024-12-06 09:30:00',
      syncInProgress: false,
      totalRecords: 1250,
      syncedRecords: 1250,
      errors: []
    },
    timestamp: new Date().toISOString()
  });
});

app.get('/api/tally/sync-logs', (_req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        timestamp: '2024-12-06 08:30:00',
        module: 'Ledgers',
        action: 'Export',
        status: 'success',
        message: 'Successfully exported 45 ledger accounts to Tally',
        recordsProcessed: 45
      },
      {
        id: '2',
        timestamp: '2024-12-06 08:25:00',
        module: 'Vouchers',
        action: 'Export',
        status: 'success',
        message: 'Successfully exported 128 vouchers to Tally',
        recordsProcessed: 128
      }
    ],
    timestamp: new Date().toISOString()
  });
});

// API routes
app.get('/api/v1', (_req, res) => {
  res.status(200).json({
    message: 'Welcome to Inventory Management System API v1',
    endpoints: {
      health: '/api/health',
      docs: '/api-docs',
      auth: '/api/v1/auth',
      products: '/api/v1/products',
      inventory: '/api/v1/inventory',
      orders: '/api/v1/orders',
      vendors: '/api/v1/vendors',
      customers: '/api/v1/customers',
      reports: '/api/v1/reports',
      tally: '/api/tally',
    },
  });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`Client connected: ${socket.id}`);
  
  socket.on('disconnect', () => {
    console.log(`Client disconnected: ${socket.id}`);
  });
  
  // Real-time inventory updates
  socket.on('join-inventory', (_data) => {
    socket.join('inventory-updates');
    console.log(`Client ${socket.id} joined inventory updates`);
  });

  // Real-time order updates
  socket.on('join-orders', (_data) => {
    socket.join('order-updates');
    console.log(`Client ${socket.id} joined order updates`);
  });
});

// Error handling middleware
app.use((err: any, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
  console.error('Error:', err);
  res.status(err.status || 500).json({
    error: {
      message: err.message || 'Internal Server Error',
      status: err.status || 500,
      timestamp: new Date().toISOString(),
    },
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: {
      message: 'Endpoint not found',
      status: 404,
      path: req.originalUrl,
      timestamp: new Date().toISOString(),
    },
  });
});

const PORT = process.env['APP_PORT'] || 3000;
const HOST = process.env['APP_HOST'] || 'localhost';

// Initialize database connection
async function startServer() {
  try {
    // Try to connect to Prisma database (optional for now)
    try {
      await prismaDb.connect();
      console.log('✅ PostgreSQL database connected successfully');
    } catch (dbError) {
      console.warn('⚠️ PostgreSQL not available, running with mock data:', (dbError as Error).message);
    }

    server.listen(PORT, () => {
      console.log(`
🚀 Inventory Management System API Server Started!
📍 Server running on: http://${HOST}:${PORT}
🌍 Environment: ${process.env['NODE_ENV'] || 'development'}
📊 Health Check: http://${HOST}:${PORT}/api/health
📚 API Docs: http://${HOST}:${PORT}/api-docs
🔌 Socket.IO: Enabled
💾 Database: PostgreSQL with Prisma (fallback to mock data if unavailable)
⏰ Started at: ${new Date().toISOString()}
      `);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  await prismaDb.disconnect();
  server.close(() => {
    console.log('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  await prismaDb.disconnect();
  server.close(() => {
    console.log('Process terminated');
    process.exit(0);
  });
});

export default app;
