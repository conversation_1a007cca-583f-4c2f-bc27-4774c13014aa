# ==============================================
# BACKEND ENVIRONMENT VARIABLES
# ==============================================

# Application Settings
NODE_ENV=development
APP_NAME=Inventory Management System API
APP_VERSION=1.0.0
APP_PORT=3000
APP_HOST=localhost
APP_URL=http://localhost:3000

# Database Configuration
DATABASE_URL=postgresql://inventory_user:inventory_password@localhost:5432/inventory_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=inventory_db
DB_USER=inventory_user
DB_PASSWORD=inventory_password
DB_SSL=false

# Redis Configuration
REDIS_URL=redis://:redis_password@localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password
REDIS_DB=0

# Elasticsearch Configuration
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX_PREFIX=inventory

# RabbitMQ Configuration
RABBITMQ_URL=amqp://inventory_user:inventory_password@localhost:5672
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=inventory_user
RABBITMQ_PASSWORD=inventory_password

# JWT & Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=7d

# Encryption & Security
ENCRYPTION_KEY=your-32-character-encryption-key-here
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key-change-this

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# File Storage
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,xlsx,csv

# CORS Configuration
CORS_ORIGIN=http://localhost:5173,http://localhost:3000
CORS_CREDENTIALS=true

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# Development Tools
DEBUG=inventory:*
SWAGGER_ENABLED=true
API_DOCS_PATH=/api-docs
