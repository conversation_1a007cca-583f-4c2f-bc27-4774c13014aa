version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:13-alpine
    container_name: inventory_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: inventory_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres_password
      POSTGRES_HOST_AUTH_METHOD: md5
      POSTGRES_INITDB_ARGS: "--auth-host=md5 --auth-local=trust"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - inventory_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: inventory_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - inventory_network

  # Elasticsearch for search and analytics
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: inventory_elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - inventory_network

  # RabbitMQ Message Queue
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: inventory_rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: inventory_user
      RABBITMQ_DEFAULT_PASS: inventory_password
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - inventory_network

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: inventory_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin_password
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - inventory_network

  # Backend API (when ready)
  # backend:
  #   build:
  #     context: ./backend
  #     dockerfile: Dockerfile
  #   container_name: inventory_backend
  #   restart: unless-stopped
  #   environment:
  #     NODE_ENV: development
  #     DATABASE_URL: ************************************************************/inventory_db
  #     REDIS_URL: redis://:redis_password@redis:6379
  #   ports:
  #     - "3000:3000"
  #   depends_on:
  #     - postgres
  #     - redis
  #   volumes:
  #     - ./backend:/app
  #     - /app/node_modules
  #   networks:
  #     - inventory_network

  # Frontend (when ready)
  # frontend:
  #   build:
  #     context: ./frontend
  #     dockerfile: Dockerfile
  #   container_name: inventory_frontend
  #   restart: unless-stopped
  #   ports:
  #     - "5173:5173"
  #   volumes:
  #     - ./frontend:/app
  #     - /app/node_modules
  #   networks:
  #     - inventory_network

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:
  rabbitmq_data:
  pgadmin_data:

networks:
  inventory_network:
    driver: bridge
