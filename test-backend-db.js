// Test backend database connection
const { Client } = require('pg');

async function testBackendDatabase() {
  // Try different connection configurations
  const configs = [
    {
      name: 'With empty password',
      config: {
        host: 'localhost',
        port: 5432,
        database: 'inventory_db',
        user: 'postgres',
        password: '',
      }
    },
    {
      name: 'Without password',
      config: {
        host: 'localhost',
        port: 5432,
        database: 'inventory_db',
        user: 'postgres',
      }
    },
    {
      name: 'With original password',
      config: {
        host: 'localhost',
        port: 5432,
        database: 'inventory_db',
        user: 'postgres',
        password: 'postgres_password',
      }
    }
  ];

  for (const { name, config } of configs) {
    console.log(`\n🧪 Testing: ${name}`);
    const client = new Client(config);
    
    try {
      await client.connect();
      console.log('✅ Connected successfully!');
      
      // Test query
      const result = await client.query('SELECT COUNT(*) as count FROM users');
      console.log('✅ Query successful:', result.rows[0].count, 'users found');
      
      // Test the tables we created
      const tables = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name
      `);
      console.log('✅ Tables found:', tables.rows.map(r => r.table_name).join(', '));
      
      await client.end();
      console.log('🎉 This configuration works!');
      
      // If we get here, this config works, so let's update the .env file
      let dbUrl;
      if (config.password) {
        dbUrl = `postgresql://${config.user}:${config.password}@${config.host}:${config.port}/${config.database}`;
      } else {
        dbUrl = `postgresql://${config.user}@${config.host}:${config.port}/${config.database}`;
      }
      console.log('📝 Working DATABASE_URL:', dbUrl);
      break;
      
    } catch (error) {
      console.log('❌ Failed:', error.message);
      await client.end().catch(() => {});
    }
  }
}

testBackendDatabase();
