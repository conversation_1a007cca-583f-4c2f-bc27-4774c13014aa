-- Initialize database for inventory management system
-- Create a simple test table first

CREATE TABLE IF NOT EXISTS test_connection (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Insert test data
INSERT INTO test_connection (name) VALUES ('Database Connection Test');

-- Create basic users table
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'USER',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create basic products table
CREATE TABLE IF NOT EXISTS products (
    id VARCHAR(36) PRIMARY KEY DEFAULT gen_random_uuid()::text,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    sku VARCHAR(100) UNIQUE NOT NULL,
    price DECIMAL(10,2) NOT NULL DEFAULT 0,
    cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    category VARCHAR(100),
    brand VARCHAR(100),
    status VARCHAR(50) DEFAULT 'ACTIVE',
    created_by VARCHAR(36),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Insert test user
INSERT INTO users (email, username, password, first_name, last_name, role) 
VALUES ('<EMAIL>', 'admin', 'hashed_password', 'Admin', 'User', 'ADMIN')
ON CONFLICT (email) DO NOTHING;

-- Insert test product
INSERT INTO products (name, description, sku, price, cost, category, brand) 
VALUES ('Test Product', 'A test product for the inventory system', 'TEST-001', 99.99, 50.00, 'Electronics', 'TestBrand')
ON CONFLICT (sku) DO NOTHING;

-- Verify tables were created
SELECT 'Tables created successfully' as status;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as product_count FROM products;
SELECT COUNT(*) as test_count FROM test_connection;
