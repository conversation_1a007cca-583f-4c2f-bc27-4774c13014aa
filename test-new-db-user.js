// Test new database user connection
const { Client } = require('pg');

async function testNewDatabaseUser() {
  console.log('🧪 Testing new database user connection...\n');

  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'inventory_db',
    user: 'inventory_user',
    password: 'inventory_pass',
  });

  try {
    console.log('🔌 Connecting to database...');
    await client.connect();
    console.log('✅ Connected successfully!');
    
    // Test basic query
    const result = await client.query('SELECT NOW() as current_time');
    console.log('✅ Basic query successful:', result.rows[0].current_time);
    
    // Test users table
    const usersResult = await client.query('SELECT COUNT(*) as count FROM users');
    console.log('✅ Users table accessible:', usersResult.rows[0].count, 'users found');
    
    // Test products table
    const productsResult = await client.query('SELECT COUNT(*) as count FROM products');
    console.log('✅ Products table accessible:', productsResult.rows[0].count, 'products found');
    
    // Test insert capability
    const insertResult = await client.query(
      "INSERT INTO test_connection (name) VALUES ('New User Test') RETURNING id, name, created_at"
    );
    console.log('✅ Insert test successful:', insertResult.rows[0]);
    
    // Test select from products
    const productsList = await client.query('SELECT id, name, sku, price FROM products LIMIT 3');
    console.log('✅ Products query successful:');
    productsList.rows.forEach(product => {
      console.log(`   - ${product.name} (${product.sku}): $${product.price}`);
    });
    
    console.log('\n🎉 Database connection test completed successfully!');
    console.log('📊 Summary:');
    console.log('   ✅ New user authentication working');
    console.log('   ✅ All tables accessible');
    console.log('   ✅ Read/Write operations working');
    console.log('   ✅ Ready for backend integration');
    
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    console.error('   Code:', error.code);
  } finally {
    await client.end();
  }
}

testNewDatabaseUser();
