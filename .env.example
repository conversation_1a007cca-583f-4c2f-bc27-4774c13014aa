# ==============================================
# INVENTORY MANAGEMENT SYSTEM - ENVIRONMENT VARIABLES
# ==============================================

# ==============================================
# APPLICATION SETTINGS
# ==============================================
NODE_ENV=development
APP_NAME=Inventory Management System
APP_VERSION=1.0.0
APP_PORT=3000
APP_HOST=localhost
APP_URL=http://localhost:3000

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
# PostgreSQL Primary Database
DATABASE_URL=postgresql://inventory_user:inventory_password@localhost:5432/inventory_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=inventory_db
DB_USER=inventory_user
DB_PASSWORD=inventory_password
DB_SSL=false

# ==============================================
# REDIS CONFIGURATION
# ==============================================
REDIS_URL=redis://:redis_password@localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password
REDIS_DB=0

# ==============================================
# ELASTICSEARCH CONFIGURATION
# ==============================================
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX_PREFIX=inventory

# ==============================================
# MESSAGE QUEUE (RabbitMQ)
# ==============================================
RABBITMQ_URL=amqp://inventory_user:inventory_password@localhost:5672
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=inventory_user
RABBITMQ_PASSWORD=inventory_password

# ==============================================
# JWT & AUTHENTICATION
# ==============================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=7d

# ==============================================
# ENCRYPTION & SECURITY
# ==============================================
ENCRYPTION_KEY=your-32-character-encryption-key-here
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key-change-this

# ==============================================
# EMAIL CONFIGURATION
# ==============================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# ==============================================
# FILE STORAGE
# ==============================================
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,xlsx,csv

# ==============================================
# TALLY ERP INTEGRATION
# ==============================================
TALLY_SERVER_URL=http://localhost:9000
TALLY_COMPANY_NAME=Your Company Name
TALLY_USERNAME=tally_user
TALLY_PASSWORD=tally_password

# ==============================================
# E-COMMERCE INTEGRATIONS
# ==============================================
# WooCommerce
WOOCOMMERCE_URL=https://your-store.com
WOOCOMMERCE_CONSUMER_KEY=ck_your_consumer_key
WOOCOMMERCE_CONSUMER_SECRET=cs_your_consumer_secret

# Shopify
SHOPIFY_SHOP_NAME=your-shop-name
SHOPIFY_ACCESS_TOKEN=your_access_token
SHOPIFY_API_VERSION=2023-10

# Magento
MAGENTO_BASE_URL=https://your-magento-store.com
MAGENTO_ACCESS_TOKEN=your_access_token

# ==============================================
# PAYMENT GATEWAYS
# ==============================================
# Stripe
STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Razorpay (for India)
RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_key_secret

# ==============================================
# CLOUD STORAGE (AWS S3)
# ==============================================
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=inventory-management-bucket

# ==============================================
# MONITORING & LOGGING
# ==============================================
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
SENTRY_DSN=your_sentry_dsn_url

# ==============================================
# API RATE LIMITING
# ==============================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ==============================================
# CORS CONFIGURATION
# ==============================================
CORS_ORIGIN=http://localhost:5173,http://localhost:3000
CORS_CREDENTIALS=true

# ==============================================
# MOBILE APP CONFIGURATION
# ==============================================
MOBILE_API_URL=http://localhost:3000/api/v1
MOBILE_SOCKET_URL=http://localhost:3000

# ==============================================
# DEVELOPMENT TOOLS
# ==============================================
DEBUG=inventory:*
SWAGGER_ENABLED=true
API_DOCS_PATH=/api-docs
