# ==============================================
# INVENTORY MANAGEMENT SYSTEM - GITIGNORE
# ==============================================

# ==============================================
# ENVIRONMENT & SECRETS
# ==============================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.env
secrets/
config/secrets.json

# ==============================================
# NODE.JS & NPM
# ==============================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# ==============================================
# BUILD OUTPUTS
# ==============================================
build/
dist/
out/
*.tgz
*.tar.gz

# ==============================================
# LOGS
# ==============================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# ==============================================
# DATABASES
# ==============================================
*.db
*.sqlite
*.sqlite3
*.db-journal

# ==============================================
# UPLOADS & MEDIA
# ==============================================
uploads/
media/
public/uploads/
storage/

# ==============================================
# DOCKER
# ==============================================
.dockerignore
docker-compose.override.yml

# ==============================================
# IDE & EDITORS
# ==============================================
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ==============================================
# OPERATING SYSTEMS
# ==============================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ==============================================
# MOBILE DEVELOPMENT
# ==============================================
# React Native
.expo/
.expo-shared/

# Android
*.apk
*.aab
*.ap_
*.dex
*.class
bin/
gen/
out/
release/

# iOS
*.ipa
*.dSYM.zip
*.dSYM
DerivedData/
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace

# ==============================================
# TESTING
# ==============================================
coverage/
.nyc_output/
junit.xml
test-results/

# ==============================================
# MISC
# ==============================================
.sass-cache/
.connect.lock
.typings/
.vscode/
.history/
.eslintcache
