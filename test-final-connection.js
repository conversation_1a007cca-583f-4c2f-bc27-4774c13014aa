// Final test of database connection with exact backend configuration
const { Pool } = require('pg');

async function testFinalConnection() {
  console.log('🧪 Testing final database connection with backend configuration...\n');

  // Use exact same configuration as backend
  const pool = new Pool({
    host: 'localhost',
    port: 5432,
    database: 'inventory_db',
    user: 'postgres',
    password: 'postgres_password',
    ssl: false,
    connectionTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    max: 10,
  });

  try {
    console.log('🔌 Connecting to database with backend configuration...');
    const client = await pool.connect();
    console.log('✅ Connected successfully!');
    
    // Test basic query
    const result = await client.query('SELECT NOW() as current_time, version()');
    console.log('✅ Basic query successful:', result.rows[0].current_time);
    console.log('✅ PostgreSQL version:', result.rows[0].version.split(',')[0]);
    
    // Test users table
    const usersResult = await client.query('SELECT COUNT(*) as count FROM users');
    console.log('✅ Users table accessible:', usersResult.rows[0].count, 'users found');
    
    // Test products table
    const productsResult = await client.query('SELECT COUNT(*) as count FROM products');
    console.log('✅ Products table accessible:', productsResult.rows[0].count, 'products found');
    
    // Test select from products
    const productsList = await client.query('SELECT id, name, sku, price FROM products LIMIT 3');
    console.log('✅ Products query successful:');
    productsList.rows.forEach(product => {
      console.log(`   - ${product.name} (${product.sku}): $${product.price}`);
    });
    
    client.release();
    
    console.log('\n🎉 Database connection test completed successfully!');
    console.log('📊 Summary:');
    console.log('   ✅ Backend configuration working');
    console.log('   ✅ All tables accessible');
    console.log('   ✅ Read operations working');
    console.log('   ✅ Ready for products API integration');
    
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    console.error('   Code:', error.code);
    console.error('   Detail:', error.detail);
    
    // Try alternative connection methods
    console.log('\n🔄 Trying alternative connection methods...');
    
    // Try without password
    try {
      const altPool = new Pool({
        host: 'localhost',
        port: 5432,
        database: 'inventory_db',
        user: 'postgres',
        ssl: false,
      });
      
      const altClient = await altPool.connect();
      console.log('✅ Alternative connection (no password) successful!');
      altClient.release();
      await altPool.end();
      
    } catch (altError) {
      console.log('❌ Alternative connection failed:', altError.message);
    }
    
  } finally {
    await pool.end();
  }
}

testFinalConnection();
