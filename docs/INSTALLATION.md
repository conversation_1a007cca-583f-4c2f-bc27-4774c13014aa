# 🚀 Installation Guide - Inventory Management System

## 📋 Prerequisites

### System Requirements
- **Operating System:** Windows 10/11, macOS 10.15+, or Ubuntu 18.04+
- **RAM:** Minimum 8GB, Recommended 16GB+
- **Storage:** Minimum 10GB free space
- **Network:** Stable internet connection for downloads

### Required Software

#### ✅ Already Installed
- ✅ **Node.js v22.12.0** (Latest LTS)
- ✅ **npm v10.9.0** (Package manager)
- ✅ **Python 3.12.6** (Latest)
- ✅ **pip 24.2** (Python package manager)
- ✅ **Git 2.49.0** (Version control)
- ✅ **VS Code 0.44.11** (IDE)

#### 🔧 Need to Install

##### 1. PostgreSQL 16+ (Database)
```bash
# Download from: https://www.enterprisedb.com/downloads/postgres-postgresql-downloads
# Choose: PostgreSQL 16.x for Windows x86-64
# Installation notes:
# - Remember the password for 'postgres' user
# - Default port: 5432 (keep default)
# - Install pgAdmin 4 (included)
```

##### 2. Redis 7+ (Caching)
```bash
# Option 1: Windows Port
# Download from: https://github.com/tporadowski/redis/releases

# Option 2: Docker (Recommended)
docker run -d --name redis -p 6379:6379 redis:7-alpine
```

##### 3. Docker Desktop (Containerization)
```bash
# Download from: https://www.docker.com/products/docker-desktop/
# Requirements: Windows 10/11 with WSL2 enabled
```

## 🏗️ Project Setup

### Step 1: Clone Repository
```bash
git clone <repository-url>
cd inventory-management-system
```

### Step 2: Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
# Update database credentials, API keys, etc.
```

### Step 3: Install Dependencies
```bash
# Install root dependencies
npm install

# Install all project dependencies
npm run install:all
```

### Step 4: Database Setup
```bash
# Start Docker services (PostgreSQL, Redis, etc.)
npm run docker:up

# Run database migrations
npm run db:migrate

# Seed initial data
npm run db:seed
```

### Step 5: Start Development Servers
```bash
# Start all services
npm run dev

# Or start individually:
npm run dev:backend    # Backend API (Port 3000)
npm run dev:frontend   # Frontend App (Port 5173)
npm run dev:mobile     # Mobile App (Metro bundler)
```

## 🐳 Docker Setup (Alternative)

### Quick Start with Docker
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Services Included
- **PostgreSQL:** Database (Port 5432)
- **Redis:** Cache (Port 6379)
- **Elasticsearch:** Search (Port 9200)
- **RabbitMQ:** Message Queue (Port 5672, Management: 15672)
- **pgAdmin:** Database Management (Port 8080)

## 📱 Mobile Development Setup

### Android Development
```bash
# Install Android Studio
# Download from: https://developer.android.com/studio

# Install Java Development Kit (JDK 17+)
# Download from: https://adoptium.net/

# Set environment variables
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools
```

### iOS Development (macOS only)
```bash
# Install Xcode from App Store
# Install Xcode Command Line Tools
xcode-select --install

# Install CocoaPods
sudo gem install cocoapods
```

## 🖥️ Desktop Development Setup

### Tauri (Rust-based)
```bash
# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Install Tauri CLI
cargo install tauri-cli

# For Windows: Install Microsoft C++ Build Tools
# Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/
```

## 🔧 Development Tools

### Global npm Packages
```bash
npm install -g typescript ts-node nodemon
npm install -g @nestjs/cli express-generator
npm install -g prisma @prisma/client
npm install -g eslint prettier
npm install -g jest cypress
npm install -g @react-native-community/cli
```

### VS Code Extensions (Recommended)
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-eslint",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-json",
    "ms-python.python",
    "ms-vscode.vscode-docker",
    "prisma.prisma",
    "rust-lang.rust-analyzer"
  ]
}
```

## 🔍 Verification

### Check Installation
```bash
# Verify Node.js and npm
node --version  # Should show v22.12.0
npm --version   # Should show v10.9.0

# Verify Python
python --version  # Should show Python 3.12.6

# Verify Git
git --version  # Should show git version 2.49.0

# Verify Docker
docker --version
docker-compose --version

# Verify PostgreSQL
psql --version

# Verify Redis
redis-cli --version
```

### Test Database Connection
```bash
# Test PostgreSQL
psql -h localhost -p 5432 -U inventory_user -d inventory_db

# Test Redis
redis-cli ping  # Should return PONG
```

### Test Application
```bash
# Start development servers
npm run dev

# Check endpoints
curl http://localhost:3000/api/health  # Backend health check
curl http://localhost:5173             # Frontend app
```

## 🚨 Troubleshooting

### Common Issues

#### PowerShell Execution Policy (Windows)
```powershell
# Fix execution policy
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### Port Conflicts
```bash
# Check what's using a port
netstat -ano | findstr :3000  # Windows
lsof -i :3000                 # macOS/Linux

# Kill process using port
taskkill /PID <PID> /F        # Windows
kill -9 <PID>                 # macOS/Linux
```

#### Docker Issues
```bash
# Reset Docker
docker system prune -a

# Restart Docker Desktop
# Windows: Restart Docker Desktop application
# Linux: sudo systemctl restart docker
```

#### Database Connection Issues
```bash
# Check PostgreSQL service
# Windows: Services.msc -> PostgreSQL
# Linux: sudo systemctl status postgresql
# macOS: brew services list | grep postgresql
```

## 📞 Support

If you encounter any issues:
1. Check the [Troubleshooting](#-troubleshooting) section
2. Review the logs: `npm run logs`
3. Create an issue in the repository
4. Contact the development team

## 🔄 Updates

### Updating Dependencies
```bash
# Update all dependencies
npm update

# Update specific package
npm update <package-name>

# Check for outdated packages
npm outdated
```

### Database Migrations
```bash
# Create new migration
npm run db:migration:create <migration-name>

# Run migrations
npm run db:migrate

# Rollback migration
npm run db:migrate:rollback
```
