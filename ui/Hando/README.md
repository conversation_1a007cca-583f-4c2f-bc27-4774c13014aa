
# Hando - Bootstrap Admin and Dash<PERSON> Template

Thank you for purchasing Hando :). The following guideline will help you to get started with Hando and its documentation.


#### Gulp

We've included Gulp file (gulpfile.js) to help you get started with theme and build automation. You'll need to install Node.js and Gulp before using our included gulpfile.js. Note that the detailed instructions are available in `docs/setup.html` too.

To install Node visit [https://nodejs.org/download](https://nodejs.org/download/).

To install gulp, run the following command:
```
$ npm install gulp -g
```

When you're done, install the rest of the theme's dependencies:
```
$ npm install
```

From here on out, simply run `gulp` from your terminal and you're good to go!

+  `gulp` - recompiles and minifies theme assets into `dist` directory and starts local server serving the theme.

+  `gulp build` - recompiles and minifies theme assets into `dist` folder.
