{"name": "hando", "version": "1.0.0", "description": "Hando - Bootstrap Admin and Dashboard Template", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "zoyothemes", "private": true, "devDependencies": {"@babel/core": "^7.15.8", "@babel/preset-env": "^7.15.8", "@popperjs/core": "^2.9.1", "braces": "^3.0.2", "browser-sync": "^2.26.7", "del": "^5.1.0", "gulp": "^4.0.2", "gulp-autoprefixer": "^7.0.1", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.2.0", "gulp-concat": "^2.6.1", "gulp-file-include": "^2.1.1", "gulp-htmlmin": "^5.0.1", "gulp-imagemin": "^5.0.3", "gulp-newer": "^1.4.0", "gulp-npm-dist": "^1.0.2", "gulp-rename": "^1.4.0", "gulp-rtlcss": "^2.0.0", "gulp-sass": "^5.0.0", "gulp-sequence": "^1.0.0", "gulp-sourcemaps": "^2.6.5", "gulp-uglify": "^3.0.2", "merge-stream": "^2.0.0", "rtlcss": "^4.1.1", "sass": "^1.44.0", "tar": "^6.0.1"}, "dependencies": {"@tabler/icons": "^3.12.0", "apexcharts": "^3.49.0", "bootstrap": "^5.3.3", "datatables.net": "1.11.3", "datatables.net-bs5": "1.11.3", "datatables.net-buttons": "2.2.1", "datatables.net-buttons-bs5": "2.2.1", "datatables.net-keytable": "2.6.4", "datatables.net-keytable-bs5": "2.6.4", "datatables.net-responsive": "2.2.9", "datatables.net-responsive-bs5": "2.2.9", "datatables.net-select": "1.3.4", "datatables.net-select-bs5": "1.3.4", "dropzone": "^5.8.1", "feather-icons": "^4.29.1", "flatpickr": "^4.6.9", "fullcalendar": "^6.1.9", "glightbox": "^3.3.0", "gmaps": "^0.4.25", "gumshoejs": "^5.1.2", "jquery": "3.7.1", "jquery-countdown": "^2.2.0", "jquery.counterup": "^2.1.0", "jsvectormap": "^1.5.3", "list.js": "^2.3.1", "moment": "2.22.2", "node-waves": "^0.7.6", "peity": "^3.3.0", "quill": "^1.3.7", "simple-datatables": "^9.0.4", "simplebar": "^5.3.0", "swiper": "^8.0.7", "waypoints": "^4.0.1"}}