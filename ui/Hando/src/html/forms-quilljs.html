<!DOCTYPE html>
<html lang="en">
    <head>

        @@include("partials/title-meta.html", {"title": "Quilljs Editor"})

        <!-- Quill css -->
        <link href="assets/libs/quill/quill.core.js" rel="stylesheet" type="text/css" />
        <link href="assets/libs/quill/quill.snow.css" rel="stylesheet" type="text/css" />
        <link href="assets/libs/quill/quill.bubble.css" rel="stylesheet" type="text/css" />

        @@include('./partials/head-css.html')

    </head>

    @@include('./partials/body.html')

        <!-- Begin page -->
        <div id="app-layout">


            @@include('./partials/topbar.html')

            @@include('./partials/sidebar.html')

            <!-- ============================================================== -->
            <!-- Start Page Content here -->
            <!-- ============================================================== -->
        
            <div class="content-page">
                <div class="content">

                    <!-- Start Content-->
                    <div class="container-fluid">

                        <div class="py-3 d-flex align-items-sm-center flex-sm-row flex-column">
                            <div class="flex-grow-1">
                                <h4 class="fs-18 fw-semibold m-0">Quill JS Editor</h4>
                            </div>
            
                            <div class="text-end">
                                <ol class="breadcrumb m-0 py-0">
                                    <li class="breadcrumb-item"><a href="javascript: void(0);">Forms</a></li>
                                    <li class="breadcrumb-item active">Quill JS Editor</li>
                                </ol>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body">
                                        <div id="quill-editor" style="height: 400px;">
                                            <h1>Hello World</h1>
                                            <p><br></p>
                                            <h4>This is an simple editable area</h4>
                                            <p><br></p>
                                            <ol>
                                                <li>
                                                    Select a text to reveal the toolbar.
                                                </li>
                                                <li>
                                                    Edit rich document on-the-fly, so elastic!
                                                </li>
                                            </ol>
                                            <br>
                                            <p>Preset build with <code>snow</code> theme, and some common formats.</p>
                                        </div>
                                    </div>
                                </div>  
                            </div>
                        </div>

                    </div> <!-- container-fluid -->

                </div> <!-- content -->

                @@include('./partials/footer.html')

            </div>
            <!-- ============================================================== -->
            <!-- End Page content -->
            <!-- ============================================================== -->


        </div>
        <!-- END wrapper -->

        @@include('./partials/vendor.html')

        <!-- Quill Editor Js -->
        <script src="assets/libs/quill/quill.core.js"></script>
        <script src="assets/libs/quill/quill.min.js"></script>

        <!-- Quill Demo Js -->
        <script src="assets/js/pages/quilljs.init.js"></script>

        <!-- App js-->
        <script src="assets/js/app.js"></script>
        
    </body>
</html>