<!DOCTYPE html>
<html lang="en">
    <head>

        @@include("partials/title-meta.html", {"title": "Carousel"})

        @@include('./partials/head-css.html')

    </head>

    @@include('./partials/body.html')

        <!-- Begin page -->
        <div id="app-layout">


            @@include('./partials/topbar.html')

            @@include('./partials/sidebar.html')

            <!-- ============================================================== -->
            <!-- Start Page Content here -->
            <!-- ============================================================== -->
        
            <div class="content-page">
                <div class="content">

                    <!-- Start Content-->
                    <div class="container-fluid">

                        <div class="py-3 d-flex align-items-sm-center flex-sm-row flex-column">
                            <div class="flex-grow-1">
                                <h4 class="fs-18 fw-semibold m-0">Carousel</h4>
                            </div>
            
                            <div class="text-end">
                                <ol class="breadcrumb m-0 py-0">
                                    <li class="breadcrumb-item"><a href="javascript: void(0);">Components</a></li>
                                    <li class="breadcrumb-item active">Carousel</li>
                                </ol>
                            </div>
                        </div>

                        <!-- Start Carousel -->
                        <div class="row">
                            <div class="col-xl-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Default Carousel</h5>
                                    </div><!-- end card header -->
        
                                    <div class="card-body">
                                        <div class="d-flex flex-wrap gap-2">
                                            
                                            <div id="carouselExample" class="carousel slide">
                                                <div class="carousel-inner">
                                                    <div class="carousel-item active">
                                                        <img src="assets/images/small/img-7.jpg" class="d-block w-100" alt="carousel">
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img src="assets/images/small/img-9.jpg" class="d-block w-100" alt="carousel">
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img src="assets/images/small/img-5.jpg" class="d-block w-100" alt="carousel">
                                                    </div>
                                                </div>
                                                <button class="carousel-control-prev" type="button" data-bs-target="#carouselExample" data-bs-slide="prev">
                                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Previous</span>
                                                </button>
                                                <button class="carousel-control-next" type="button" data-bs-target="#carouselExample" data-bs-slide="next">
                                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Next</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div> <!-- end card-body -->

                                </div> <!-- end card-->
                            </div> <!-- end col -->

                            <div class="col-xl-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Indicators Carousel</h5>
                                    </div><!-- end card header -->
        
                                    <div class="card-body">
                                        <div class="d-flex flex-wrap gap-2">
                                            
                                            <div id="carouselExampleIndicators" class="carousel slide">
                                                <div class="carousel-indicators">
                                                    <button type="button" data-bs-target="#carouselExampleIndicators" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                                                    <button type="button" data-bs-target="#carouselExampleIndicators" data-bs-slide-to="1" aria-label="Slide 2"></button>
                                                    <button type="button" data-bs-target="#carouselExampleIndicators" data-bs-slide-to="2" aria-label="Slide 3"></button>
                                                </div>
                                                <div class="carousel-inner">
                                                    <div class="carousel-item active">
                                                        <img src="assets/images/small/img-2.jpg" class="d-block w-100" alt="carousel">
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img src="assets/images/small/img-1.jpg" class="d-block w-100" alt="carousel">
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img src="assets/images/small/img-5.jpg" class="d-block w-100" alt="carousel">
                                                    </div>
                                                </div>
                                                <button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleIndicators" data-bs-slide="prev">
                                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Previous</span>
                                                </button>
                                                <button class="carousel-control-next" type="button" data-bs-target="#carouselExampleIndicators" data-bs-slide="next">
                                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Next</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div> <!-- end card-body -->

                                </div> <!-- end card-->
                            </div> <!-- end col -->

                            <div class="col-xl-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Captions Carousel</h5>
                                    </div><!-- end card header -->
        
                                    <div class="card-body">
                                        <div class="d-flex flex-wrap gap-2">
                                            
                                            <div id="carouselExampleCaptions" class="carousel slide">
                                                <div class="carousel-indicators">
                                                    <button type="button" data-bs-target="#carouselExampleCaptions" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                                                    <button type="button" data-bs-target="#carouselExampleCaptions" data-bs-slide-to="1" aria-label="Slide 2"></button>
                                                    <button type="button" data-bs-target="#carouselExampleCaptions" data-bs-slide-to="2" aria-label="Slide 3"></button>
                                                </div>
                                                <div class="carousel-inner">
                                                    <div class="carousel-item active">
                                                        <img src="assets/images/small/img-4.jpg" class="d-block w-100" alt="carousel">
                                                        <div class="carousel-caption d-none d-md-block">
                                                            <h5>First slide label</h5>
                                                            <p>Some representative placeholder content for the first slide.</p>
                                                        </div>
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img src="assets/images/small/img-2.jpg" class="d-block w-100" alt="carousel">
                                                        <div class="carousel-caption d-none d-md-block">
                                                            <h5>Second slide label</h5>
                                                            <p>Some representative placeholder content for the second slide.</p>
                                                        </div>
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img src="assets/images/small/img-7.jpg" class="d-block w-100" alt="carousel">
                                                        <div class="carousel-caption d-none d-md-block">
                                                            <h5>Third slide label</h5>
                                                            <p>Some representative placeholder content for the third slide.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleCaptions" data-bs-slide="prev">
                                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Previous</span>
                                                </button>
                                                <button class="carousel-control-next" type="button" data-bs-target="#carouselExampleCaptions" data-bs-slide="next">
                                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Next</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div> <!-- end card-body -->

                                </div> <!-- end card-->
                            </div> <!-- end col -->

                            <div class="col-xl-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Crossfade Carousel</h5>
                                    </div><!-- end card header -->
        
                                    <div class="card-body">
                                        <div class="d-flex flex-wrap gap-2">
                                            
                                            <div id="carouselExampleFade" class="carousel slide carousel-fade">
                                                <div class="carousel-inner">
                                                    <div class="carousel-item active">
                                                        <img src="assets/images/small/img-9.jpg" class="d-block w-100" alt="carousel">
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img src="assets/images/small/img-8.jpg" class="d-block w-100" alt="carousel">
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img src="assets/images/small/img-6.jpg" class="d-block w-100" alt="carousel">
                                                    </div>
                                                </div>
                                                <button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleFade" data-bs-slide="prev">
                                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Previous</span>
                                                </button>
                                                <button class="carousel-control-next" type="button" data-bs-target="#carouselExampleFade" data-bs-slide="next">
                                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Next</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div> <!-- end card-body -->

                                </div> <!-- end card-->
                            </div> <!-- end col -->

                            <div class="col-xl-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Disable Touch Swiping</h5>
                                    </div><!-- end card header -->
        
                                    <div class="card-body">
                                        <div class="d-flex flex-wrap gap-2">
                                            
                                            <div id="carouselExampleControlsNoTouching" class="carousel slide" data-bs-touch="false">
                                                <div class="carousel-inner">
                                                    <div class="carousel-item active">
                                                        <img src="assets/images/small/img-9.jpg" class="d-block w-100" alt="carousel">
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img src="assets/images/small/img-6.jpg" class="d-block w-100" alt="carousel">
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img src="assets/images/small/img-1.jpg" class="d-block w-100" alt="carousel">
                                                    </div>
                                                </div>
                                                <button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleControlsNoTouching" data-bs-slide="prev">
                                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Previous</span>
                                                </button>
                                                <button class="carousel-control-next" type="button" data-bs-target="#carouselExampleControlsNoTouching" data-bs-slide="next">
                                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Next</span>
                                                </button>
                                            </div>

                                        </div>
                                    </div> <!-- end card-body -->

                                </div> <!-- end card-->
                            </div> <!-- end col -->

                            <div class="col-xl-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Dark Variant</h5>
                                    </div><!-- end card header -->
        
                                    <div class="card-body">
                                        <div class="d-flex flex-wrap gap-2">
                                            
                                            <div id="carouselExampleDark" class="carousel carousel-dark slide">
                                                <div class="carousel-indicators">
                                                    <button type="button" data-bs-target="#carouselExampleDark" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                                                    <button type="button" data-bs-target="#carouselExampleDark" data-bs-slide-to="1" aria-label="Slide 2"></button>
                                                    <button type="button" data-bs-target="#carouselExampleDark" data-bs-slide-to="2" aria-label="Slide 3"></button>
                                                </div>
                                                <div class="carousel-inner">
                                                    <div class="carousel-item active" data-bs-interval="10000">
                                                        <img src="assets/images/small/img-8.jpg" class="d-block w-100" alt="carousel">
                                                        <div class="carousel-caption d-none d-md-block">
                                                            <h5>First slide label</h5>
                                                            <p>Some representative placeholder content for the first slide.</p>
                                                        </div>
                                                    </div>
                                                    <div class="carousel-item" data-bs-interval="2000">
                                                        <img src="assets/images/small/img-1.jpg" class="d-block w-100" alt="carousel">
                                                        <div class="carousel-caption d-none d-md-block">
                                                            <h5>Second slide label</h5>
                                                            <p>Some representative placeholder content for the second slide.</p>
                                                        </div>
                                                    </div>
                                                    <div class="carousel-item">
                                                        <img src="assets/images/small/img-5.jpg" class="d-block w-100" alt="carousel">
                                                        <div class="carousel-caption d-none d-md-block">
                                                            <h5>Third slide label</h5>
                                                            <p>Some representative placeholder content for the third slide.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleDark" data-bs-slide="prev">
                                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Previous</span>
                                                </button>
                                                <button class="carousel-control-next" type="button" data-bs-target="#carouselExampleDark" data-bs-slide="next">
                                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                    <span class="visually-hidden">Next</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div> <!-- end card-body -->

                                </div> <!-- end card-->
                            </div> <!-- end col -->

                        </div> <!-- end row -->
                        
                    </div> <!-- container-fluid -->

                </div> <!-- content -->

                @@include('./partials/footer.html')

            </div>
            <!-- ============================================================== -->
            <!-- End Page content -->
            <!-- ============================================================== -->


        </div>
        <!-- END wrapper -->

        @@include('./partials/vendor.html')

        <!-- App js-->
        <script src="assets/js/app.js"></script>
        
    </body>
</html>