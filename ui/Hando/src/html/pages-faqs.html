<!DOCTYPE html>
<html lang="en">
    <head>

        @@include("partials/title-meta.html", {"title": "FAQ"})

        @@include('./partials/head-css.html')

    </head>

    @@include('./partials/body.html')

        <!-- Begin page -->
        <div id="app-layout">


            @@include('./partials/topbar.html', {"pagetitle": "FAQ"})

            @@include('./partials/sidebar.html')

            <!-- ============================================================== -->
            <!-- Start Page Content here -->
            <!-- ============================================================== -->
         
            <div class="content-page">
                <div class="content">

                    <!-- Start Content-->
                    <div class="container-fluid">

                        <div class="py-3 d-flex align-items-sm-center flex-sm-row flex-column">
                            <div class="flex-grow-1">
                                <h4 class="fs-18 fw-semibold m-0">FAQ</h4>
                            </div>
            
                            <div class="text-end">
                                <ol class="breadcrumb m-0 py-0">
                                    <li class="breadcrumb-item"><a href="javascript: void(0);">Pages</a></li>
                                    <li class="breadcrumb-item active">FAQ</li>
                                </ol>
                            </div>
                        </div>

                        <div clsas="row">
                            <div class="col-xxl-12">
                                <div class="profile-container p-5">
                                    <div class="justify-content-center align-content-center text-center">
                                        <p class="fs-14 mb-0 text-white fw-medium">FAQs</p>
                                        <h3 class="mb-0 fs-26 text-white">Frequently Asked Questions</h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row justify-content-center">
                            <div class="col-xxl-8">
                                <div class="mt-5 mb-5">

                                    <div class="accordion accordion-flush card position-relative overflow-hidden" id="accordionFlushExample">
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="flush-headingOne">
                                                <button class="accordion-button fs-15 fw-semibold shadow-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                                                What is an Admin Dashboard?
                                                </button>
                                            </h2>
                                            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne" data-bs-parent="#accordionFlushExample" style="">
                                                <div class="accordion-body fw-normal">
                                                Admin Dashboard is the backend interface of a website or an application that helps to manage the
                                                website's overall content and settings. It is widely used by the site owners to keep track of
                                                their website,
                                                make changes to their content, and more.
                                                </div>
                                            </div>
                                        </div>

                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="flush-headingTwo">
                                                <button class="accordion-button fs-15 fw-semibold shadow-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                                                    What should an admin dashboard template include?
                                                </button>
                                            </h2>
                                            <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo" data-bs-parent="#accordionFlushExample" style="">
                                                <div class="accordion-body fw-normal">
                                                Admin dashboard template should include user &amp; SEO friendly design with a variety of components
                                                and
                                                application designs to help create your own web application with ease. This could include
                                                customization
                                                options, technical support and about 6 months of future updates.
                                                </div>
                                            </div>
                                        </div>

                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="flush-headingThree">
                                                <button class="accordion-button fs-15 fw-semibold shadow-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                                                    Why should I buy admin templates from Wrappixel?
                                                </button>
                                            </h2>
                                            <div id="flush-collapseThree" class="accordion-collapse collapse" aria-labelledby="flush-headingThree" data-bs-parent="#accordionFlushExample" style="">
                                                <div class="accordion-body fw-normal">
                                                    Wrappixel offers high-quality templates that are easy to use and fully customizable. With over
                                                    101,801
                                                    happy customers &amp; trusted by 10,000+ Companies. Wrappixel is recognized as the leading online
                                                    source
                                                    for buying admin templates.
                                                </div>
                                            </div>
                                        </div>

                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="flush-headingfour">
                                                <button class="accordion-button fs-15 fw-semibold shadow-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapsefour" aria-expanded="false" aria-controls="flush-collapsefour">
                                                    Do Wrappixel offers a money back guarantee?
                                                </button>
                                            </h2>
                                            <div id="flush-collapsefour" class="accordion-collapse collapse" aria-labelledby="flush-headingfour" data-bs-parent="#accordionFlushExample" style="">
                                                <div class="accordion-body fw-normal">
                                                There is no money back guarantee in most companies, but if you are unhappy with our product,
                                                Wrappixel
                                                gives you a 100% money back guarantee.
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <div clsas="row">
                            <div class="col-xxl-12">
                                <div class="profile-container p-5">
                                    <div class="justify-content-center align-content-center text-center">

                                        <div class="faq-profile">
                                            <ul class="list-group-flush">
                                                <li class="list-group-item d-flex justify-content-center align-items-center flex-wrap border-top-0 p-0">
                                                    <div class="d-flex flex-wrap align-items-center">
                                                        <ul class="list-unstyled users-list d-flex align-items-center avatar-group m-0 me-2">
                                                            <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar-sm pull-up" aria-label="Vinnie Mostowy" data-bs-original-title="Vinnie Mostowy">
                                                                <img class="rounded-circle img-fluid border border-2" src="assets/images/users/user-5.jpg" alt="avatar">
                                                            </li>
                                                            <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar-sm pull-up" aria-label="Allen Rieske" data-bs-original-title="Allen Rieske">
                                                                <img class="rounded-circle img-fluid border border-2" src="assets/images/users/user-9.jpg" alt="avatar">
                                                            </li>
                                                            <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" class="avatar-sm pull-up" aria-label="Julee Rossignol" data-bs-original-title="Julee Rossignol">
                                                                <img class="rounded-circle img-fluid border border-2" src="assets/images/users/user-8.jpg" alt="avatar">
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </li>
                                            </ul>
                                        </div>
                                        
                                        <h3 class="mb-0 fs-26 text-white">Still have questions </h3>

                                    </div>
                                </div>
                            </div>
                        </div>

                    </div> <!-- container-fluid -->

                </div> <!-- content -->

                @@include('./partials/footer.html')

            </div>
            <!-- ============================================================== -->
            <!-- End Page content -->
            <!-- ============================================================== -->


        </div>
        <!-- END wrapper -->

        @@include('./partials/vendor.html')

        <!-- App js-->
        <script src="assets/js/app.js"></script>
        
    </body>
</html>