<!DOCTYPE html>
<html lang="en">
    <head>

        @@include("partials/title-meta.html", {"title": "Tooltips"})

        @@include('./partials/head-css.html')

    </head>

    @@include('./partials/body.html')

        <!-- Begin page -->
        <div id="app-layout">


            @@include('./partials/topbar.html')

            @@include('./partials/sidebar.html')

            <!-- ============================================================== -->
            <!-- Start Page Content here -->
            <!-- ============================================================== -->
        
            <div class="content-page">
                <div class="content">

                    <!-- Start Content-->
                    <div class="container-fluid">

                        <div class="py-3 d-flex align-items-sm-center flex-sm-row flex-column">
                            <div class="flex-grow-1">
                                <h4 class="fs-18 fw-semibold m-0">Tooltips</h4>
                            </div>
            
                            <div class="text-end">
                                <ol class="breadcrumb m-0 py-0">
                                    <li class="breadcrumb-item"><a href="javascript: void(0);">Components</a></li>
                                    <li class="breadcrumb-item active">Tooltips</li>
                                </ol>
                            </div>
                        </div>

                        
                        <div class="row">
                            <div class="col-xl-6">
                                <div class="card">

                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Examples</h5>
                                    </div><!-- end card header -->
        
                                    <div class="card-body">
                                        <p class="muted">Placeholder text to demonstrate some <a href="#" data-bs-toggle="tooltip" data-bs-title="Default tooltip">inline links</a> with tooltips. This is now just filler, no killer. Content placed here just to mimic the presence of <a href="#" data-bs-toggle="tooltip" data-bs-title="Another tooltip">real text</a>. And all that just to give you an idea of how tooltips would look when used in real-world situations. So hopefully you've now seen how <a href="#" data-bs-toggle="tooltip" data-bs-title="Another one here too">these tooltips on links</a> can work in practice, once you use them on <a href="#" data-bs-toggle="tooltip" data-bs-title="The last tip!">your own</a> site or project.
                                        </p>                                        
                                    </div> <!-- end card-body -->

                                </div> <!-- end card-->
                            </div>

                            <div class="col-xl-6">
                                <div class="card">

                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Custom Tooltip</h5>
                                    </div><!-- end card header -->
        
                                    <div class="card-body">
                                        <div class="d-flex flex-wrap gap-2">
                                            <button type="button" class="btn btn-primary" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-custom-class="custom-tooltip"
                                                data-bs-title="This top tooltip is themed via CSS variables.">
                                                Custom tooltip
                                            </button>
                                        </div>
                                    </div> <!-- end card-body -->

                                </div> <!-- end card-->
                            </div> 

                            <!-- Four Directions  -->
                            <div class="col-xl-6">
                                <div class="card">

                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Four Directions</h5>
                                    </div><!-- end card header -->
        
                                    <div class="card-body">
                                        <div class="d-flex flex-wrap gap-2">
                                            <button type="button" class="btn btn-primary" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Tooltip on top">
                                                Tooltip on top
                                            </button>
                                            <button type="button" class="btn btn-primary" data-bs-toggle="tooltip" data-bs-placement="right" data-bs-title="Tooltip on right">
                                                Tooltip on right
                                            </button>
                                            <button type="button" class="btn btn-primary" data-bs-toggle="tooltip" data-bs-placement="bottom" data-bs-title="Tooltip on bottom">
                                                Tooltip on bottom
                                            </button>
                                            <button type="button" class="btn btn-primary" data-bs-toggle="tooltip" data-bs-placement="left" data-bs-title="Tooltip on left">
                                                Tooltip on left
                                            </button>
                                            <button type="button" class="btn btn-primary" data-bs-toggle="tooltip" data-bs-html="true" data-bs-title="<em>Tooltip</em> <u>with</u> <b>HTML</b>">
                                                Tooltip with HTML
                                            </button>
                                        </div>
                                        
                                    </div> <!-- end card-body -->

                                </div> <!-- end card-->
                            </div>

                            <!-- HTML -->
                            <div class="col-xl-6">
                                <div class="card">

                                    <div class="card-header">
                                        <h5 class="card-title mb-0">HTML Tags</h5>
                                    </div><!-- end card header -->
        
                                    <div class="card-body">
                                        <div class="d-flex flex-wrap gap-2">
                                            <button type="button" class="btn btn-primary" data-bs-toggle="tooltip" data-bs-html="true" data-bs-title="<em>Tooltip</em> <u>with</u> <b>HTML</b>">
                                                Tooltip with HTML
                                            </button>
                                        </div>
                                    </div> <!-- end card-body -->

                                </div> <!-- end card-->
                            </div>
                            
                            <div class="col-xl-6">
                                <div class="card">

                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Disabled Tooltips</h5>
                                    </div><!-- end card header -->
        
                                    <div class="card-body">
                                        <div class="d-flex flex-wrap gap-2">
                                            <span class="d-inline-block" tabindex="0" data-bs-toggle="tooltip" data-bs-title="Disabled tooltip">
                                                <button class="btn btn-primary" type="button" disabled>Disabled button</button>
                                            </span>
                                        </div>
                                    </div> <!-- end card-body -->

                                </div> <!-- end card-->
                            </div>

                        </div>
                        
                    </div> <!-- container-fluid -->

                </div> <!-- content -->

                @@include('./partials/footer.html')

            </div>
            <!-- ============================================================== -->
            <!-- End Page content -->
            <!-- ============================================================== -->


        </div>
        <!-- END wrapper -->

        @@include('./partials/vendor.html')

        <!-- App js-->
        <script src="assets/js/app.js"></script>
        
    </body>
</html>