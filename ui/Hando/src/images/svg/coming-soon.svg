<svg width="746" height="306" viewBox="0 0 746 306" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1002_81)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M268.61 106.269C265.71 106.269 264.183 104.744 262.955 103.518C261.81 102.374 260.905 101.469 258.999 101.469C257.092 101.469 256.188 102.374 255.043 103.518C253.814 104.744 252.288 106.269 249.388 106.269C246.493 106.269 244.967 104.744 243.743 103.518C242.599 102.372 241.696 101.469 239.795 101.469C237.892 101.469 236.99 102.372 235.845 103.518C234.621 104.744 233.095 106.269 230.201 106.269C227.305 106.269 225.782 104.743 224.556 103.518C223.413 102.372 222.509 101.469 220.61 101.469C219.947 101.469 219.41 100.932 219.41 100.269C219.41 99.6056 219.947 99.0686 220.61 99.0686C223.506 99.0686 225.029 100.596 226.254 101.821C227.397 102.966 228.3 103.869 230.201 103.869C232.102 103.869 233.006 102.966 234.149 101.821C235.374 100.594 236.899 99.0686 239.795 99.0686C242.689 99.0686 244.214 100.594 245.439 101.821C246.584 102.966 247.486 103.869 249.388 103.869C251.295 103.869 252.201 102.964 253.345 101.819C254.574 100.594 256.1 99.0686 258.999 99.0686C261.897 99.0686 263.423 100.594 264.652 101.819C265.797 102.964 266.704 103.869 268.61 103.869C269.272 103.869 269.81 104.407 269.81 105.069C269.81 105.731 269.272 106.269 268.61 106.269Z" fill="url(#paint0_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M268.61 119.069C265.71 119.069 264.183 117.544 262.955 116.317C261.81 115.173 260.905 114.269 258.999 114.269C257.092 114.269 256.188 115.173 255.043 116.317C253.814 117.544 252.288 119.069 249.388 119.069C246.493 119.069 244.967 117.544 243.743 116.317C242.599 115.172 241.696 114.269 239.795 114.269C237.892 114.269 236.99 115.172 235.845 116.317C234.621 117.544 233.095 119.069 230.201 119.069C227.305 119.069 225.782 117.542 224.556 116.317C223.413 115.172 222.509 114.269 220.61 114.269C219.947 114.269 219.41 113.731 219.41 113.069C219.41 112.406 219.947 111.869 220.61 111.869C223.506 111.869 225.029 113.395 226.254 114.62C227.397 115.766 228.3 116.669 230.201 116.669C232.102 116.669 233.006 115.766 234.149 114.62C235.374 113.394 236.899 111.869 239.795 111.869C242.689 111.869 244.214 113.394 245.439 114.62C246.584 115.766 247.486 116.669 249.388 116.669C251.295 116.669 252.201 115.764 253.345 114.619C254.574 113.394 256.1 111.869 258.999 111.869C261.897 111.869 263.423 113.394 264.652 114.619C265.797 115.764 266.704 116.669 268.61 116.669C269.272 116.669 269.81 117.206 269.81 117.869C269.81 118.531 269.272 119.069 268.61 119.069Z" fill="url(#paint1_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M268.61 131.869C265.71 131.869 264.183 130.344 262.955 129.118C261.81 127.973 260.905 127.069 258.999 127.069C257.092 127.069 256.188 127.973 255.043 129.118C253.814 130.344 252.288 131.869 249.388 131.869C246.493 131.869 244.967 130.344 243.743 129.118C242.599 127.972 241.696 127.069 239.795 127.069C237.892 127.069 236.99 127.972 235.845 129.118C234.621 130.344 233.095 131.869 230.201 131.869C227.305 131.869 225.782 130.343 224.556 129.118C223.413 127.972 222.509 127.069 220.61 127.069C219.947 127.069 219.41 126.532 219.41 125.869C219.41 125.206 219.947 124.669 220.61 124.669C223.506 124.669 225.029 126.196 226.254 127.421C227.397 128.566 228.3 129.469 230.201 129.469C232.102 129.469 233.006 128.566 234.149 127.421C235.374 126.194 236.899 124.669 239.795 124.669C242.689 124.669 244.214 126.194 245.439 127.421C246.584 128.566 247.486 129.469 249.388 129.469C251.295 129.469 252.201 128.564 253.345 127.419C254.574 126.194 256.1 124.669 258.999 124.669C261.897 124.669 263.423 126.194 264.652 127.419C265.797 128.564 266.704 129.469 268.61 129.469C269.272 129.469 269.81 130.007 269.81 130.669C269.81 131.331 269.272 131.869 268.61 131.869Z" fill="url(#paint2_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M268.61 144.669C265.71 144.669 264.183 143.143 262.955 141.917C261.81 140.773 260.905 139.868 258.999 139.868C257.092 139.868 256.188 140.773 255.043 141.917C253.814 143.143 252.288 144.669 249.388 144.669C246.493 144.669 244.967 143.143 243.743 141.917C242.599 140.772 241.696 139.868 239.795 139.868C237.892 139.868 236.99 140.772 235.845 141.917C234.621 143.143 233.095 144.669 230.201 144.669C227.305 144.669 225.782 143.142 224.556 141.917C223.413 140.772 222.509 139.868 220.61 139.868C219.947 139.868 219.41 139.332 219.41 138.669C219.41 138.007 219.947 137.469 220.61 137.469C223.506 137.469 225.029 138.996 226.254 140.221C227.397 141.366 228.3 142.268 230.201 142.268C232.102 142.268 233.006 141.366 234.149 140.221C235.374 138.994 236.899 137.469 239.795 137.469C242.689 137.469 244.214 138.994 245.439 140.221C246.584 141.366 247.486 142.268 249.388 142.268C251.295 142.268 252.201 141.364 253.345 140.219C254.574 138.994 256.1 137.469 258.999 137.469C261.897 137.469 263.423 138.994 264.652 140.219C265.797 141.364 266.704 142.268 268.61 142.268C269.272 142.268 269.81 142.807 269.81 143.469C269.81 144.132 269.272 144.669 268.61 144.669Z" fill="url(#paint3_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M610.096 71.6738C610.096 74.3248 607.947 76.4738 605.296 76.4738C602.645 76.4738 600.496 74.3248 600.496 71.6738C600.496 69.0228 602.645 66.8738 605.296 66.8738C607.947 66.8738 610.096 69.0228 610.096 71.6738Z" fill="url(#paint4_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M642.096 71.6738C642.096 74.3248 639.947 76.4738 637.296 76.4738C634.645 76.4738 632.496 74.3248 632.496 71.6738C632.496 69.0228 634.645 66.8738 637.296 66.8738C639.947 66.8738 642.096 69.0228 642.096 71.6738Z" fill="url(#paint5_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M674.096 71.6738C674.096 74.3248 671.947 76.4738 669.296 76.4738C666.645 76.4738 664.496 74.3248 664.496 71.6738C664.496 69.0228 666.645 66.8738 669.296 66.8738C671.947 66.8738 674.096 69.0228 674.096 71.6738Z" fill="url(#paint6_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M704.498 71.6739C704.498 73.4419 703.064 74.8739 701.298 74.8739C699.53 74.8739 698.098 73.4419 698.098 71.6739C698.098 69.9069 699.53 68.4739 701.298 68.4739C703.064 68.4739 704.498 69.9069 704.498 71.6739Z" fill="url(#paint7_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M610.096 103.674C610.096 106.325 607.947 108.474 605.296 108.474C602.645 108.474 600.496 106.325 600.496 103.674C600.496 101.023 602.645 98.8738 605.296 98.8738C607.947 98.8738 610.096 101.023 610.096 103.674Z" fill="url(#paint8_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M642.096 103.674C642.096 106.325 639.947 108.474 637.296 108.474C634.645 108.474 632.496 106.325 632.496 103.674C632.496 101.023 634.645 98.8738 637.296 98.8738C639.947 98.8738 642.096 101.023 642.096 103.674Z" fill="url(#paint9_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M674.096 103.674C674.096 106.325 671.947 108.474 669.296 108.474C666.645 108.474 664.496 106.325 664.496 103.674C664.496 101.023 666.645 98.8738 669.296 98.8738C671.947 98.8738 674.096 101.023 674.096 103.674Z" fill="url(#paint10_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M704.498 103.674C704.498 105.442 703.064 106.874 701.298 106.874C699.53 106.874 698.098 105.442 698.098 103.674C698.098 101.907 699.53 100.474 701.298 100.474C703.064 100.474 704.498 101.907 704.498 103.674Z" fill="url(#paint11_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M610.096 135.674C610.096 138.325 607.947 140.474 605.296 140.474C602.645 140.474 600.496 138.325 600.496 135.674C600.496 133.023 602.645 130.874 605.296 130.874C607.947 130.874 610.096 133.023 610.096 135.674Z" fill="url(#paint12_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M642.096 135.674C642.096 138.325 639.947 140.474 637.296 140.474C634.645 140.474 632.496 138.325 632.496 135.674C632.496 133.023 634.645 130.874 637.296 130.874C639.947 130.874 642.096 133.023 642.096 135.674Z" fill="url(#paint13_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M672.498 135.674C672.498 137.442 671.064 138.874 669.298 138.874C667.53 138.874 666.098 137.442 666.098 135.674C666.098 133.907 667.53 132.474 669.298 132.474C671.064 132.474 672.498 133.907 672.498 135.674Z" fill="url(#paint14_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M704.498 135.674C704.498 137.442 703.064 138.874 701.298 138.874C699.53 138.874 698.098 137.442 698.098 135.674C698.098 133.907 699.53 132.474 701.298 132.474C703.064 132.474 704.498 133.907 704.498 135.674Z" fill="url(#paint15_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M608.498 167.674C608.498 169.442 607.064 170.874 605.298 170.874C603.53 170.874 602.098 169.442 602.098 167.674C602.098 165.906 603.53 164.474 605.298 164.474C607.064 164.474 608.498 165.906 608.498 167.674Z" fill="url(#paint16_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M640.498 167.674C640.498 169.442 639.064 170.874 637.298 170.874C635.53 170.874 634.098 169.442 634.098 167.674C634.098 165.906 635.53 164.474 637.298 164.474C639.064 164.474 640.498 165.906 640.498 167.674Z" fill="url(#paint17_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M672.498 167.674C672.498 169.442 671.064 170.874 669.298 170.874C667.53 170.874 666.098 169.442 666.098 167.674C666.098 165.906 667.53 164.474 669.298 164.474C671.064 164.474 672.498 165.906 672.498 167.674Z" fill="url(#paint18_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M704.498 167.674C704.498 169.442 703.064 170.874 701.298 170.874C699.53 170.874 698.098 169.442 698.098 167.674C698.098 165.906 699.53 164.474 701.298 164.474C703.064 164.474 704.498 165.906 704.498 167.674Z" fill="url(#paint19_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M147.068 58.5133C146.566 58.1353 146.069 58.0073 145.546 58.0913C142.02 58.7263 140.66 67.0733 140.238 73.0423H152.178L152.181 73.0413C151.396 68.1823 149.77 60.5453 147.068 58.5133Z" fill="url(#paint20_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M145.349 56.9884C146.178 56.8304 146.988 57.0504 147.741 57.6174C150.772 59.8984 152.499 67.9154 153.305 72.9284C154.867 72.7704 156.322 72.3034 157.626 71.5714C154.803 56.6564 149.925 38.6334 145.49 38.6334H145.458C142.493 38.6914 141.539 42.1364 140.438 46.1274C139.269 50.3624 137.943 55.1584 133.532 55.8274C133.384 55.8504 133.24 55.8604 133.095 55.8604C127.78 55.8604 123.72 41.1434 121.666 31.7554C119.764 33.7624 118.578 36.4574 118.578 39.4424V61.8424C118.578 68.0284 123.593 73.0424 129.779 73.0424H139.104C139.475 67.3264 140.777 57.8114 145.349 56.9884Z" fill="url(#paint21_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M152.179 28.2417H129.78C127.047 28.2417 124.577 29.2587 122.633 30.8837C124.809 40.8827 128.776 54.7427 133.065 54.7427C133.165 54.7427 133.264 54.7347 133.364 54.7207C137.057 54.1597 138.228 49.9257 139.359 45.8307C140.478 41.7777 141.635 37.5867 145.437 37.5147C145.452 37.5127 145.468 37.5127 145.484 37.5127C151.474 37.5127 156.481 59.6657 158.646 70.9607C161.502 68.9307 163.379 65.6127 163.379 61.8417V39.4417C163.379 33.2567 158.365 28.2417 152.179 28.2417Z" fill="url(#paint22_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M745.696 54.8673H726.497C726.497 35.4573 710.706 19.6673 691.297 19.6673V0.467285C721.294 0.467285 745.696 24.8703 745.696 54.8673Z" fill="url(#paint23_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.3047 237.164L8.30469 245.164L0.304688 237.164L8.30469 229.164L16.3047 237.164Z" fill="url(#paint24_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M745.695 213.419L737.695 221.419L729.695 213.419L737.695 205.419L745.695 213.419Z" fill="url(#paint25_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M259.188 266.222H87.7851C83.3341 266.222 79.4021 263.33 78.0751 259.083L43.6471 148.917C41.6461 142.513 46.4301 136.006 53.1391 136.006H219.319C223.769 136.006 227.702 138.898 229.029 143.145L265.014 258.297C266.243 262.228 263.306 266.222 259.188 266.222Z" fill="url(#paint26_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M268.328 258.297L232.343 143.145C231.015 138.898 227.082 136.006 222.633 136.006H57.858C53.74 136.006 50.804 140 52.032 143.93L88.017 259.083C89.345 263.33 93.278 266.222 97.727 266.222H262.502C266.619 266.222 269.556 262.228 268.328 258.297Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M97.7278 256.276L63.0898 145.951H222.633L257.271 256.276H97.7278Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M367.442 276.515H100.922C95.8608 276.515 91.7578 272.412 91.7578 267.352V264.307H376.606V267.352C376.606 272.412 372.504 276.515 367.442 276.515Z" fill="url(#paint27_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M181.028 277.315C180.586 277.315 180.227 276.957 180.227 276.514C180.227 276.073 180.586 275.714 181.028 275.714C186.07 275.714 190.173 271.613 190.173 266.571V264.308C190.173 263.866 190.531 263.508 190.973 263.508C191.415 263.508 191.773 263.866 191.773 264.308V266.571C191.773 272.495 186.953 277.315 181.028 277.315Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M487.275 117.678C440.157 124.374 381.582 159.427 374.455 193.165C368.464 221.516 396.281 281.096 396.281 281.096H642.171C642.171 281.096 639.118 185.495 620.904 162.6C602.126 138.996 535.805 110.782 487.275 117.678Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M425.298 21.6607C425.298 21.6607 397.337 18.1177 390.103 42.9247C383.743 64.7327 398.316 85.3507 398.316 85.3507L425.298 21.6607Z" fill="#825656"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M451.071 281.897C450.775 281.897 450.49 281.732 450.352 281.448L420.061 219.182C419.868 218.785 420.033 218.305 420.43 218.113C420.821 217.922 421.306 218.084 421.499 218.482L451.79 280.748C451.983 281.144 451.818 281.624 451.421 281.816C451.308 281.871 451.189 281.897 451.071 281.897Z" fill="url(#paint28_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M419.65 261.76C419.65 261.76 420.781 234.33 414.122 234.33C408.166 234.33 410.273 261.76 410.273 261.76H393.473C385.837 158.913 413.328 107.994 413.328 107.994L452.273 107.872C451.265 196.575 460.672 261.76 460.672 261.76H419.65Z" fill="#825656"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M433.953 129.26C433.953 129.26 456.862 174.696 467.553 183.096C475.879 189.637 500.234 182.48 511.08 165.533C523.299 146.442 520.133 128.418 520.133 128.418L481.299 91.9695L433.953 129.26Z" fill="#FFC4A3"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M461.636 225.974C461.236 225.974 460.89 225.674 460.841 225.266C456.627 188.828 452.357 137.364 452.315 136.849C452.277 136.408 452.605 136.022 453.046 135.986C453.447 135.953 453.87 136.275 453.908 136.718C453.952 137.233 458.22 188.67 462.432 225.081C462.482 225.521 462.167 225.917 461.729 225.969C461.697 225.972 461.667 225.974 461.636 225.974Z" fill="#825656"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M387.373 262.552C386.945 262.552 386.59 262.213 386.573 261.781C382.223 144.558 410.453 107.738 410.738 107.38C411.016 107.034 411.516 106.98 411.862 107.253C412.207 107.527 412.264 108.031 411.99 108.377C411.707 108.731 383.848 145.23 388.173 261.722C388.188 262.164 387.845 262.534 387.402 262.552H387.373Z" fill="#825656"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M457.356 170.48C457.374 170.471 466.986 165.787 472.267 149.69L449.422 117.076L433.953 129.261C433.953 129.261 446.499 154.138 457.356 170.48Z" fill="#F99175"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M479.814 35.7921C479.814 35.7921 433.954 21.5871 412.762 35.9061C392.223 49.7841 386.164 107.83 402.072 138.732C418.681 170.996 467.831 171.323 481.224 134.987C495.617 95.9401 479.814 35.7921 479.814 35.7921Z" fill="#FFC4A3"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M487.25 91.9692C487.25 91.9692 484.351 70.3332 473.659 52.7692C473.659 52.7692 453.296 55.3152 433.95 47.1692C414.604 39.0242 406.077 21.3962 408.997 14.7932C414.228 2.96117 467.793 -0.945827 507.259 27.3152C538.187 49.4602 554.223 80.0062 565.55 151.532C576.947 223.499 574.205 281.096 574.205 281.096H483.332C483.332 281.096 480.022 203.206 500.312 105.707L487.25 91.9692Z" fill="#825656"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M575.545 228.583C575.112 228.583 574.758 228.239 574.745 227.805C572.134 129.87 555.494 92.512 539.563 68.884C539.315 68.519 539.412 68.022 539.779 67.773C540.144 67.53 540.642 67.622 540.891 67.991C556.949 91.809 573.722 129.417 576.345 227.761C576.355 228.203 576.008 228.57 575.566 228.583H575.545Z" fill="#825656"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M540.225 173.565C540.225 173.565 528.01 174.996 520.501 179.547C513.003 184.091 514.199 197.741 524.4 197.729C532.909 197.719 540.225 196.461 540.225 196.461V173.565Z" fill="#FFC4A3"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M549.77 198.858C549.77 198.858 547.535 199.905 547.157 212.237C546.775 224.71 528.642 228.278 527.827 209.691C527.079 192.645 535.515 158.469 552.315 152.36C569.116 146.251 605.258 159.92 619.515 177.56C630.779 191.496 641.916 281.096 641.916 281.096H568.097L570.388 214.405C570.388 214.405 562.878 210.831 560.46 204.732C560.46 204.732 553.333 204.439 549.77 198.858Z" fill="#FFC4A3"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M528.493 198.43C528.458 198.43 528.423 198.428 528.388 198.424C527.948 198.365 527.64 197.963 527.698 197.526C528.751 189.58 530.856 181.328 533.63 174.287C533.792 173.877 534.258 173.672 534.667 173.838C535.078 173.998 535.279 174.463 535.117 174.875C532.391 181.797 530.32 189.916 529.286 197.735C529.231 198.138 528.889 198.43 528.493 198.43Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M549.771 199.659C549.499 199.659 549.235 199.52 549.084 199.27C546.767 195.396 546.537 187.377 548.521 179.763C550.667 171.529 554.994 165.189 560.704 161.908C561.08 161.689 561.572 161.817 561.795 162.203C562.016 162.586 561.883 163.075 561.5 163.295C554.303 167.431 551.25 175.64 550.069 180.167C547.967 188.229 548.673 195.467 550.457 198.448C550.685 198.828 550.561 199.318 550.181 199.545C550.052 199.623 549.912 199.659 549.771 199.659Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M583.638 211.631C582.577 211.631 581.508 211.549 580.443 211.378C575.804 210.636 572.044 208.411 569.57 204.945C566.643 200.846 565.684 195.289 566.799 188.877C568.903 176.776 576.621 168.924 576.95 168.596C577.262 168.283 577.766 168.284 578.078 168.594C578.393 168.906 578.393 169.412 578.082 169.725C578.005 169.803 570.388 177.565 568.374 189.152C567.335 195.133 568.2 200.272 570.871 204.016C573.093 207.125 576.49 209.125 580.696 209.797C585.784 210.611 590.95 209.283 594.535 206.248C597.18 204.01 598.649 201.037 598.672 197.878C598.687 195.938 598.143 194.397 597.057 193.297C595.203 191.422 592.336 191.386 592.309 191.386C591.867 191.385 591.51 191.026 591.512 190.584C591.512 190.143 591.869 189.786 592.31 189.786C592.453 189.786 595.852 189.813 598.182 192.159C599.586 193.575 600.289 195.503 600.274 197.891C600.246 201.522 598.576 204.925 595.57 207.471C592.39 210.161 588.091 211.631 583.638 211.631Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M477.226 281.897C476.784 281.897 476.426 281.54 476.426 281.097C476.361 184.282 502.346 103.36 502.609 102.554C502.748 102.135 503.192 101.905 503.619 102.041C504.038 102.179 504.267 102.63 504.131 103.05C503.869 103.854 477.962 184.545 478.026 281.097C478.026 281.538 477.668 281.897 477.226 281.897Z" fill="#825656"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M587.633 198.685C587.485 198.685 587.334 198.644 587.201 198.558C586.829 198.321 586.72 197.826 586.959 197.454L596.314 182.857C596.556 182.483 597.05 182.377 597.419 182.615C597.79 182.852 597.9 183.347 597.661 183.719L588.306 198.316C588.154 198.555 587.896 198.685 587.633 198.685Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M560.46 205.533C560.018 205.533 559.66 205.175 559.66 204.732C559.66 204.291 560.018 203.932 560.46 203.932C566.499 203.932 568.059 201.534 568.072 201.511C568.304 201.14 568.789 201.017 569.166 201.239C569.541 201.463 569.671 201.939 569.453 202.317C569.379 202.448 567.531 205.533 560.46 205.533Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M592 219.649C585.338 219.649 574.933 218.186 569.966 215.085C569.591 214.852 569.477 214.358 569.711 213.983C569.947 213.607 570.444 213.495 570.813 213.729C576.788 217.459 591.727 218.712 596.247 217.73C596.69 217.635 597.104 217.909 597.198 218.341C597.294 218.772 597.019 219.199 596.588 219.293C595.51 219.529 593.908 219.649 592 219.649Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M477.227 85.3512C477.227 85.3512 485.435 74.9282 497.845 78.2892C511.551 82.0012 511.144 104.71 500.47 110.786C491.574 115.85 482.498 113.704 482.498 113.704L477.227 85.3512Z" fill="#FFC4A3"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M486.026 95.4248C485.937 95.4248 485.845 95.4088 485.756 95.3778C485.34 95.2278 485.125 94.7698 485.273 94.3548C488.562 85.1908 498.01 86.4328 498.117 86.4498C498.553 86.5118 498.856 86.9168 498.794 87.3548C498.733 87.7918 498.328 88.0878 497.89 88.0338C497.559 87.9848 489.62 86.9778 486.78 94.8948C486.663 95.2218 486.354 95.4248 486.026 95.4248Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M457.523 58.8013C451.171 58.8013 444.19 57.8953 437.534 55.1783C413.382 45.3153 413.36 26.3623 413.363 26.1723C413.37 25.7343 413.726 25.3833 414.164 25.3833H414.172C414.615 25.3893 414.968 25.7513 414.963 26.1923C414.961 26.3713 415.04 44.2643 438.14 53.6973C457.421 61.5693 479.599 53.7643 479.821 53.6843C480.23 53.5403 480.695 53.7503 480.844 54.1673C480.993 54.5823 480.777 55.0403 480.362 55.1903C480.211 55.2453 470.107 58.8013 457.523 58.8013Z" fill="#825656"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M448.831 99.1115C448.831 101.093 447.224 102.7 445.241 102.7C443.26 102.7 441.652 101.093 441.652 99.1115C441.652 97.1295 443.26 95.5225 445.241 95.5225C447.224 95.5225 448.831 97.1295 448.831 99.1115Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M404.253 99.1115C404.253 101.093 402.646 102.7 400.664 102.7C398.682 102.7 397.074 101.093 397.074 99.1115C397.074 97.1295 398.682 95.5225 400.664 95.5225C402.646 95.5225 404.253 97.1295 404.253 99.1115Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M459.704 83.8449C458.341 84.4909 453.57 84.2319 448.602 83.8339C443.64 83.3639 438.847 82.7849 437.615 81.9119C436.309 81.0659 441.145 79.6729 448.882 80.3459C456.616 81.0269 461.137 83.2379 459.704 83.8449Z" fill="#825656"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M407.313 81.5445C406.379 82.3695 402.689 82.7545 398.873 83.1235C395.05 83.4235 391.384 83.6815 390.322 83.0315C389.207 82.4175 392.652 80.3535 398.602 79.8295C404.553 79.3125 408.304 80.7465 407.313 81.5445Z" fill="#825656"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M415.885 116.47C414.099 116.47 412.413 115.905 411.185 115.004C409.266 113.598 408.551 111.553 409.271 109.533C409.726 108.256 410.524 107.361 411.648 106.871C413.258 106.173 415.13 106.523 416.207 106.837L418.094 99.3614C418.202 98.9324 418.633 98.6674 419.065 98.7814C419.492 98.8894 419.754 99.3234 419.645 99.7514L417.544 108.073C417.489 108.301 417.333 108.491 417.124 108.595C416.913 108.698 416.669 108.707 416.453 108.612C416.429 108.601 414.012 107.59 412.281 108.342C411.573 108.651 411.082 109.215 410.778 110.07C410.101 111.969 411.499 113.25 412.13 113.714C414.033 115.108 417.315 115.487 419.913 113.518C420.262 113.253 420.765 113.32 421.033 113.673C421.301 114.025 421.232 114.526 420.879 114.793C419.321 115.973 417.559 116.47 415.885 116.47Z" fill="#F99175"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M414.456 262.56C414.05 262.56 413.703 262.252 413.661 261.838C411.575 240.719 413.646 227.782 413.669 227.654C413.739 227.217 414.162 226.916 414.587 226.995C415.023 227.066 415.318 227.477 415.247 227.913C415.227 228.039 413.19 240.792 415.255 261.682C415.297 262.121 414.977 262.513 414.536 262.557C414.509 262.558 414.482 262.56 414.456 262.56Z" fill="#825656"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M431.067 126.282C425.706 127.23 422.987 123.74 420.381 126.849C417.358 130.458 423.474 139.525 432.68 137.18C440.562 135.172 442.259 125.831 438.684 124.371C435.961 123.26 434.444 125.685 431.067 126.282Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M426.801 137.121C428.546 137.613 430.519 137.731 432.685 137.18C437.622 135.922 440.13 131.787 440.438 128.514C439.466 128.225 438.457 128.023 437.392 128.023C432.02 128.023 427.605 131.974 426.801 137.121Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M440.656 128.782C440.921 126.672 440.34 124.859 438.913 124.277C436.118 123.136 434.563 125.624 431.098 126.237C425.598 127.21 422.809 123.628 420.135 126.819C419.359 127.745 419.172 129.022 419.472 130.379C421.8 131.409 425.003 132.276 429.189 132.021C434.312 131.708 438.162 130.149 440.656 128.782Z" fill="url(#paint29_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M308.527 204.733C295.031 204.733 284.09 215.674 284.09 229.17V257.075C284.09 258.533 285.273 259.715 286.731 259.715C287.886 259.715 288.907 258.964 289.252 257.862L292.665 246.941C294.833 240.003 301.258 235.279 308.527 235.279V204.733Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M332.961 204.733C346.457 204.733 357.398 215.674 357.398 229.17V257.075C357.398 258.533 356.215 259.715 354.757 259.715C353.602 259.715 352.581 258.964 352.236 257.862L348.823 246.941C346.655 240.003 340.23 235.279 332.961 235.279V204.733Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M332.964 250.551H308.527C306.841 250.551 305.473 249.184 305.473 247.496V238.333H336.019V247.496C336.019 249.184 334.651 250.551 332.964 250.551Z" fill="#848CAE"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M335.635 241.388C338.404 241.388 340.846 239.549 341.54 236.869C343.502 229.3 345.181 219.427 345.181 207.787C345.181 170.674 331.526 131.424 320.744 131.424C309.964 131.424 296.309 170.674 296.309 207.787C296.309 219.427 297.988 229.3 299.949 236.869C300.644 239.549 303.086 241.388 305.855 241.388H335.635Z" fill="url(#paint30_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M326.851 232.224C326.851 247.407 324.116 259.715 320.742 259.715C317.369 259.715 314.633 247.407 314.633 232.224C314.633 217.041 317.369 204.733 320.742 204.733C324.116 204.733 326.851 217.041 326.851 232.224Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M304.43 155.861H337.062C332.292 141.317 326.158 131.424 320.745 131.424C315.334 131.424 309.2 141.317 304.43 155.861Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M378.78 278.042H262.707L268.816 259.715H372.671L378.78 278.042Z" fill="url(#paint31_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M559.854 265.824H520.145V244.442C520.145 239.381 524.248 235.278 529.308 235.278H550.69C555.751 235.278 559.854 239.381 559.854 244.442V265.824Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M378.78 278.042H348.234L342.125 259.715H372.671L378.78 278.042Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M585.817 278.042H494.18L500.29 259.715H579.708L585.817 278.042Z" fill="url(#paint32_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M558.327 278.842C557.944 278.842 557.604 278.566 557.539 278.174L554.484 259.846C554.411 259.409 554.706 258.997 555.142 258.925C555.572 258.852 555.989 259.146 556.062 259.583L559.117 277.911C559.19 278.347 558.895 278.76 558.46 278.831C558.416 278.839 558.37 278.842 558.327 278.842Z" fill="#1D213A"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M692.571 305.533H57.2254L38.8984 274.987H710.898L692.571 305.533Z" fill="url(#paint33_linear_1002_81)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M692.571 305.533H545.953L564.28 274.987H710.898L692.571 305.533Z" fill="#1D213A"/>
</g>
<defs>
<linearGradient id="paint0_linear_1002_81" x1="244.61" y1="99.0686" x2="244.61" y2="106.269" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint1_linear_1002_81" x1="244.61" y1="111.869" x2="244.61" y2="119.069" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint2_linear_1002_81" x1="244.61" y1="124.669" x2="244.61" y2="131.869" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint3_linear_1002_81" x1="244.61" y1="137.469" x2="244.61" y2="144.669" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint4_linear_1002_81" x1="605.296" y1="66.8738" x2="605.296" y2="76.4738" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint5_linear_1002_81" x1="637.296" y1="66.8738" x2="637.296" y2="76.4738" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint6_linear_1002_81" x1="669.296" y1="66.8738" x2="669.296" y2="76.4738" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint7_linear_1002_81" x1="701.298" y1="68.4739" x2="701.298" y2="74.8739" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint8_linear_1002_81" x1="605.296" y1="98.8738" x2="605.296" y2="108.474" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint9_linear_1002_81" x1="637.296" y1="98.8738" x2="637.296" y2="108.474" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint10_linear_1002_81" x1="669.296" y1="98.8738" x2="669.296" y2="108.474" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint11_linear_1002_81" x1="701.298" y1="100.474" x2="701.298" y2="106.874" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint12_linear_1002_81" x1="605.296" y1="130.874" x2="605.296" y2="140.474" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint13_linear_1002_81" x1="637.296" y1="130.874" x2="637.296" y2="140.474" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint14_linear_1002_81" x1="669.298" y1="132.474" x2="669.298" y2="138.874" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint15_linear_1002_81" x1="701.298" y1="132.474" x2="701.298" y2="138.874" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint16_linear_1002_81" x1="605.298" y1="164.474" x2="605.298" y2="170.874" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint17_linear_1002_81" x1="637.298" y1="164.474" x2="637.298" y2="170.874" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint18_linear_1002_81" x1="669.298" y1="164.474" x2="669.298" y2="170.874" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint19_linear_1002_81" x1="701.298" y1="164.474" x2="701.298" y2="170.874" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint20_linear_1002_81" x1="146.21" y1="58.0667" x2="146.21" y2="73.0423" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint21_linear_1002_81" x1="138.102" y1="31.7554" x2="138.102" y2="73.0424" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint22_linear_1002_81" x1="143.006" y1="28.2417" x2="143.006" y2="70.9607" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint23_linear_1002_81" x1="718.496" y1="0.467285" x2="718.496" y2="54.8673" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint24_linear_1002_81" x1="8.30469" y1="229.164" x2="8.30469" y2="245.164" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint25_linear_1002_81" x1="737.695" y1="205.419" x2="737.695" y2="221.419" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint26_linear_1002_81" x1="154.242" y1="136.006" x2="154.242" y2="266.222" gradientUnits="userSpaceOnUse">
<stop stop-color="#E8EAEF"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint27_linear_1002_81" x1="234.182" y1="264.307" x2="234.182" y2="276.515" gradientUnits="userSpaceOnUse">
<stop stop-color="#E8EAEF"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint28_linear_1002_81" x1="435.926" y1="218.033" x2="435.926" y2="281.897" gradientUnits="userSpaceOnUse">
<stop stop-color="#E8EAEF"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint29_linear_1002_81" x1="430.027" y1="123.982" x2="430.027" y2="132.066" gradientUnits="userSpaceOnUse">
<stop stop-color="#E8EAEF"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint30_linear_1002_81" x1="320.745" y1="131.424" x2="320.745" y2="241.388" gradientUnits="userSpaceOnUse">
<stop stop-color="#E8EAEF"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint31_linear_1002_81" x1="320.744" y1="259.715" x2="320.744" y2="278.042" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint32_linear_1002_81" x1="539.998" y1="259.715" x2="539.998" y2="278.042" gradientUnits="userSpaceOnUse">
<stop stop-color="#E8EAEF"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<linearGradient id="paint33_linear_1002_81" x1="374.898" y1="274.987" x2="374.898" y2="305.533" gradientUnits="userSpaceOnUse">
<stop stop-color="#108dff"/>
<stop offset="1" stop-color="#06162E"/>
</linearGradient>
<clipPath id="clip0_1002_81">
<rect width="746" height="306" fill="white"/>
</clipPath>
</defs>
</svg>
