/*!
 *
 * Documentation
 *
 */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap'); 


body {
  font-family: "Inter", sans-serif;
  font-weight: 400;
  color: #666;
  background-color: #edf0f0;
  font-size: 14px;
  line-height: 22px;
  overflow-x:hidden;
}
a {
  color: inherit;
  text-decoration: none !important;
}
a:hover,
a:focus {
  color: #253138;
  outline: none;
  text-decoration: none;
}

.p-t-20 {
	padding-top: 2em;
}

.p-t-50 {
  padding-top: 5em;
}
.list-none {
  list-style: none;
}

.navbar-default {
	border: none;
  margin-bottom: 0px;
  background-color: #7578f9;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16);
  border-radius: 0px;
	padding-top: 20px;
  padding-bottom: 20px;
}

/* Navbar brand box */
.navbar-brand-box {
  padding: 0 1.3rem;
  text-align: center;
  transition: all .1s ease-out;
}

.logo {
	font-weight: 700;
  color: #fff !important;
  line-height: 70px;
}

.bg-dark {
  background-color: #0c1021;
}

.copy {
  padding: 20px 0px 0px 0px;
}

/* Toggle Styles */


/* Topbar Header */
#page-topbar {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1002;
  background-color: #ffffff !important;
  transition: all .1s ease-out;
}
#page-topbar .navbar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  height: 70px;
  padding: 20px 32px;
}

/* this css is taken from Start Bootstrap - Simple Sidebar HTML Template (http://startbootstrap.com) */
#wrapper {
  padding-left: 0;
  transition: all 0.5s ease;
}

#wrapper.toggled {
  padding-left: 250px;
}

#sidebar-wrapper {
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  position: fixed;
  left: 250px;
  width: 0;
  height: 100%;
  margin-left: -250px;
  overflow-y: auto;
  transition: all 0.5s ease;
}

#wrapper.toggled #sidebar-wrapper {
  width: 250px;
}

#page-content-wrapper {
  width: 100%;
  position: absolute;
  padding: 15px;
}

.page-content {
  padding: 80px 0px 0px 0px;
  margin: 0 auto;
}

#wrapper.toggled #page-content-wrapper {
  position: absolute;
  margin-right: -250px;
}

/* Sidebar Styles */

.sidebar-nav {
  width: 250px;
  margin: 0;
  padding: 0;
  list-style: none;
  top: 2em;
}

.sidebar-nav li {
  text-indent: 20px;
  line-height: 40px;
}

.sidebar-nav li a {
  display: block;
  text-decoration: none;
  color: #444;
  font-weight: 600;
}

.sidebar-nav li a:hover {
  text-decoration: none;
  color: #111;
  background: rgba(0,0,0,0.05);
}

.sidebar-nav li a:active,
.sidebar-nav li a:focus {
  text-decoration: none;
}

.sidebar-nav > .sidebar-brand {
  height: 65px;
  font-size: 18px;
  line-height: 60px;
}

.sidebar-nav > .sidebar-brand a {
  color: #999999;
}

.sidebar-nav > .sidebar-brand a:hover {
  color: #fff;
  background: none;
}

@media(min-width:768px) {
  #wrapper {
    padding-left: 250px;
  }

  #wrapper.toggled {
    padding-left: 0;
  }

  #sidebar-wrapper {
    width: 250px;
  }

  #wrapper.toggled #sidebar-wrapper {
    width: 0;
  }

  #page-content-wrapper {
    padding: 20px;
    position: relative;
  }

  #wrapper.toggled #page-content-wrapper {
    position: relative;
    margin-right: 0;
  }
}

/* right bar */
.right-bar {
  background-color: #fff;
  padding: 10px 30px;
  margin-top: 20px;
  font-size: 15px;
}

.page-header {
  padding-bottom: 9px;
  margin: 0px 0 20px;
  border-bottom: 1px solid #eee;
  font-weight: 700;
}

.com-container {
	padding-bottom: 2rem;
}

#plugin-list li,#plugin-list li a {
  font-size: 15px;
}

/* ****** Utiliy ****** */
/* ********************** */
.fs-12 {
  font-size: 12px;
}
.fs-13 {
  font-size: 13px;
}
.fs-14 {
  font-size: 14px;
}
.fs-15 {
  font-size: 15px;
}
.fs-16 {
  font-size: 16px;
}
.fs-17 {
  font-size: 17px;
}
.fs-18 {
  font-size: 18px;
}
.fs-19 {
  font-size: 19px;
}
.fs-20 {
  font-size: 20px;
}
p {
  font-size: 14px;
}
.card-header {
  padding: 2rem !important;
}

/* ********************** */


/* Responsive Design */
@media (min-width: 768px) {
  #page-topbar {
    left: 250px;
  }
}

/* ****** Tree css ****** */
/* *********************** */
.verti-sitemap a {
  color: var(--bs-body-color);
  display: block
}

.verti-sitemap .parent-title a {
  padding-left: 0
}

.verti-sitemap .parent-title a:before {
  display: none
}

.verti-sitemap .parent-title:before {
  display: none
}

.verti-sitemap .first-list {
  position: relative;
  padding-top: 10px
}

.verti-sitemap .first-list:before {
  content: "";
  border-left: 2px dashed var(--bs-border-color);
  position: absolute;
  top: 0;
  height: 100%;
  bottom: 0;
  left: 0
}

.verti-sitemap .first-list .list-wrap a,.verti-sitemap .first-list li a {
  position: relative;
  padding: 10px 16px 4px 36px
}

.verti-sitemap .first-list .list-wrap a::before,.verti-sitemap .first-list li a::before {
  content: "";
  width: 24px;
  border-top: 2px dashed var(--bs-border-color);
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 0
}

.verti-sitemap .first-list .second-list,.verti-sitemap .first-list .third-list {
  margin-left: 42px
}

.verti-sitemap .first-list .second-list,.verti-sitemap .first-list .third-list {
  position: relative
}

.verti-sitemap .first-list .second-list li,.verti-sitemap .first-list .third-list li {
  position: relative
}

.verti-sitemap .first-list .second-list li:before,.verti-sitemap .first-list .third-list li:before {
  content: "";
  height: 100%;
  border-left: 2px dashed var(--bs-border-color);
  position: absolute;
  top: 0;
  left: 0;
  margin: 0 auto
}

.verti-sitemap .first-list .second-list li:last-child::before,.verti-sitemap .first-list .third-list li:last-child::before {
  height: 13px
}

.verti-sitemap .first-list:last-child::before {
  height: 25px
}

/* ********************** */