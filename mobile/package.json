{"name": "inventory-mobile", "version": "1.0.0", "description": "Mobile App for Inventory Management System", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx}", "type-check": "tsc --noEmit", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace InventoryMobile.xcworkspace -scheme InventoryMobile -configuration Release -destination generic/platform=iOS -archivePath InventoryMobile.xcarchive archive", "clean": "react-native clean", "reset-cache": "react-native start --reset-cache"}, "dependencies": {"react": "18.2.0", "react-native": "0.73.2", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.29.0", "react-native-gesture-handler": "^2.14.1", "react-native-reanimated": "^3.6.1", "react-native-vector-icons": "^10.0.3", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "redux-persist": "^6.0.0", "axios": "^1.6.2", "react-native-async-storage": "^1.21.0", "react-native-keychain": "^8.1.3", "react-native-biometrics": "^3.0.1", "react-native-camera": "^4.2.1", "react-native-vision-camera": "^3.6.17", "react-native-qrcode-scanner": "^1.5.5", "react-native-barcode-mask": "^1.2.4", "react-native-image-picker": "^7.1.0", "react-native-document-picker": "^9.1.1", "react-native-fs": "^2.20.0", "react-native-share": "^10.0.2", "react-native-push-notification": "^8.1.1", "@react-native-firebase/app": "^18.7.3", "@react-native-firebase/messaging": "^18.7.3", "react-native-splash-screen": "^3.3.0", "react-native-orientation-locker": "^1.5.0", "react-native-device-info": "^10.12.0", "react-native-network-info": "^5.2.1", "react-native-permissions": "^4.1.1", "react-native-toast-message": "^2.2.0", "react-native-modal": "^13.0.1", "react-native-super-grid": "^4.9.7", "react-native-chart-kit": "^6.12.0", "react-native-svg": "^14.1.0", "react-native-linear-gradient": "^2.8.3", "react-native-fast-image": "^8.6.3", "react-native-date-picker": "^4.3.3", "react-native-picker-select": "^9.1.3", "react-native-swipe-list-view": "^3.2.9", "socket.io-client": "^4.7.4", "moment": "^2.29.4", "lodash": "^4.17.21"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/runtime": "^7.23.6", "@react-native/eslint-config": "^0.73.2", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@types/react": "^19.0.0", "@types/react-test-renderer": "^18.0.7", "@types/lodash": "^4.14.202", "babel-jest": "^29.7.0", "eslint": "^8.55.0", "jest": "^29.7.0", "prettier": "^3.1.0", "react-test-renderer": "18.2.0", "typescript": "^5.3.3", "metro-react-native-babel-preset": "^0.77.0", "@testing-library/react-native": "^12.4.2", "@testing-library/jest-native": "^5.4.3", "detox": "^20.13.5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["inventory", "management", "mobile", "react-native", "typescript", "android", "ios"], "author": "Your Company", "license": "MIT"}