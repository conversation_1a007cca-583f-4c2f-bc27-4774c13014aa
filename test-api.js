// Simple API test script
// Using built-in fetch (Node.js 18+)

async function testAPI() {
  try {
    console.log('🧪 Testing Inventory Management System API...\n');

    // Test health endpoint
    console.log('1. Testing Health Endpoint...');
    const healthResponse = await fetch('http://localhost:3000/api/health');
    const healthData = await healthResponse.json();
    console.log('✅ Health Check:', healthData.message);
    console.log('   Status:', healthData.status);
    console.log('   Environment:', healthData.environment);
    console.log('');

    // Test auth health endpoint
    console.log('2. Testing Auth Health Endpoint...');
    const authHealthResponse = await fetch('http://localhost:3000/api/auth/health');
    const authHealthData = await authHealthResponse.json();
    console.log('✅ Auth Health:', authHealthData.message);
    console.log('');

    // Test login endpoint
    console.log('3. Testing Login Endpoint...');
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('✅ Login Test:', loginData.message);
    console.log('   User:', loginData.data?.user?.email);
    console.log('   Role:', loginData.data?.user?.role);
    console.log('   Token:', loginData.data?.token ? 'Generated' : 'Not generated');
    console.log('');

    // Test protected endpoint
    console.log('4. Testing Protected Endpoint...');
    const meResponse = await fetch('http://localhost:3000/api/auth/me', {
      headers: {
        'Authorization': `Bearer ${loginData.data?.token || 'demo-token'}`
      }
    });
    
    const meData = await meResponse.json();
    console.log('✅ Protected Route:', meData.message);
    console.log('   User ID:', meData.data?.user?.id);
    console.log('');

    console.log('🎉 All API tests completed successfully!');
    console.log('');
    console.log('📊 Summary:');
    console.log('   ✅ Backend server running on http://localhost:3000');
    console.log('   ✅ Health check endpoint working');
    console.log('   ✅ Authentication endpoints working');
    console.log('   ✅ Protected routes working');
    console.log('   ✅ JSON responses properly formatted');

  } catch (error) {
    console.error('❌ API Test Error:', error.message);
  }
}

testAPI();
