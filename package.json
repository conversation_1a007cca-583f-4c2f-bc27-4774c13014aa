{"name": "inventory-management-system", "version": "1.0.0", "description": "Enterprise-grade inventory management system with accounting integration", "private": true, "workspaces": ["backend", "frontend", "shared"], "scripts": {"install:all": "npm install && npm run install:backend && npm run install:frontend && npm run install:mobile", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "install:mobile": "cd mobile && npm install", "install:desktop": "cd desktop && npm install", "install:shared": "cd shared && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:mobile": "cd mobile && npm run start", "build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "build:mobile": "cd mobile && npm run build", "build:desktop": "cd desktop && npm run build", "build:shared": "cd shared && npm run build", "test": "npm run test:backend && npm run test:frontend && npm run test:mobile", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm run test", "test:mobile": "cd mobile && npm run test", "test:shared": "cd shared && npm run test", "lint": "npm run lint:backend && npm run lint:frontend && npm run lint:mobile", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:mobile": "cd mobile && npm run lint", "lint:shared": "cd shared && npm run lint", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:build": "docker-compose build", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:reset": "cd backend && npm run db:reset"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/inventory-management-system.git"}, "keywords": ["inventory", "management", "accounting", "tally", "ecommerce", "enterprise", "nodejs", "react", "react-native", "postgresql", "redis"], "author": "Your Company", "license": "MIT", "bugs": {"url": "https://github.com/your-org/inventory-management-system/issues"}, "homepage": "https://github.com/your-org/inventory-management-system#readme"}