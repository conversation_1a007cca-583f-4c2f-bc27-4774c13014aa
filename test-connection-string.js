// Test database connection using connection string
const { Client } = require('pg');

async function testConnectionString() {
  console.log('🧪 Testing database connection with connection string...\n');

  // Try different connection string formats
  const connectionStrings = [
    'postgresql://postgres@localhost:5432/inventory_db',
    'postgresql://postgres:postgres_password@localhost:5432/inventory_db',
    'postgres://postgres@localhost:5432/inventory_db',
    'postgres://postgres:postgres_password@localhost:5432/inventory_db'
  ];

  for (const connectionString of connectionStrings) {
    console.log(`🔌 Testing: ${connectionString}`);
    
    const client = new Client({
      connectionString: connectionString
    });

    try {
      await client.connect();
      console.log('✅ Connected successfully!');
      
      // Test basic query
      const result = await client.query('SELECT NOW() as current_time, COUNT(*) as user_count FROM users');
      console.log('✅ Query successful:', result.rows[0]);
      
      await client.end();
      console.log('🎉 This connection string works!');
      console.log('📝 Working connection string:', connectionString);
      break;
      
    } catch (error) {
      console.log('❌ Failed:', error.message);
      await client.end().catch(() => {});
    }
  }
}

testConnectionString();
