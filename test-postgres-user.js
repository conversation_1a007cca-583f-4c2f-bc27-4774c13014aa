// Test postgres user connection with trust authentication
const { Client } = require('pg');

async function testPostgresUser() {
  console.log('🧪 Testing postgres user connection with trust auth...\n');

  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'inventory_db',
    user: 'postgres',
    // password: 'postgres_password', // Using trust authentication
  });

  try {
    console.log('🔌 Connecting to database...');
    await client.connect();
    console.log('✅ Connected successfully!');
    
    // Test basic query
    const result = await client.query('SELECT NOW() as current_time, version()');
    console.log('✅ Basic query successful:', result.rows[0].current_time);
    console.log('✅ PostgreSQL version:', result.rows[0].version.split(',')[0]);
    
    // Test users table
    const usersResult = await client.query('SELECT COUNT(*) as count FROM users');
    console.log('✅ Users table accessible:', usersResult.rows[0].count, 'users found');
    
    // Test products table
    const productsResult = await client.query('SELECT COUNT(*) as count FROM products');
    console.log('✅ Products table accessible:', productsResult.rows[0].count, 'products found');
    
    // Test insert capability
    const insertResult = await client.query(
      "INSERT INTO test_connection (name) VALUES ('Trust Auth Test') RETURNING id, name, created_at"
    );
    console.log('✅ Insert test successful:', insertResult.rows[0]);
    
    // Test select from products
    const productsList = await client.query('SELECT id, name, sku, price FROM products LIMIT 3');
    console.log('✅ Products query successful:');
    productsList.rows.forEach(product => {
      console.log(`   - ${product.name} (${product.sku}): $${product.price}`);
    });
    
    // Test users query
    const usersList = await client.query('SELECT id, email, first_name, last_name, role FROM users LIMIT 2');
    console.log('✅ Users query successful:');
    usersList.rows.forEach(user => {
      console.log(`   - ${user.first_name} ${user.last_name} (${user.email}) - ${user.role}`);
    });
    
    console.log('\n🎉 Database connection test completed successfully!');
    console.log('📊 Summary:');
    console.log('   ✅ Trust authentication working');
    console.log('   ✅ All tables accessible');
    console.log('   ✅ Read/Write operations working');
    console.log('   ✅ Ready for backend integration');
    
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    console.error('   Code:', error.code);
  } finally {
    await client.end();
  }
}

testPostgresUser();
