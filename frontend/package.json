{"name": "inventory-frontend", "version": "1.0.0", "description": "Frontend Admin Panel for Inventory Management System", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write src/**/*.{ts,tsx}", "type-check": "tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@hookform/resolvers": "^3.3.2", "@tanstack/react-query": "^5.80.5", "@tanstack/react-query-devtools": "^5.80.5", "antd": "^5.12.8", "axios": "^1.6.2", "dayjs": "^1.11.10", "file-saver": "^2.0.5", "framer-motion": "^10.16.16", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "moment": "^2.29.4", "qrcode": "^1.5.3", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-helmet-async": "^2.0.4", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.5.2", "react-query": "^3.39.3", "react-router-dom": "^6.30.1", "react-table": "^7.8.0", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "xlsx": "^0.18.5", "yup": "^1.4.0", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.14.202", "@types/react": "^18.2.43", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.17", "@types/react-table": "^7.7.19", "@types/react-virtualized": "^9.21.29", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "@vitejs/plugin-react": "^4.2.1", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^23.0.1", "markdown-pdf": "^11.0.0", "postcss": "^8.4.32", "prettier": "^3.1.0", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "vite": "^5.0.8", "vitest": "^1.0.4"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["inventory", "management", "frontend", "react", "typescript", "vite", "antd"], "author": "Your Company", "license": "MIT"}