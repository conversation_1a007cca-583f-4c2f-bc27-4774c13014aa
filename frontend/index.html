<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Inventory Management System</title>
    <meta name="description" content="Comprehensive inventory management system with real-time tracking, multi-platform e-commerce sync, and advanced analytics." />
    
    <!-- Preconnect to external domains for better performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="manifest" href="/site.webmanifest">
    
    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#3B82F6" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="http://localhost:5173/" />
    <meta property="og:title" content="Inventory Management System" />
    <meta property="og:description" content="Comprehensive inventory management system with real-time tracking, multi-platform e-commerce sync, and advanced analytics." />
    <meta property="og:image" content="/og-image.png" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="http://localhost:5173/" />
    <meta property="twitter:title" content="Inventory Management System" />
    <meta property="twitter:description" content="Comprehensive inventory management system with real-time tracking, multi-platform e-commerce sync, and advanced analytics." />
    <meta property="twitter:image" content="/twitter-image.png" />
    
    <!-- CSS Reset and Base Styles -->
    <style>
      /* CSS Reset */
      *, *::before, *::after {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }
      
      /* Base styles */
      html {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        line-height: 1.5;
        font-weight: 400;
        color-scheme: light dark;
        color: rgba(255, 255, 255, 0.87);
        background-color: #242424;
        font-synthesis: none;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        -webkit-text-size-adjust: 100%;
      }
      
      body {
        margin: 0;
        display: flex;
        place-items: center;
        min-width: 320px;
        min-height: 100vh;
      }
      
      #root {
        width: 100%;
        margin: 0 auto;
        text-align: center;
      }
      
      /* Loading spinner */
      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        flex-direction: column;
        gap: 1rem;
      }
      
      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3B82F6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: #666;
        font-size: 14px;
      }
      
      /* Light mode */
      @media (prefers-color-scheme: light) {
        html {
          color: #213547;
          background-color: #ffffff;
        }
        
        .loading-text {
          color: #666;
        }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- Loading state while React app loads -->
      <div class="loading">
        <div class="spinner"></div>
        <div class="loading-text">Loading Inventory Management System...</div>
      </div>
    </div>
    
    <!-- React app entry point -->
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
