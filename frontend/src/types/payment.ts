// Payment Gateway Types and Interfaces

export interface PaymentGateway {
  id: string;
  name: string;
  type: PaymentGatewayType;
  enabled: boolean;
  isDefault: boolean;
  configuration: PaymentGatewayConfig;
  supportedCurrencies: string[];
  supportedCountries: string[];
  features: PaymentFeature[];
  status: 'active' | 'inactive' | 'testing' | 'error';
  lastTested?: string;
  createdAt: string;
  updatedAt: string;
}

export type PaymentGatewayType = 
  | 'stripe' 
  | 'paypal' 
  | 'razorpay' 
  | 'square' 
  | 'braintree' 
  | 'authorize_net' 
  | 'payu' 
  | 'mollie' 
  | 'bank_transfer' 
  | 'cash_on_delivery';

export interface PaymentGatewayConfig {
  // Common fields
  apiKey?: string;
  secretKey?: string;
  webhookSecret?: string;
  environment: 'sandbox' | 'production';
  
  // Stripe specific
  publishableKey?: string;
  
  // PayPal specific
  clientId?: string;
  clientSecret?: string;
  
  // Razorpay specific
  keyId?: string;
  keySecret?: string;
  
  // Bank Transfer specific
  bankName?: string;
  accountNumber?: string;
  routingNumber?: string;
  swiftCode?: string;
  
  // Additional settings
  currency?: string;
  minimumAmount?: number;
  maximumAmount?: number;
  processingFee?: number;
  processingFeeType?: 'fixed' | 'percentage';
}

export interface PaymentFeature {
  name: string;
  enabled: boolean;
  description?: string;
}

export interface PaymentMethod {
  id: string;
  gatewayId: string;
  type: 'card' | 'bank_account' | 'digital_wallet' | 'crypto' | 'cash';
  name: string;
  enabled: boolean;
  icon?: string;
  description?: string;
  processingTime?: string;
  fees?: {
    fixed?: number;
    percentage?: number;
    currency?: string;
  };
}

export interface PaymentTransaction {
  id: string;
  gatewayId: string;
  gatewayTransactionId?: string;
  clientId: string;
  invoiceId?: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: string;
  description?: string;
  metadata?: Record<string, any>;
  gatewayResponse?: Record<string, any>;
  failureReason?: string;
  processedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export type PaymentStatus = 
  | 'pending' 
  | 'processing' 
  | 'completed' 
  | 'failed' 
  | 'cancelled' 
  | 'refunded' 
  | 'partially_refunded';

export interface PaymentWebhook {
  id: string;
  gatewayId: string;
  url: string;
  events: string[];
  secret?: string;
  enabled: boolean;
  lastTriggered?: string;
  status: 'active' | 'inactive' | 'failed';
  createdAt: string;
  updatedAt: string;
}

export interface PaymentSettings {
  defaultGateway: string;
  defaultCurrency: string;
  allowedCurrencies: string[];
  minimumAmount: number;
  maximumAmount: number;
  autoCapture: boolean;
  requireBillingAddress: boolean;
  requireShippingAddress: boolean;
  enableSaveCard: boolean;
  enableRecurringPayments: boolean;
  paymentTimeout: number; // in minutes
  retryFailedPayments: boolean;
  maxRetryAttempts: number;
  notificationSettings: {
    emailOnSuccess: boolean;
    emailOnFailure: boolean;
    smsOnSuccess: boolean;
    smsOnFailure: boolean;
  };
}

export interface PaymentGatewayTemplate {
  type: PaymentGatewayType;
  name: string;
  description: string;
  icon: string;
  documentation: string;
  requiredFields: PaymentConfigField[];
  optionalFields: PaymentConfigField[];
  supportedFeatures: string[];
  defaultCurrencies: string[];
  testCredentials?: Record<string, string>;
}

export interface PaymentConfigField {
  key: string;
  label: string;
  type: 'text' | 'password' | 'select' | 'number' | 'boolean' | 'textarea';
  required: boolean;
  description?: string;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: {
    pattern?: string;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
  };
}

// API Response Types
export interface PaymentGatewayResponse {
  success: boolean;
  data?: PaymentGateway | PaymentGateway[];
  message?: string;
  error?: string;
}

export interface PaymentTestResponse {
  success: boolean;
  gatewayId: string;
  testResults: {
    connection: boolean;
    authentication: boolean;
    webhook?: boolean;
    testTransaction?: boolean;
  };
  message?: string;
  error?: string;
  details?: Record<string, any>;
}

// Form Types
export interface PaymentGatewayFormData {
  name: string;
  type: PaymentGatewayType;
  enabled: boolean;
  isDefault: boolean;
  configuration: Partial<PaymentGatewayConfig>;
  supportedCurrencies: string[];
  features: PaymentFeature[];
}

export interface PaymentMethodFormData {
  gatewayId: string;
  type: 'card' | 'bank_account' | 'digital_wallet' | 'crypto' | 'cash';
  name: string;
  enabled: boolean;
  description?: string;
  processingTime?: string;
  fees?: {
    fixed?: number;
    percentage?: number;
    currency?: string;
  };
}

// Constants
export const PAYMENT_GATEWAY_TYPES: { value: PaymentGatewayType; label: string; icon: string }[] = [
  { value: 'stripe', label: 'Stripe', icon: '💳' },
  { value: 'paypal', label: 'PayPal', icon: '🅿️' },
  { value: 'razorpay', label: 'Razorpay', icon: '💰' },
  { value: 'square', label: 'Square', icon: '⬜' },
  { value: 'braintree', label: 'Braintree', icon: '🧠' },
  { value: 'authorize_net', label: 'Authorize.Net', icon: '🔐' },
  { value: 'payu', label: 'PayU', icon: '💸' },
  { value: 'mollie', label: 'Mollie', icon: '🇳🇱' },
  { value: 'bank_transfer', label: 'Bank Transfer', icon: '🏦' },
  { value: 'cash_on_delivery', label: 'Cash on Delivery', icon: '💵' },
];

export const SUPPORTED_CURRENCIES = [
  'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD',
  'MXN', 'SGD', 'HKD', 'NOK', 'TRY', 'RUB', 'INR', 'BRL', 'ZAR', 'KRW'
];

export const PAYMENT_STATUSES: { value: PaymentStatus; label: string; color: string }[] = [
  { value: 'pending', label: 'Pending', color: 'warning' },
  { value: 'processing', label: 'Processing', color: 'info' },
  { value: 'completed', label: 'Completed', color: 'success' },
  { value: 'failed', label: 'Failed', color: 'danger' },
  { value: 'cancelled', label: 'Cancelled', color: 'secondary' },
  { value: 'refunded', label: 'Refunded', color: 'dark' },
  { value: 'partially_refunded', label: 'Partially Refunded', color: 'warning' },
];
