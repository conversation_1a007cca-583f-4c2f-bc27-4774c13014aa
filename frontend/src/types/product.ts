export interface ProductOption {
  id: string;
  name: string;
  type: 'text' | 'select' | 'multiselect' | 'number' | 'boolean' | 'date' | 'color' | 'file' | 'textarea' | 'url' | 'email' | 'range';
  required: boolean;
  values: string[];
  defaultValue?: any;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
    minLength?: number;
    maxLength?: number;
  };
  placeholder?: string;
  helpText?: string;
  group?: string;
  sortOrder?: number;
  conditional?: {
    dependsOn: string;
    value: any;
  };
}

export interface ProductVariant {
  id: string;
  name: string;
  sku: string;
  price: number;
  comparePrice?: number;
  costPrice?: number;
  stock: number;
  weight?: number;
  length?: number;
  width?: number;
  height?: number;
  options: Record<string, any>;
  images: string[];
  enabled?: boolean;
}

export interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  shortDescription?: string;
  sku: string;
  barcode?: string;
  
  // Pricing
  price: number;
  comparePrice?: number;
  costPrice?: number;
  
  // Inventory
  stock: number;
  minStock?: number;
  maxStock?: number;
  trackStock: boolean;
  allowBackorder: boolean;
  
  // Physical properties
  weight?: number;
  length?: number;
  width?: number;
  height?: number;
  
  // Organization
  categories: string[];
  categoryNames: string[];
  tags: string[];
  
  // Media
  images: string[];
  
  // Status
  featured: boolean;
  status: 'active' | 'draft' | 'archived';
  visibility: 'public' | 'private' | 'hidden';
  
  // SEO
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string;
  
  // Variants and Options
  variants?: ProductVariant[];
  options?: ProductOption[];
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
}

export interface ProductFormData {
  name: string;
  description: string;
  shortDescription?: string;
  sku: string;
  barcode?: string;
  
  // Pricing
  price: number;
  comparePrice?: number;
  costPrice?: number;
  
  // Inventory
  stock: number;
  minStock?: number;
  maxStock?: number;
  trackStock: boolean;
  allowBackorder: boolean;
  
  // Physical properties
  weight?: number;
  length?: number;
  width?: number;
  height?: number;
  
  // Organization
  categories: string[];
  categoryNames: string[];
  tags: string[];
  
  // Media
  images: string[];
  
  // Status
  featured: boolean;
  status: 'active' | 'draft' | 'archived';
  visibility: 'public' | 'private' | 'hidden';
  
  // SEO
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string;
  
  // Variants and Options
  variants?: ProductVariant[];
  options?: ProductOption[];
}

export interface ProductFilters {
  search?: string;
  category?: string;
  status?: string;
  featured?: boolean;
  priceMin?: number;
  priceMax?: number;
  stockMin?: number;
  stockMax?: number;
  tags?: string[];
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ProductListResponse {
  products: Product[];
  total: number;
  page: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface ProductAnalytics {
  views: number;
  sales: number;
  revenue: number;
  conversionRate: number;
  stockTurnover: number;
  topVariants?: {
    variantId: string;
    name: string;
    sales: number;
    revenue: number;
  }[];
  salesHistory?: {
    date: string;
    sales: number;
    revenue: number;
  }[];
}

export interface BulkProductOperation {
  action: 'update' | 'delete' | 'activate' | 'deactivate' | 'feature' | 'unfeature';
  productIds: string[];
  data?: Partial<ProductFormData>;
}

export interface ProductImportData {
  name: string;
  sku: string;
  price: number;
  stock: number;
  description?: string;
  category?: string;
  tags?: string;
  weight?: number;
  status?: string;
}

export interface ProductExportOptions {
  format: 'csv' | 'xlsx' | 'json';
  fields: string[];
  filters?: ProductFilters;
  includeVariants?: boolean;
  includeImages?: boolean;
}

// Category related types
export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parentId?: string;
  image?: string;
  status: 'active' | 'inactive';
  sortOrder: number;
  productCount: number;
  seoTitle?: string;
  seoDescription?: string;
  customFields?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface CategoryFormData {
  name: string;
  description?: string;
  parentId?: string;
  image?: string;
  status: 'active' | 'inactive';
  sortOrder: number;
  seoTitle?: string;
  seoDescription?: string;
  customFields?: Record<string, any>;
}

// Stock movement types
export interface StockMovement {
  id: string;
  productId: string;
  variantId?: string;
  type: 'in' | 'out' | 'adjustment' | 'transfer';
  quantity: number;
  previousStock: number;
  newStock: number;
  reason: string;
  reference?: string;
  userId: string;
  createdAt: string;
}

// Product review types
export interface ProductReview {
  id: string;
  productId: string;
  variantId?: string;
  userId: string;
  userName: string;
  rating: number;
  title: string;
  comment: string;
  verified: boolean;
  helpful: number;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  updatedAt: string;
}

export default Product;
