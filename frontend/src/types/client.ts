// Client Panel Types and Interfaces

export interface ClientUser {
  id: string;
  clientId: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: ClientRole;
  permissions: Permission[];
  isActive: boolean;
  lastLogin?: string;
  avatar?: string;
  phone?: string;
  department?: string;
  position?: string;
  createdAt: string;
  updatedAt: string;
}

export type ClientRole = 
  | 'client_admin' 
  | 'warehouse_manager' 
  | 'warehouse_staff' 
  | 'pos_manager' 
  | 'pos_cashier' 
  | 'inventory_manager' 
  | 'sales_manager' 
  | 'accountant' 
  | 'viewer';

export interface Permission {
  module: string;
  actions: string[];
}

export interface ClientSettings {
  clientId: string;
  companyInfo: {
    name: string;
    logo?: string;
    address: string;
    phone: string;
    email: string;
    website?: string;
    taxId?: string;
  };
  branding: {
    primaryColor: string;
    secondaryColor: string;
    logoUrl?: string;
    faviconUrl?: string;
    customCss?: string;
  };
  features: {
    inventory: boolean;
    pos: boolean;
    warehouse: boolean;
    ecommerce: boolean;
    accounting: boolean;
    reports: boolean;
    multiLocation: boolean;
    barcodeScan: boolean;
  };
  integrations: {
    woocommerce: boolean;
    shopify: boolean;
    magento: boolean;
    tally: boolean;
    quickbooks: boolean;
  };
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    lowStock: boolean;
    orderUpdates: boolean;
    paymentAlerts: boolean;
  };
  security: {
    twoFactorAuth: boolean;
    sessionTimeout: number;
    passwordPolicy: {
      minLength: number;
      requireUppercase: boolean;
      requireLowercase: boolean;
      requireNumbers: boolean;
      requireSymbols: boolean;
    };
    ipWhitelist: string[];
  };
  limits: {
    maxUsers: number;
    maxProducts: number;
    maxOrders: number;
    storageLimit: number; // in MB
    apiCallsPerMonth: number;
  };
  updatedAt: string;
}

export interface ClientDashboardStats {
  clientId: string;
  totalProducts: number;
  totalOrders: number;
  totalRevenue: number;
  lowStockItems: number;
  activeUsers: number;
  warehouseLocations: number;
  posTerminals: number;
  lastSyncTime?: string;
  period: 'today' | 'week' | 'month' | 'year';
  updatedAt: string;
}

export interface Warehouse {
  id: string;
  clientId: string;
  name: string;
  code: string;
  address: string;
  phone?: string;
  email?: string;
  managerId?: string;
  isActive: boolean;
  type: 'main' | 'branch' | 'storage' | 'distribution';
  capacity?: {
    totalSpace: number;
    usedSpace: number;
    unit: 'sqft' | 'sqm';
  };
  zones: WarehouseZone[];
  createdAt: string;
  updatedAt: string;
}

export interface WarehouseZone {
  id: string;
  warehouseId: string;
  name: string;
  code: string;
  type: 'receiving' | 'storage' | 'picking' | 'shipping' | 'returns';
  capacity?: number;
  isActive: boolean;
  createdAt: string;
}

export interface POSTerminal {
  id: string;
  clientId: string;
  warehouseId?: string;
  name: string;
  code: string;
  location: string;
  isActive: boolean;
  status: 'online' | 'offline' | 'maintenance';
  cashierId?: string;
  lastTransaction?: string;
  dailySales?: {
    amount: number;
    transactions: number;
    date: string;
  };
  hardware: {
    printer: boolean;
    scanner: boolean;
    cashDrawer: boolean;
    cardReader: boolean;
    display: boolean;
  };
  settings: {
    allowDiscounts: boolean;
    allowReturns: boolean;
    requireManagerApproval: boolean;
    autoOpenDrawer: boolean;
    printReceipts: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

export interface ClientModule {
  id: string;
  name: string;
  icon: string;
  description: string;
  path: string;
  enabled: boolean;
  requiredRole: ClientRole[];
  requiredPermissions: string[];
  category: 'core' | 'inventory' | 'sales' | 'warehouse' | 'pos' | 'reports' | 'settings';
}

export interface ClientNotification {
  id: string;
  clientId: string;
  userId?: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  read: boolean;
  actionUrl?: string;
  actionText?: string;
  metadata?: Record<string, any>;
  createdAt: string;
}

export interface ClientActivity {
  id: string;
  clientId: string;
  userId: string;
  action: string;
  module: string;
  description: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
}

// Form Types
export interface ClientUserFormData {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: ClientRole;
  permissions: Permission[];
  phone?: string;
  department?: string;
  position?: string;
  isActive: boolean;
}

export interface WarehouseFormData {
  name: string;
  code: string;
  address: string;
  phone?: string;
  email?: string;
  managerId?: string;
  type: 'main' | 'branch' | 'storage' | 'distribution';
  isActive: boolean;
}

export interface POSTerminalFormData {
  name: string;
  code: string;
  location: string;
  warehouseId?: string;
  cashierId?: string;
  hardware: {
    printer: boolean;
    scanner: boolean;
    cashDrawer: boolean;
    cardReader: boolean;
    display: boolean;
  };
  settings: {
    allowDiscounts: boolean;
    allowReturns: boolean;
    requireManagerApproval: boolean;
    autoOpenDrawer: boolean;
    printReceipts: boolean;
  };
  isActive: boolean;
}

// Constants
export const CLIENT_ROLES: { value: ClientRole; label: string; description: string }[] = [
  { value: 'client_admin', label: 'Client Admin', description: 'Full access to all client features' },
  { value: 'warehouse_manager', label: 'Warehouse Manager', description: 'Manage warehouse operations' },
  { value: 'warehouse_staff', label: 'Warehouse Staff', description: 'Basic warehouse operations' },
  { value: 'pos_manager', label: 'POS Manager', description: 'Manage POS terminals and sales' },
  { value: 'pos_cashier', label: 'POS Cashier', description: 'Process sales transactions' },
  { value: 'inventory_manager', label: 'Inventory Manager', description: 'Manage inventory and stock' },
  { value: 'sales_manager', label: 'Sales Manager', description: 'Manage sales and customers' },
  { value: 'accountant', label: 'Accountant', description: 'Access financial reports and data' },
  { value: 'viewer', label: 'Viewer', description: 'Read-only access to reports' }
];

export const CLIENT_MODULES: ClientModule[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    icon: '📊',
    description: 'Overview and analytics',
    path: '/client/dashboard',
    enabled: true,
    requiredRole: ['client_admin', 'warehouse_manager', 'pos_manager', 'inventory_manager', 'sales_manager'],
    requiredPermissions: ['dashboard.view'],
    category: 'core'
  },
  {
    id: 'inventory',
    name: 'Inventory',
    icon: '📦',
    description: 'Product and stock management',
    path: '/client/inventory',
    enabled: true,
    requiredRole: ['client_admin', 'warehouse_manager', 'inventory_manager'],
    requiredPermissions: ['inventory.view'],
    category: 'inventory'
  },
  {
    id: 'warehouse',
    name: 'Warehouse',
    icon: '🏭',
    description: 'Warehouse operations',
    path: '/client/warehouse',
    enabled: true,
    requiredRole: ['client_admin', 'warehouse_manager', 'warehouse_staff'],
    requiredPermissions: ['warehouse.view'],
    category: 'warehouse'
  },
  {
    id: 'pos',
    name: 'Point of Sale',
    icon: '💰',
    description: 'POS terminals and sales',
    path: '/client/pos',
    enabled: true,
    requiredRole: ['client_admin', 'pos_manager', 'pos_cashier'],
    requiredPermissions: ['pos.view'],
    category: 'pos'
  },
  {
    id: 'orders',
    name: 'Orders',
    icon: '📋',
    description: 'Order management',
    path: '/client/orders',
    enabled: true,
    requiredRole: ['client_admin', 'sales_manager', 'warehouse_manager'],
    requiredPermissions: ['orders.view'],
    category: 'sales'
  },
  {
    id: 'customers',
    name: 'Customers',
    icon: '👥',
    description: 'Customer management',
    path: '/client/customers',
    enabled: true,
    requiredRole: ['client_admin', 'sales_manager'],
    requiredPermissions: ['customers.view'],
    category: 'sales'
  },
  {
    id: 'reports',
    name: 'Reports',
    icon: '📈',
    description: 'Analytics and reports',
    path: '/client/reports',
    enabled: true,
    requiredRole: ['client_admin', 'warehouse_manager', 'pos_manager', 'inventory_manager', 'sales_manager', 'accountant', 'viewer'],
    requiredPermissions: ['reports.view'],
    category: 'reports'
  },
  {
    id: 'settings',
    name: 'Settings',
    icon: '⚙️',
    description: 'Client configuration',
    path: '/client/settings',
    enabled: true,
    requiredRole: ['client_admin'],
    requiredPermissions: ['settings.view'],
    category: 'settings'
  }
];

export const WAREHOUSE_TYPES = [
  { value: 'main', label: 'Main Warehouse', description: 'Primary storage facility' },
  { value: 'branch', label: 'Branch Warehouse', description: 'Secondary storage location' },
  { value: 'storage', label: 'Storage Facility', description: 'Long-term storage' },
  { value: 'distribution', label: 'Distribution Center', description: 'Order fulfillment hub' }
];

export const WAREHOUSE_ZONE_TYPES = [
  { value: 'receiving', label: 'Receiving', description: 'Incoming goods area' },
  { value: 'storage', label: 'Storage', description: 'Main storage area' },
  { value: 'picking', label: 'Picking', description: 'Order picking area' },
  { value: 'shipping', label: 'Shipping', description: 'Outgoing goods area' },
  { value: 'returns', label: 'Returns', description: 'Returned items area' }
];
