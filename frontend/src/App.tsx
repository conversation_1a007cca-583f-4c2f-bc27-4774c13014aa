import React, { Suspense } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from './hooks/useAuth'
import SimpleLayout from './components/Layout/SimpleLayout'
import LoadingSpinner from './components/LoadingSpinner'

// Lazy load pages for better performance
const HandoDashboard = React.lazy(() => import('./pages/HandoDashboard'))
const HandoProducts = React.lazy(() => import('./pages/HandoProducts'))
const Login = React.lazy(() => import('./pages/Login'))
const Products = React.lazy(() => import('./pages/Products'))
const ProductCatalog = React.lazy(() => import('./pages/ProductCatalog'))
const Orders = React.lazy(() => import('./pages/Orders'))
const Customers = React.lazy(() => import('./pages/Customers'))
const Reports = React.lazy(() => import('./pages/Reports'))
const Settings = React.lazy(() => import('./pages/Settings'))
const Plugins = React.lazy(() => import('./pages/Plugins'))
const TallyIntegration = React.lazy(() => import('./pages/TallyIntegration'))
const Profile = React.lazy(() => import('./pages/Profile'))

// Protected Route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return <LoadingSpinner />
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  return <>{children}</>
}

// Public Route component (redirects to dashboard if already authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return <LoadingSpinner />
  }

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />
  }

  return <>{children}</>
}

// Loading fallback component
const PageLoadingFallback: React.FC = () => (
  <div className="flex items-center justify-center min-h-screen">
    <LoadingSpinner />
  </div>
)

// Main App component
const App: React.FC = () => {
  return (
    <div className="App">
      <Suspense fallback={<PageLoadingFallback />}>
        <Routes>
          {/* Public routes */}
          <Route
            path="/login"
            element={
              <PublicRoute>
                <Login />
              </PublicRoute>
            }
          />

          {/* Protected routes with Simple layout */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <SimpleLayout />
              </ProtectedRoute>
            }
          >
            {/* Dashboard */}
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<HandoDashboard />} />

            {/* Products */}
            <Route path="products" element={<ProductCatalog />} />
            <Route path="product-catalog" element={<ProductCatalog />} />

            {/* Inventory Management */}
            <Route path="inventory" element={<ProductCatalog />} />
            <Route path="orders" element={<Orders />} />
            <Route path="customers" element={<Customers />} />

            {/* Analytics & Reports */}
            <Route path="reports" element={<Reports />} />

            {/* User Profile */}
            <Route path="profile" element={<Profile />} />

            {/* Settings */}
            <Route path="settings" element={<Settings />} />

            {/* Plugins & Integrations */}
            <Route path="plugins" element={<Plugins />} />
            <Route path="tally-integration" element={<TallyIntegration />} />
          </Route>

          {/* Catch all route - redirect to dashboard if authenticated, login if not */}
          <Route
            path="*"
            element={
              <ProtectedRoute>
                <Navigate to="/dashboard" replace />
              </ProtectedRoute>
            }
          />
        </Routes>
      </Suspense>
    </div>
  )
}

export default App
