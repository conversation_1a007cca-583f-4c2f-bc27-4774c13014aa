import React, { Suspense } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from './hooks/useAuth'
import SimpleLayout from './components/Layout/SimpleLayout'
import LoadingSpinner from './components/LoadingSpinner'

// Lazy load pages for better performance
const HandoDashboard = React.lazy(() => import('./pages/HandoDashboard'))
const HandoProducts = React.lazy(() => import('./pages/HandoProducts'))
const Login = React.lazy(() => import('./pages/Login'))
const Products = React.lazy(() => import('./pages/Products'))
const ProductCatalog = React.lazy(() => import('./pages/ProductCatalog'))
const Orders = React.lazy(() => import('./pages/Orders'))
const Customers = React.lazy(() => import('./pages/Customers'))
const Reports = React.lazy(() => import('./pages/Reports'))
const Settings = React.lazy(() => import('./pages/Settings'))
const Plugins = React.lazy(() => import('./pages/Plugins'))
const TallyIntegration = React.lazy(() => import('./pages/TallyIntegration'))
const ClientManagement = React.lazy(() => import('./pages/ClientManagement'))
const LicenseValidation = React.lazy(() => import('./pages/LicenseValidation'))
const Analytics = React.lazy(() => import('./pages/Analytics'))
const Billing = React.lazy(() => import('./pages/Billing'))
const Profile = React.lazy(() => import('./pages/Profile'))
const PaymentGateways = React.lazy(() => import('./pages/PaymentGateways'))

// Client Panel Components
const ClientLayout = React.lazy(() => import('./components/Layout/ClientLayout'))
const ClientDashboard = React.lazy(() => import('./pages/client/ClientDashboard'))
const WarehousePanel = React.lazy(() => import('./pages/client/WarehousePanel'))
const POSPanel = React.lazy(() => import('./pages/client/POSPanel'))
const ClientInventory = React.lazy(() => import('./pages/client/ClientInventory'))
const ClientOrders = React.lazy(() => import('./pages/client/ClientOrders'))

// Protected Route component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return <LoadingSpinner />
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  return <>{children}</>
}

// Public Route component (redirects to dashboard if already authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return <LoadingSpinner />
  }

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />
  }

  return <>{children}</>
}

// Loading fallback component
const PageLoadingFallback: React.FC = () => (
  <div className="flex items-center justify-center min-h-screen">
    <LoadingSpinner />
  </div>
)

// Main App component
const App: React.FC = () => {
  return (
    <div className="App">
      <Suspense fallback={<PageLoadingFallback />}>
        <Routes>
          {/* Public routes */}
          <Route
            path="/login"
            element={
              <PublicRoute>
                <Login />
              </PublicRoute>
            }
          />

          {/* Protected routes with Simple layout */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <SimpleLayout />
              </ProtectedRoute>
            }
          >
            {/* Dashboard */}
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<HandoDashboard />} />

            {/* Products */}
            <Route path="products" element={<ProductCatalog />} />
            <Route path="product-catalog" element={<ProductCatalog />} />

            {/* Inventory Management */}
            <Route path="inventory" element={<ProductCatalog />} />
            <Route path="orders" element={<Orders />} />
            <Route path="customers" element={<Customers />} />

            {/* Analytics & Reports */}
            <Route path="reports" element={<Reports />} />

            {/* User Profile */}
            <Route path="profile" element={<Profile />} />

            {/* Settings */}
            <Route path="settings" element={<Settings />} />

            {/* Plugins & Integrations */}
            <Route path="plugins" element={<Plugins />} />
            <Route path="tally-integration" element={<TallyIntegration />} />

            {/* SAAS Management */}
            <Route path="client-management" element={<ClientManagement />} />
            <Route path="license-validation" element={<LicenseValidation />} />
            <Route path="analytics" element={<Analytics />} />
            <Route path="billing" element={<Billing />} />
            <Route path="payment-gateways" element={<PaymentGateways />} />
          </Route>

          {/* Client Panel Routes */}
          <Route path="client" element={<ClientLayout />}>
            <Route path="dashboard" element={<ClientDashboard />} />
            <Route path="warehouse" element={<WarehousePanel />} />
            <Route path="pos" element={<POSPanel />} />
            <Route path="inventory" element={<ClientInventory />} />
            <Route path="orders" element={<ClientOrders />} />
            <Route path="customers" element={<div className="p-4"><h3>Customers Module</h3><p>Coming soon...</p></div>} />
            <Route path="reports" element={<div className="p-4"><h3>Reports Module</h3><p>Coming soon...</p></div>} />
            <Route path="settings" element={<div className="p-4"><h3>Client Settings</h3><p>Coming soon...</p></div>} />
          </Route>

          {/* Catch all route - redirect to dashboard if authenticated, login if not */}
          <Route
            path="*"
            element={
              <ProtectedRoute>
                <Navigate to="/dashboard" replace />
              </ProtectedRoute>
            }
          />
        </Routes>
      </Suspense>
    </div>
  )
}

export default App
