/*
Template Name: Hando - Responsive Bootstrap 5 Admin Dashboard
Author: Zoyothemes
Version: 1.0.0
Website: https://zoyothemes.com/
Contact: <EMAIL>
File: Main Css File
*/

/* Import Google Fonts and Icon Libraries */
@import url('https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');
@import url('https://cdn.materialdesignicons.com/6.5.95/css/materialdesignicons.min.css');

/* Root Variables - Authentic Hando Colors */
:root {
  --bs-primary: #3b82f6;
  --bs-secondary: #6366f1;
  --bs-success: #10b981;
  --bs-info: #06b6d4;
  --bs-warning: #f59e0b;
  --bs-danger: #ef4444;
  --bs-light: #f8fafc;
  --bs-dark: #1e293b;
  --bs-body-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --bs-body-font-size: 0.875rem;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.5;
  --bs-body-color: #64748b;
  --bs-body-bg: #f8fafc;
  --bs-sidebar-bg: #ffffff;
  --bs-sidebar-width: 260px;
  --bs-topbar-height: 70px;
}

/* Base Styles */
body {
  font-family: var(--bs-body-font-family);
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  color: var(--bs-body-color);
  background-color: var(--bs-body-bg);
}

/* App Layout */
#app-layout {
  display: flex;
  min-height: 100vh;
}

/* Sidebar Styles */
.app-sidebar-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: var(--bs-sidebar-width);
  height: 100vh;
  background: var(--bs-sidebar-bg);
  border-right: 1px solid #e2e8f0;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 0 35px 0 rgba(154, 161, 171, 0.15);
}

.logo-box {
  padding: 1.5rem 1.25rem;
  border-bottom: 1px solid #e9ecef;
}

.logo-box .logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo-box .logo img {
  height: 24px;
}

/* Sidebar Menu */
#side-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  padding: 1rem 0;
}

.menu-title {
  padding: 0.75rem 1.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

#side-menu > li > a {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.25rem;
  color: #495057;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border-radius: 0;
}

#side-menu > li > a:hover {
  background-color: #f8f9fa;
  color: var(--bs-primary);
}

#side-menu > li > a.active {
  background-color: var(--bs-primary);
  color: #fff;
}

#side-menu > li > a i {
  width: 20px;
  height: 20px;
  margin-right: 0.75rem;
  font-size: 18px;
}

/* Content Page */
.content-page {
  margin-left: var(--bs-sidebar-width);
  min-height: 100vh;
  background-color: var(--bs-body-bg);
  transition: all 0.3s ease;
}

.content {
  padding: 1.5rem;
}

/* Page Title */
.page-title-box {
  margin-bottom: 1.5rem;
}

.page-title-box h4 {
  color: var(--bs-dark);
  margin-bottom: 0;
}

.breadcrumb {
  background: transparent;
  padding: 0;
  margin: 0;
  font-size: 0.8125rem;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: "/";
  color: #94a3b8;
}

/* Topbar */
.topbar-custom {
  background: #fff;
  border-bottom: 1px solid #e9ecef;
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 999;
}

.topbar-custom .container-fluid {
  padding: 1rem 1.5rem;
}

.topnav-menu {
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  list-style: none;
}

.topnav-menu li {
  margin-right: 1rem;
}

.button-toggle-menu {
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 0.375rem;
  color: #6c757d;
  transition: all 0.2s ease;
}

.button-toggle-menu:hover {
  background-color: #f8f9fa;
  color: var(--bs-primary);
}

/* Search */
.app-search .topbar-search {
  position: relative;
}

.app-search input {
  padding-left: 2.5rem;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  background-color: #f8f9fa;
  width: 300px;
}

.app-search input:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
  background-color: #fff;
}

/* Cards */
.card {
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  background-color: #fff;
}

.card-header {
  padding: 1rem 1.25rem;
  background-color: transparent;
  border-bottom: 1px solid #e9ecef;
}

.card-body {
  padding: 1.25rem;
}

/* Widget Styles */
.widget-first {
  position: relative;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.widget-first:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.widget-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bg-primary {
  background-color: var(--bs-primary) !important;
}

.bg-success {
  background-color: var(--bs-success) !important;
}

.bg-info {
  background-color: var(--bs-info) !important;
}

.bg-warning {
  background-color: var(--bs-warning) !important;
}

/* Badge Styles */
.badge-custom-second {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
}

.bg-success-subtle {
  background-color: rgba(16, 185, 129, 0.1) !important;
}

.text-success {
  color: var(--bs-success) !important;
}

/* Progress Bar Styles */
.progress {
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  transition: width 0.3s ease;
}

.progress-bar.bg-primary {
  background-color: var(--bs-primary);
}

.progress-bar.bg-success {
  background-color: var(--bs-success);
}

.progress-bar.bg-info {
  background-color: var(--bs-info);
}

.progress-bar.bg-warning {
  background-color: var(--bs-warning);
}

/* Table Styles */
.table {
  border-collapse: separate;
  border-spacing: 0;
}

.table th {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
  color: var(--bs-dark);
  padding: 0.75rem;
}

.table td {
  padding: 0.75rem;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
}

.table-hover tbody tr:hover {
  background-color: #f8fafc;
}

/* Avatar Styles */
.avatar-sm {
  width: 32px;
  height: 32px;
  font-size: 0.75rem;
}

/* Badge Styles */
.badge {
  font-weight: 500;
  font-size: 0.75rem;
}

.bg-success-subtle {
  background-color: rgba(16, 185, 129, 0.1) !important;
}

.bg-warning-subtle {
  background-color: rgba(245, 158, 11, 0.1) !important;
}

.bg-danger-subtle {
  background-color: rgba(239, 68, 68, 0.1) !important;
}

.text-warning {
  color: var(--bs-warning) !important;
}

.text-danger {
  color: var(--bs-danger) !important;
}

/* Form Controls */
.form-select-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
}

.form-select-sm:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
}

/* Pagination */
.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.375rem;
}

.pagination .page-item.active .page-link {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
}

/* Chart Container */
.chart-container {
  position: relative;
  overflow: hidden;
}

.circular-progress {
  transform: rotate(-90deg);
}

/* Country Stats */
.country-stats .progress {
  background-color: #f1f5f9;
}

.flag-icon {
  border-radius: 2px;
  flex-shrink: 0;
}

/* Icon Fixes */
.nav-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.widget-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* Button Icon Alignment */
.btn svg {
  vertical-align: middle;
}

.btn-sm svg {
  width: 14px;
  height: 14px;
}

/* Search Icon */
.search-box {
  position: relative;
}

.search-box svg {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.search-box input {
  padding-left: 35px;
}

/* Header Improvements */
.header-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.pro-user-name {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

/* Sidebar Improvements */
.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #64748b;
  text-decoration: none;
  border-radius: 0.375rem;
  margin-bottom: 0.25rem;
  transition: all 0.2s ease;
}

.nav-link:hover {
  background-color: #f1f5f9;
  color: #334155;
}

.nav-link.active {
  background-color: var(--bs-primary);
  color: white;
}

.nav-text {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Logo Improvements */
.logo-box {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 1rem;
}

.logo {
  text-decoration: none;
}

/* Card Improvements */
.card {
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 1.5rem;
}

.card-body {
  padding: 1.5rem;
}

/* Widget Card Specific */
.widget-first .card-body {
  padding: 1.25rem;
}

/* Hando Template Specific Styles */

/* Vertical Menu */
.vertical-menu {
  width: 260px;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  background: #fff;
  box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
  border-right: 1px solid #e9ecef;
}

.navbar-brand-box {
  padding: 1rem 1.5rem;
  text-align: center;
  background: #fff;
}

.navbar-brand-box .logo {
  line-height: 1;
}

.logo-sm, .logo-lg {
  display: inline-block;
}

/* Sidebar Menu */
#sidebar-menu {
  padding: 10px 0 30px 0;
}

#sidebar-menu .metismenu {
  background: transparent;
}

#sidebar-menu .metismenu li {
  display: block;
  width: 100%;
}

#sidebar-menu .metismenu .menu-title {
  padding: 12px 20px;
  letter-spacing: 0.05em;
  pointer-events: none;
  cursor: default;
  font-size: 11px;
  text-transform: uppercase;
  color: #99abb4;
  font-weight: 600;
}

#sidebar-menu .metismenu a {
  display: block;
  padding: 12px 20px;
  color: #545862;
  position: relative;
  font-size: 13px;
  font-weight: 500;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  margin: 0 10px;
  border-radius: 3px;
}

#sidebar-menu .metismenu a:hover {
  color: #5664d2;
  text-decoration: none;
  background-color: rgba(86, 100, 210, 0.06);
}

#sidebar-menu .metismenu a.mm-active {
  color: #5664d2;
  background-color: rgba(86, 100, 210, 0.06);
}

#sidebar-menu .metismenu a i {
  display: inline-block;
  width: 20px;
  font-size: 16px;
  margin-right: 8px;
  vertical-align: middle;
  color: #99abb4;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
}

#sidebar-menu .metismenu a:hover i {
  color: #5664d2;
}

#sidebar-menu .metismenu a.mm-active i {
  color: #5664d2;
}

/* Header */
#page-topbar {
  position: fixed;
  top: 0;
  right: 0;
  left: 260px;
  z-index: 1000;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
  padding: 0 calc(24px / 2) 0 0;
}

.navbar-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0;
  padding: 0 20px 0 0;
  color: #545862;
  background-color: transparent;
  min-height: 70px;
}

.header-item {
  height: 70px;
  box-shadow: none !important;
  color: #545862;
  border: 0;
  background-color: transparent !important;
}

.header-item:hover {
  color: #5664d2;
}

.header-profile-user {
  height: 36px;
  width: 36px;
}

.noti-icon {
  position: relative;
}

.noti-icon .badge {
  position: absolute;
  top: 8px;
  right: 8px;
}

/* App Search */
.app-search {
  padding: 0 15px;
}

.app-search .form-control {
  border: none;
  height: 38px;
  padding-left: 40px;
  padding-right: 20px;
  background-color: #f6f6f6;
  box-shadow: none;
  border-radius: 30px;
  font-size: 13px;
}

.app-search span {
  position: absolute;
  z-index: 10;
  font-size: 16px;
  left: 28px;
  top: 0;
  line-height: 38px;
  color: #74788d;
}

/* Main Content */
.main-content {
  margin-left: 260px;
  overflow: hidden;
  padding: 0;
  margin-top: 70px;
  min-height: calc(100vh - 70px);
}

.page-content {
  padding: calc(24px + 24px) calc(24px / 2) 60px calc(24px / 2);
}

/* Simple Hover Effects */
.hover-bg-light:hover {
  background-color: #f8f9fa !important;
}

.btn:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* Responsive */
@media (max-width: 992px) {
  .vertical-menu {
    display: none;
  }

  #page-topbar {
    left: 0;
  }

  .main-content {
    margin-left: 0;
  }
}

/* Progress Bars */
.progress {
  height: 0.5rem;
  border-radius: 0.25rem;
  background-color: #e9ecef;
}

.progress-bar {
  border-radius: 0.25rem;
}

/* Badges */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
}

.badge-custom-second {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

.bg-success-subtle {
  background-color: rgba(0, 184, 148, 0.1);
}

.text-success {
  color: var(--bs-success);
}

/* Buttons */
.btn {
  font-weight: 500;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
}

.btn-primary:hover {
  background-color: #5a4fcf;
  border-color: #5a4fcf;
}

/* Utilities */
.fs-14 { font-size: 0.875rem; }
.fs-15 { font-size: 0.9375rem; }
.fs-16 { font-size: 1rem; }
.fs-18 { font-size: 1.125rem; }
.fs-22 { font-size: 1.375rem; }
.fs-26 { font-size: 1.625rem; }
.fs-28 { font-size: 1.75rem; }

.fw-semibold { font-weight: 600; }

/* Responsive */
@media (max-width: 991.98px) {
  .app-sidebar-menu {
    transform: translateX(-100%);
  }
  
  .app-sidebar-menu.show {
    transform: translateX(0);
  }
  
  .content-page {
    margin-left: 0;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --bs-body-color: #e9ecef;
    --bs-body-bg: #212529;
  }
  
  .app-sidebar-menu {
    background: #2d3436;
    border-right-color: #495057;
  }
  
  .topbar-custom {
    background: #2d3436;
    border-bottom-color: #495057;
  }
  
  .card {
    background-color: #2d3436;
    border-color: #495057;
  }
}
