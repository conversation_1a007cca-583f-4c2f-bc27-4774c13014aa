import React, { useState } from 'react';
import { Product } from '../../types/product';

interface ProductTableProps {
  products?: Product[];
  onEdit?: (product: Product) => void;
  onDelete?: (productId: string) => void;
  onView?: (product: Product) => void;
}

const ProductTable: React.FC<ProductTableProps> = ({
  products = [],
  onEdit,
  onDelete,
  onView
}) => {
  const [sortField, setSortField] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [searchTerm, setSearchTerm] = useState('');

  const displayProducts = products || [];

  // Filter products based on search term
  const filteredProducts = displayProducts.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.categoryNames && product.categoryNames.some(cat =>
      cat.toLowerCase().includes(searchTerm.toLowerCase())
    ))
  );

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    const aValue = (a as any)[sortField];
    const bValue = (b as any)[sortField];

    if (aValue == null && bValue == null) return 0;
    if (aValue == null) return 1;
    if (bValue == null) return -1;

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getStockStatus = (stock: number) => {
    if (stock === 0) return { text: 'Out of Stock', class: 'bg-danger' };
    if (stock < 10) return { text: 'Low Stock', class: 'bg-warning' };
    return { text: 'In Stock', class: 'bg-success' };
  };

  const getSortIcon = (field: string) => {
    if (sortField !== field) return '↕️';
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  return (
    <div className="card">
      <div className="card-header">
        <div className="row align-items-center">
          <div className="col-md-6">
            <h5 className="card-title mb-0">Products</h5>
          </div>
          <div className="col-md-6">
            <div className="d-flex justify-content-end">
              <div className="search-box me-2">
                <input
                  type="text"
                  className="form-control"
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <button className="btn btn-primary btn-sm">
                ➕ Add Product
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div className="card-body p-0">
        <div className="table-responsive">
          <table className="table table-hover mb-0">
            <thead className="table-light">
              <tr>
                <th 
                  className="border-0 px-3 py-3 cursor-pointer"
                  onClick={() => handleSort('name')}
                >
                  Product {getSortIcon('name')}
                </th>
                <th 
                  className="border-0 px-3 py-3 cursor-pointer"
                  onClick={() => handleSort('sku')}
                >
                  SKU {getSortIcon('sku')}
                </th>
                <th
                  className="border-0 px-3 py-3 cursor-pointer"
                  onClick={() => handleSort('categoryNames')}
                >
                  Category {getSortIcon('categoryNames')}
                </th>
                <th 
                  className="border-0 px-3 py-3 cursor-pointer"
                  onClick={() => handleSort('price')}
                >
                  Price {getSortIcon('price')}
                </th>
                <th 
                  className="border-0 px-3 py-3 cursor-pointer"
                  onClick={() => handleSort('stock')}
                >
                  Stock {getSortIcon('stock')}
                </th>
                <th className="border-0 px-3 py-3">Status</th>
                <th className="border-0 px-3 py-3">Actions</th>
              </tr>
            </thead>
            <tbody>
              {sortedProducts.map((product) => {
                const stockStatus = getStockStatus(product.stock);
                return (
                  <tr key={product.id}>
                    <td className="px-3 py-3">
                      <div className="d-flex align-items-center">
                        <div className="flex-shrink-0 me-3">
                          <div 
                            className="bg-light rounded d-flex align-items-center justify-content-center"
                            style={{width: '40px', height: '40px'}}
                          >
                            📦
                          </div>
                        </div>
                        <div>
                          <h6 className="mb-0 fw-medium">{product.name}</h6>
                          <small className="text-muted">ID: {product.id}</small>
                        </div>
                      </div>
                    </td>
                    <td className="px-3 py-3">
                      <span className="fw-medium">{product.sku}</span>
                    </td>
                    <td className="px-3 py-3">
                      {product.categoryNames && product.categoryNames.length > 0 ? (
                        product.categoryNames.map((category, index) => (
                          <span key={index} className="badge bg-light text-dark me-1">
                            {category}
                          </span>
                        ))
                      ) : (
                        <span className="text-muted">No category</span>
                      )}
                    </td>
                    <td className="px-3 py-3">
                      <span className="fw-medium">${product.price.toFixed(2)}</span>
                    </td>
                    <td className="px-3 py-3">
                      <span className="fw-medium">{product.stock}</span>
                    </td>
                    <td className="px-3 py-3">
                      <span className={`badge ${stockStatus.class} text-white`}>
                        {stockStatus.text}
                      </span>
                    </td>
                    <td className="px-3 py-3">
                      <div className="d-flex gap-1">
                        <button 
                          className="btn btn-sm btn-outline-primary"
                          onClick={() => onView?.(product)}
                          title="View"
                        >
                          👁️
                        </button>
                        <button 
                          className="btn btn-sm btn-outline-secondary"
                          onClick={() => onEdit?.(product)}
                          title="Edit"
                        >
                          ✏️
                        </button>
                        <button 
                          className="btn btn-sm btn-outline-danger"
                          onClick={() => onDelete?.(product.id)}
                          title="Delete"
                        >
                          🗑️
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        
        {sortedProducts.length === 0 && (
          <div className="text-center py-5">
            <div className="text-muted mb-3" style={{fontSize: '48px'}}>📦</div>
            <h5 className="text-muted">No products found</h5>
            <p className="text-muted">Try adjusting your search criteria</p>
          </div>
        )}
      </div>
      
      <div className="card-footer">
        <div className="d-flex justify-content-between align-items-center">
          <small className="text-muted">
            Showing {sortedProducts.length} of {displayProducts.length} products
          </small>
          <nav>
            <ul className="pagination pagination-sm mb-0">
              <li className="page-item disabled">
                <span className="page-link">Previous</span>
              </li>
              <li className="page-item active">
                <span className="page-link">1</span>
              </li>
              <li className="page-item">
                <a className="page-link" href="#">2</a>
              </li>
              <li className="page-item">
                <a className="page-link" href="#">Next</a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
  );
};

export default ProductTable;
