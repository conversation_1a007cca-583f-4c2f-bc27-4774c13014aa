import React, { useState, useEffect } from 'react';

interface Category {
  id: string;
  name: string;
  description: string;
  parentId: string | null;
  children?: Category[];
}

interface ProductOption {
  id: string;
  name: string;
  type: 'text' | 'select' | 'multiselect' | 'number' | 'boolean' | 'date' | 'color' | 'file';
  required: boolean;
  values: string[];
  defaultValue?: any;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

interface ProductVariant {
  id: string;
  sku: string;
  price: number;
  stock: number;
  options: { [optionId: string]: any };
  image?: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
}

interface Product {
  id?: string;
  name: string;
  description: string;
  shortDescription: string;
  sku: string;
  price: number;
  comparePrice?: number;
  cost?: number;
  stock: number;
  minStock: number;
  trackQuantity: boolean;
  allowBackorder: boolean;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  categories: string[];
  tags: string[];
  images: string[];
  status: 'active' | 'draft' | 'archived';
  visibility: 'public' | 'private' | 'hidden';
  featured: boolean;
  seo: {
    title: string;
    description: string;
    keywords: string;
  };
  options: ProductOption[];
  variants: ProductVariant[];
  customFields: { [key: string]: any };
}

interface AdvancedProductFormProps {
  product?: Product | null;
  categories: Category[];
  onSave: (product: ProductFormData) => void;
  onCancel: () => void;
  isOpen: boolean;
}

const AdvancedProductForm: React.FC<AdvancedProductFormProps> = ({
  product,
  categories,
  onSave,
  onCancel,
  isOpen
}) => {
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    shortDescription: '',
    sku: '',
    price: 0,
    comparePrice: 0,
    costPrice: 0,
    stock: 0,
    minStock: 10,
    trackStock: true,
    allowBackorder: false,
    weight: 0,
    categories: [],
    categoryNames: [],
    tags: [],
    images: [],
    status: 'active',
    visibility: 'public',
    featured: false,
    options: [],
    variants: []
  });

  const [activeTab, setActiveTab] = useState('basic');
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [newTag, setNewTag] = useState('');
  const [newImage, setNewImage] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name,
        description: product.description,
        shortDescription: product.shortDescription || '',
        sku: product.sku,
        price: product.price,
        comparePrice: product.comparePrice || 0,
        costPrice: product.costPrice || 0,
        stock: product.stock,
        minStock: product.minStock || 0,
        trackStock: product.trackStock,
        allowBackorder: product.allowBackorder,
        weight: product.weight || 0,
        categories: product.categories,
        categoryNames: product.categoryNames,
        tags: product.tags,
        images: product.images,
        status: product.status,
        visibility: product.visibility,
        featured: product.featured,
        seoTitle: product.seoTitle,
        seoDescription: product.seoDescription,
        seoKeywords: product.seoKeywords,
        options: product.options || [],
        variants: product.variants || []
      });
      setSelectedCategories(new Set(product.categories));
    } else {
      // Reset form for new product
      setFormData({
        name: '',
        description: '',
        shortDescription: '',
        sku: '',
        price: 0,
        comparePrice: 0,
        costPrice: 0,
        stock: 0,
        minStock: 10,
        trackStock: true,
        allowBackorder: false,
        weight: 0,
        categories: [],
        categoryNames: [],
        tags: [],
        images: [],
        status: 'active',
        visibility: 'public',
        featured: false,
        options: [],
        variants: []
      });
      setSelectedCategories(new Set());
    }
    setErrors({});
  }, [product, isOpen]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked :
              type === 'number' ? parseFloat(value) || 0 : value
    }));

    // Auto-generate SKU from name
    if (name === 'name' && !product) {
      const sku = value.toUpperCase()
        .replace(/[^A-Z0-9\s]/g, '')
        .replace(/\s+/g, '-')
        .substring(0, 20);

      setFormData(prev => ({
        ...prev,
        sku: sku
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleCategoryToggle = (categoryId: string) => {
    const newSelected = new Set(selectedCategories);
    if (newSelected.has(categoryId)) {
      newSelected.delete(categoryId);
    } else {
      newSelected.add(categoryId);
    }
    setSelectedCategories(newSelected);

    // Get category names for the selected categories
    const categoryNames = Array.from(newSelected).map(id => {
      const category = categories.find(cat => cat.id === id);
      return category ? category.name : '';
    }).filter(name => name !== '');

    setFormData(prev => ({
      ...prev,
      categories: Array.from(newSelected),
      categoryNames: categoryNames
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const addImage = () => {
    if (newImage.trim() && !formData.images.includes(newImage.trim())) {
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, newImage.trim()]
      }));
      setNewImage('');
    }
  };

  const removeImage = (imageToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter(img => img !== imageToRemove)
    }));
  };

  const addProductOption = () => {
    const newOption: ProductOption = {
      id: `option_${Date.now()}`,
      name: '',
      type: 'text',
      required: false,
      values: [],
      validation: {}
    };

    setFormData(prev => ({
      ...prev,
      options: [...prev.options, newOption]
    }));
  };

  const updateProductOption = (optionId: string, updates: Partial<ProductOption>) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map(option =>
        option.id === optionId ? { ...option, ...updates } : option
      )
    }));
  };

  const removeProductOption = (optionId: string) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.filter(option => option.id !== optionId)
    }));
  };

  const addOptionValue = (optionId: string, value: string) => {
    if (value.trim()) {
      setFormData(prev => ({
        ...prev,
        options: prev.options.map(option =>
          option.id === optionId 
            ? { ...option, values: [...option.values, value.trim()] }
            : option
        )
      }));
    }
  };

  const removeOptionValue = (optionId: string, valueToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map(option =>
        option.id === optionId 
          ? { ...option, values: option.values.filter(v => v !== valueToRemove) }
          : option
      )
    }));
  };

  const generateVariants = () => {
    // Generate all possible combinations of option values
    const optionsWithValues = formData.options.filter(opt => opt.values.length > 0);
    
    if (optionsWithValues.length === 0) return;

    const combinations = generateCombinations(optionsWithValues);
    
    const variants: ProductVariant[] = combinations.map((combo, index) => ({
      id: `variant_${Date.now()}_${index}`,
      sku: `${formData.sku}-${index + 1}`,
      price: formData.price,
      stock: formData.stock,
      options: combo,
      weight: formData.weight || 0
    }));

    setFormData(prev => ({
      ...prev,
      variants: variants
    }));
  };

  const generateCombinations = (options: ProductOption[]): Array<{ [optionId: string]: any }> => {
    if (options.length === 0) return [{}];
    
    const [firstOption, ...restOptions] = options;
    const restCombinations = generateCombinations(restOptions);
    
    const combinations: Array<{ [optionId: string]: any }> = [];
    
    for (const value of firstOption.values) {
      for (const restCombo of restCombinations) {
        combinations.push({
          [firstOption.id]: value,
          ...restCombo
        });
      }
    }
    
    return combinations;
  };

  const renderCategoryTree = (categories: Category[], level = 0): React.ReactNode => {
    return categories.map(category => (
      <div key={category.id} style={{ marginLeft: `${level * 20}px` }}>
        <div className="form-check">
          <input
            className="form-check-input"
            type="checkbox"
            id={`category_${category.id}`}
            checked={selectedCategories.has(category.id)}
            onChange={() => handleCategoryToggle(category.id)}
          />
          <label className="form-check-label" htmlFor={`category_${category.id}`}>
            {category.name}
          </label>
        </div>
        {category.children && category.children.length > 0 && (
          <div className="ms-3">
            {renderCategoryTree(category.children, level + 1)}
          </div>
        )}
      </div>
    ));
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.sku.trim()) {
      newErrors.sku = 'SKU is required';
    }

    if (formData.price <= 0) {
      newErrors.price = 'Price must be greater than 0';
    }

    if (formData.trackStock && formData.stock < 0) {
      newErrors.stock = 'Stock cannot be negative';
    }

    if (formData.categories.length === 0) {
      newErrors.categories = 'At least one category must be selected';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    onSave(formData);
  };

  if (!isOpen) return null;

  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-xl">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">
              {product ? 'Edit Product' : 'Add New Product'}
            </h5>
            <button type="button" className="btn-close" onClick={onCancel}></button>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="modal-body" style={{ maxHeight: '70vh', overflowY: 'auto' }}>
              {/* Tabs */}
              <ul className="nav nav-tabs mb-3">
                <li className="nav-item">
                  <button
                    type="button"
                    className={`nav-link ${activeTab === 'basic' ? 'active' : ''}`}
                    onClick={() => setActiveTab('basic')}
                  >
                    📝 Basic Info
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    type="button"
                    className={`nav-link ${activeTab === 'categories' ? 'active' : ''}`}
                    onClick={() => setActiveTab('categories')}
                  >
                    📁 Categories
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    type="button"
                    className={`nav-link ${activeTab === 'inventory' ? 'active' : ''}`}
                    onClick={() => setActiveTab('inventory')}
                  >
                    📦 Inventory
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    type="button"
                    className={`nav-link ${activeTab === 'options' ? 'active' : ''}`}
                    onClick={() => setActiveTab('options')}
                  >
                    ⚙️ Options
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    type="button"
                    className={`nav-link ${activeTab === 'variants' ? 'active' : ''}`}
                    onClick={() => setActiveTab('variants')}
                  >
                    🔄 Variants
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    type="button"
                    className={`nav-link ${activeTab === 'media' ? 'active' : ''}`}
                    onClick={() => setActiveTab('media')}
                  >
                    🖼️ Media
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    type="button"
                    className={`nav-link ${activeTab === 'seo' ? 'active' : ''}`}
                    onClick={() => setActiveTab('seo')}
                  >
                    🔍 SEO
                  </button>
                </li>
              </ul>

              {/* Basic Info Tab */}
              {activeTab === 'basic' && (
                <div className="tab-content">
                  <div className="row">
                    <div className="col-md-6 mb-3">
                      <label htmlFor="name" className="form-label">
                        Product Name <span className="text-danger">*</span>
                      </label>
                      <input
                        type="text"
                        className={`form-control ${errors.name ? 'is-invalid' : ''}`}
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="Enter product name"
                      />
                      {errors.name && <div className="invalid-feedback">{errors.name}</div>}
                    </div>

                    <div className="col-md-3 mb-3">
                      <label htmlFor="sku" className="form-label">
                        SKU <span className="text-danger">*</span>
                      </label>
                      <input
                        type="text"
                        className={`form-control ${errors.sku ? 'is-invalid' : ''}`}
                        id="sku"
                        name="sku"
                        value={formData.sku}
                        onChange={handleInputChange}
                        placeholder="Product SKU"
                      />
                      {errors.sku && <div className="invalid-feedback">{errors.sku}</div>}
                    </div>

                    <div className="col-md-3 mb-3">
                      <label htmlFor="barcode" className="form-label">Barcode</label>
                      <input
                        type="text"
                        className="form-control"
                        id="barcode"
                        name="barcode"
                        value={formData.barcode || ''}
                        onChange={handleInputChange}
                        placeholder="Product barcode"
                      />
                    </div>
                  </div>

                  <div className="mb-3">
                    <label htmlFor="shortDescription" className="form-label">Short Description</label>
                    <textarea
                      className="form-control"
                      id="shortDescription"
                      name="shortDescription"
                      rows={2}
                      value={formData.shortDescription}
                      onChange={handleInputChange}
                      placeholder="Brief product description"
                    />
                  </div>

                  <div className="mb-3">
                    <label htmlFor="description" className="form-label">Full Description</label>
                    <textarea
                      className="form-control"
                      id="description"
                      name="description"
                      rows={4}
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="Detailed product description"
                    />
                  </div>

                  <div className="row">
                    <div className="col-md-4 mb-3">
                      <label htmlFor="price" className="form-label">
                        Price <span className="text-danger">*</span>
                      </label>
                      <div className="input-group">
                        <span className="input-group-text">$</span>
                        <input
                          type="number"
                          className={`form-control ${errors.price ? 'is-invalid' : ''}`}
                          id="price"
                          name="price"
                          value={formData.price}
                          onChange={handleInputChange}
                          min="0"
                          step="0.01"
                        />
                        {errors.price && <div className="invalid-feedback">{errors.price}</div>}
                      </div>
                    </div>

                    <div className="col-md-4 mb-3">
                      <label htmlFor="comparePrice" className="form-label">Compare Price</label>
                      <div className="input-group">
                        <span className="input-group-text">$</span>
                        <input
                          type="number"
                          className="form-control"
                          id="comparePrice"
                          name="comparePrice"
                          value={formData.comparePrice}
                          onChange={handleInputChange}
                          min="0"
                          step="0.01"
                        />
                      </div>
                    </div>

                    <div className="col-md-4 mb-3">
                      <label htmlFor="costPrice" className="form-label">Cost per Item</label>
                      <div className="input-group">
                        <span className="input-group-text">$</span>
                        <input
                          type="number"
                          className="form-control"
                          id="costPrice"
                          name="costPrice"
                          value={formData.costPrice}
                          onChange={handleInputChange}
                          min="0"
                          step="0.01"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-md-4 mb-3">
                      <label htmlFor="status" className="form-label">Status</label>
                      <select
                        className="form-select"
                        id="status"
                        name="status"
                        value={formData.status}
                        onChange={handleInputChange}
                      >
                        <option value="active">Active</option>
                        <option value="draft">Draft</option>
                        <option value="archived">Archived</option>
                      </select>
                    </div>

                    <div className="col-md-4 mb-3">
                      <label htmlFor="visibility" className="form-label">Visibility</label>
                      <select
                        className="form-select"
                        id="visibility"
                        name="visibility"
                        value={formData.visibility}
                        onChange={handleInputChange}
                      >
                        <option value="public">Public</option>
                        <option value="private">Private</option>
                        <option value="hidden">Hidden</option>
                      </select>
                    </div>

                    <div className="col-md-4 mb-3">
                      <div className="form-check mt-4">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id="featured"
                          name="featured"
                          checked={formData.featured}
                          onChange={handleInputChange}
                        />
                        <label className="form-check-label" htmlFor="featured">
                          Featured Product
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="mb-3">
                    <label className="form-label">Tags</label>
                    <div className="input-group mb-2">
                      <input
                        type="text"
                        className="form-control"
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        placeholder="Add a tag"
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                      />
                      <button
                        type="button"
                        className="btn btn-outline-secondary"
                        onClick={addTag}
                      >
                        Add
                      </button>
                    </div>
                    <div className="d-flex flex-wrap gap-2">
                      {formData.tags.map((tag, index) => (
                        <span key={index} className="badge bg-secondary d-flex align-items-center">
                          {tag}
                          <button
                            type="button"
                            className="btn-close btn-close-white ms-2"
                            style={{ fontSize: '0.7em' }}
                            onClick={() => removeTag(tag)}
                          ></button>
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Categories Tab */}
              {activeTab === 'categories' && (
                <div className="tab-content">
                  <div className="mb-3">
                    <label className="form-label">
                      Select Categories <span className="text-danger">*</span>
                    </label>
                    {errors.categories && (
                      <div className="text-danger small mb-2">{errors.categories}</div>
                    )}
                    <div className="border rounded p-3" style={{ maxHeight: '400px', overflowY: 'auto' }}>
                      {categories.length === 0 ? (
                        <div className="text-center py-4">
                          <div className="text-muted mb-2" style={{ fontSize: '48px' }}>📁</div>
                          <p className="text-muted mb-0">No categories available</p>
                        </div>
                      ) : (
                        renderCategoryTree(categories)
                      )}
                    </div>
                    
                    {selectedCategories.size > 0 && (
                      <div className="mt-3">
                        <small className="text-muted">Selected categories:</small>
                        <div className="d-flex flex-wrap gap-2 mt-1">
                          {Array.from(selectedCategories).map(categoryId => {
                            const category = categories.find(c => c.id === categoryId);
                            return category ? (
                              <span key={categoryId} className="badge bg-primary">
                                {category.name}
                              </span>
                            ) : null;
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Inventory Tab */}
              {activeTab === 'inventory' && (
                <div className="tab-content">
                  <div className="row">
                    <div className="col-md-6 mb-3">
                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id="trackStock"
                          name="trackStock"
                          checked={formData.trackStock}
                          onChange={handleInputChange}
                        />
                        <label className="form-check-label" htmlFor="trackStock">
                          Track Stock
                        </label>
                      </div>
                    </div>

                    <div className="col-md-6 mb-3">
                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id="allowBackorder"
                          name="allowBackorder"
                          checked={formData.allowBackorder}
                          onChange={handleInputChange}
                        />
                        <label className="form-check-label" htmlFor="allowBackorder">
                          Allow Backorders
                        </label>
                      </div>
                    </div>
                  </div>

                  {formData.trackStock && (
                    <div className="row">
                      <div className="col-md-6 mb-3">
                        <label htmlFor="stock" className="form-label">Current Stock</label>
                        <input
                          type="number"
                          className={`form-control ${errors.stock ? 'is-invalid' : ''}`}
                          id="stock"
                          name="stock"
                          value={formData.stock}
                          onChange={handleInputChange}
                          min="0"
                        />
                        {errors.stock && <div className="invalid-feedback">{errors.stock}</div>}
                      </div>

                      <div className="col-md-6 mb-3">
                        <label htmlFor="minStock" className="form-label">Minimum Stock</label>
                        <input
                          type="number"
                          className="form-control"
                          id="minStock"
                          name="minStock"
                          value={formData.minStock}
                          onChange={handleInputChange}
                          min="0"
                        />
                      </div>
                    </div>
                  )}

                  <div className="mb-3">
                    <label htmlFor="weight" className="form-label">Weight (kg)</label>
                    <input
                      type="number"
                      className="form-control"
                      id="weight"
                      name="weight"
                      value={formData.weight}
                      onChange={handleInputChange}
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div className="row">
                    <div className="col-md-4 mb-3">
                      <label htmlFor="length" className="form-label">Length (cm)</label>
                      <input
                        type="number"
                        className="form-control"
                        id="length"
                        name="length"
                        value={formData.length || 0}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                      />
                    </div>

                    <div className="col-md-4 mb-3">
                      <label htmlFor="width" className="form-label">Width (cm)</label>
                      <input
                        type="number"
                        className="form-control"
                        id="width"
                        name="width"
                        value={formData.width || 0}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                      />
                    </div>

                    <div className="col-md-4 mb-3">
                      <label htmlFor="height" className="form-label">Height (cm)</label>
                      <input
                        type="number"
                        className="form-control"
                        id="height"
                        name="height"
                        value={formData.height || 0}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Options Tab */}
              {activeTab === 'options' && (
                <div className="tab-content">
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <div>
                      <h6 className="mb-0">Product Options</h6>
                      <small className="text-muted">Add options like size, color, material, etc.</small>
                    </div>
                    <button
                      type="button"
                      className="btn btn-outline-primary"
                      onClick={addProductOption}
                    >
                      ➕ Add Option
                    </button>
                  </div>

                  {formData.options.length === 0 ? (
                    <div className="text-center py-4">
                      <div className="text-muted mb-2" style={{ fontSize: '48px' }}>⚙️</div>
                      <p className="text-muted mb-0">No options added</p>
                      <small className="text-muted">Add options to create product variants</small>
                    </div>
                  ) : (
                    <div className="options-list">
                      {formData.options.map((option, index) => (
                        <div key={option.id} className="card mb-3">
                          <div className="card-header">
                            <div className="d-flex justify-content-between align-items-center">
                              <h6 className="mb-0">Option {index + 1}</h6>
                              <button
                                type="button"
                                className="btn btn-sm btn-outline-danger"
                                onClick={() => removeProductOption(option.id)}
                              >
                                🗑️ Remove
                              </button>
                            </div>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              <div className="col-md-4 mb-3">
                                <label className="form-label">Option Name</label>
                                <input
                                  type="text"
                                  className="form-control"
                                  value={option.name}
                                  onChange={(e) => updateProductOption(option.id, { name: e.target.value })}
                                  placeholder="e.g., Size, Color, Material"
                                />
                              </div>

                              <div className="col-md-3 mb-3">
                                <label className="form-label">Type</label>
                                <select
                                  className="form-select"
                                  value={option.type}
                                  onChange={(e) => updateProductOption(option.id, { type: e.target.value as any })}
                                >
                                  <option value="text">Text</option>
                                  <option value="select">Select</option>
                                  <option value="multiselect">Multi-select</option>
                                  <option value="number">Number</option>
                                  <option value="boolean">Boolean</option>
                                  <option value="date">Date</option>
                                  <option value="color">Color</option>
                                  <option value="file">File</option>
                                </select>
                              </div>

                              <div className="col-md-3 mb-3">
                                <div className="form-check mt-4">
                                  <input
                                    className="form-check-input"
                                    type="checkbox"
                                    id={`required_${option.id}`}
                                    checked={option.required}
                                    onChange={(e) => updateProductOption(option.id, { required: e.target.checked })}
                                  />
                                  <label className="form-check-label" htmlFor={`required_${option.id}`}>
                                    Required
                                  </label>
                                </div>
                              </div>

                              <div className="col-md-2 mb-3">
                                <label className="form-label">Default</label>
                                <input
                                  type="text"
                                  className="form-control"
                                  value={option.defaultValue || ''}
                                  onChange={(e) => updateProductOption(option.id, { defaultValue: e.target.value })}
                                  placeholder="Default value"
                                />
                              </div>
                            </div>

                            {(option.type === 'select' || option.type === 'multiselect') && (
                              <div className="mb-3">
                                <label className="form-label">Option Values</label>
                                <div className="input-group mb-2">
                                  <input
                                    type="text"
                                    className="form-control"
                                    placeholder="Add option value"
                                    onKeyPress={(e) => {
                                      if (e.key === 'Enter') {
                                        e.preventDefault();
                                        const input = e.target as HTMLInputElement;
                                        addOptionValue(option.id, input.value);
                                        input.value = '';
                                      }
                                    }}
                                  />
                                  <button
                                    type="button"
                                    className="btn btn-outline-secondary"
                                    onClick={(e) => {
                                      const input = (e.target as HTMLElement).previousElementSibling as HTMLInputElement;
                                      addOptionValue(option.id, input.value);
                                      input.value = '';
                                    }}
                                  >
                                    Add
                                  </button>
                                </div>
                                <div className="d-flex flex-wrap gap-2">
                                  {option.values.map((value, valueIndex) => (
                                    <span key={valueIndex} className="badge bg-light text-dark d-flex align-items-center">
                                      {value}
                                      <button
                                        type="button"
                                        className="btn-close ms-2"
                                        style={{ fontSize: '0.7em' }}
                                        onClick={() => removeOptionValue(option.id, value)}
                                      ></button>
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}

                            {(option.type === 'number') && (
                              <div className="row">
                                <div className="col-md-6 mb-3">
                                  <label className="form-label">Min Value</label>
                                  <input
                                    type="number"
                                    className="form-control"
                                    value={option.validation?.min || ''}
                                    onChange={(e) => updateProductOption(option.id, {
                                      validation: { ...option.validation, min: parseFloat(e.target.value) }
                                    })}
                                  />
                                </div>
                                <div className="col-md-6 mb-3">
                                  <label className="form-label">Max Value</label>
                                  <input
                                    type="number"
                                    className="form-control"
                                    value={option.validation?.max || ''}
                                    onChange={(e) => updateProductOption(option.id, {
                                      validation: { ...option.validation, max: parseFloat(e.target.value) }
                                    })}
                                  />
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {formData.options.length > 0 && (
                    <div className="mt-3">
                      <button
                        type="button"
                        className="btn btn-success"
                        onClick={generateVariants}
                      >
                        🔄 Generate Variants from Options
                      </button>
                      <small className="text-muted ms-2">
                        This will create all possible combinations of your options
                      </small>
                    </div>
                  )}
                </div>
              )}

              {/* Variants Tab */}
              {activeTab === 'variants' && (
                <div className="tab-content">
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <div>
                      <h6 className="mb-0">Product Variants</h6>
                      <small className="text-muted">Manage different variations of this product</small>
                    </div>
                    {formData.options.length > 0 && (
                      <button
                        type="button"
                        className="btn btn-outline-success"
                        onClick={generateVariants}
                      >
                        🔄 Regenerate Variants
                      </button>
                    )}
                  </div>

                  {formData.variants.length === 0 ? (
                    <div className="text-center py-4">
                      <div className="text-muted mb-2" style={{ fontSize: '48px' }}>🔄</div>
                      <p className="text-muted mb-0">No variants created</p>
                      <small className="text-muted">
                        {formData.options.length === 0
                          ? 'Add options first to create variants'
                          : 'Click "Generate Variants" to create all combinations'
                        }
                      </small>
                    </div>
                  ) : (
                    <div className="variants-list">
                      {formData.variants.map((variant, index) => (
                        <div key={variant.id} className="card mb-3">
                          <div className="card-header">
                            <div className="d-flex justify-content-between align-items-center">
                              <h6 className="mb-0">
                                Variant {index + 1}
                                <span className="badge bg-light text-dark ms-2">
                                  {Object.values(variant.options).join(' / ')}
                                </span>
                              </h6>
                              <button
                                type="button"
                                className="btn btn-sm btn-outline-danger"
                                onClick={() => {
                                  setFormData(prev => ({
                                    ...prev,
                                    variants: prev.variants.filter(v => v.id !== variant.id)
                                  }));
                                }}
                              >
                                🗑️ Remove
                              </button>
                            </div>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              <div className="col-md-3 mb-3">
                                <label className="form-label">SKU</label>
                                <input
                                  type="text"
                                  className="form-control"
                                  value={variant.sku}
                                  onChange={(e) => {
                                    setFormData(prev => ({
                                      ...prev,
                                      variants: prev.variants.map(v =>
                                        v.id === variant.id ? { ...v, sku: e.target.value } : v
                                      )
                                    }));
                                  }}
                                />
                              </div>

                              <div className="col-md-3 mb-3">
                                <label className="form-label">Price</label>
                                <div className="input-group">
                                  <span className="input-group-text">$</span>
                                  <input
                                    type="number"
                                    className="form-control"
                                    value={variant.price}
                                    onChange={(e) => {
                                      setFormData(prev => ({
                                        ...prev,
                                        variants: prev.variants.map(v =>
                                          v.id === variant.id ? { ...v, price: parseFloat(e.target.value) || 0 } : v
                                        )
                                      }));
                                    }}
                                    min="0"
                                    step="0.01"
                                  />
                                </div>
                              </div>

                              <div className="col-md-3 mb-3">
                                <label className="form-label">Stock</label>
                                <input
                                  type="number"
                                  className="form-control"
                                  value={variant.stock}
                                  onChange={(e) => {
                                    setFormData(prev => ({
                                      ...prev,
                                      variants: prev.variants.map(v =>
                                        v.id === variant.id ? { ...v, stock: parseInt(e.target.value) || 0 } : v
                                      )
                                    }));
                                  }}
                                  min="0"
                                />
                              </div>

                              <div className="col-md-3 mb-3">
                                <label className="form-label">Weight (kg)</label>
                                <input
                                  type="number"
                                  className="form-control"
                                  value={variant.weight || 0}
                                  onChange={(e) => {
                                    setFormData(prev => ({
                                      ...prev,
                                      variants: prev.variants.map(v =>
                                        v.id === variant.id ? { ...v, weight: parseFloat(e.target.value) || 0 } : v
                                      )
                                    }));
                                  }}
                                  min="0"
                                  step="0.01"
                                />
                              </div>
                            </div>

                            <div className="mb-3">
                              <label className="form-label">Variant Image URL</label>
                              <input
                                type="url"
                                className="form-control"
                                value={variant.image || ''}
                                onChange={(e) => {
                                  setFormData(prev => ({
                                    ...prev,
                                    variants: prev.variants.map(v =>
                                      v.id === variant.id ? { ...v, image: e.target.value } : v
                                    )
                                  }));
                                }}
                                placeholder="https://example.com/variant-image.jpg"
                              />
                            </div>

                            <div className="row">
                              <div className="col-md-4 mb-3">
                                <label className="form-label">Length (cm)</label>
                                <input
                                  type="number"
                                  className="form-control"
                                  value={variant.dimensions?.length || 0}
                                  onChange={(e) => {
                                    setFormData(prev => ({
                                      ...prev,
                                      variants: prev.variants.map(v =>
                                        v.id === variant.id ? {
                                          ...v,
                                          dimensions: { ...v.dimensions, length: parseFloat(e.target.value) || 0 }
                                        } : v
                                      )
                                    }));
                                  }}
                                  min="0"
                                  step="0.01"
                                />
                              </div>

                              <div className="col-md-4 mb-3">
                                <label className="form-label">Width (cm)</label>
                                <input
                                  type="number"
                                  className="form-control"
                                  value={variant.dimensions?.width || 0}
                                  onChange={(e) => {
                                    setFormData(prev => ({
                                      ...prev,
                                      variants: prev.variants.map(v =>
                                        v.id === variant.id ? {
                                          ...v,
                                          dimensions: { ...v.dimensions, width: parseFloat(e.target.value) || 0 }
                                        } : v
                                      )
                                    }));
                                  }}
                                  min="0"
                                  step="0.01"
                                />
                              </div>

                              <div className="col-md-4 mb-3">
                                <label className="form-label">Height (cm)</label>
                                <input
                                  type="number"
                                  className="form-control"
                                  value={variant.dimensions?.height || 0}
                                  onChange={(e) => {
                                    setFormData(prev => ({
                                      ...prev,
                                      variants: prev.variants.map(v =>
                                        v.id === variant.id ? {
                                          ...v,
                                          dimensions: { ...v.dimensions, height: parseFloat(e.target.value) || 0 }
                                        } : v
                                      )
                                    }));
                                  }}
                                  min="0"
                                  step="0.01"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Media Tab */}
              {activeTab === 'media' && (
                <div className="tab-content">
                  <div className="mb-3">
                    <label className="form-label">Product Images</label>
                    <div className="input-group mb-2">
                      <input
                        type="url"
                        className="form-control"
                        value={newImage}
                        onChange={(e) => setNewImage(e.target.value)}
                        placeholder="https://example.com/image.jpg"
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addImage())}
                      />
                      <button
                        type="button"
                        className="btn btn-outline-secondary"
                        onClick={addImage}
                      >
                        Add Image
                      </button>
                    </div>

                    {formData.images.length === 0 ? (
                      <div className="text-center py-4 border rounded">
                        <div className="text-muted mb-2" style={{ fontSize: '48px' }}>🖼️</div>
                        <p className="text-muted mb-0">No images added</p>
                        <small className="text-muted">Add image URLs to showcase your product</small>
                      </div>
                    ) : (
                      <div className="row">
                        {formData.images.map((image, index) => (
                          <div key={index} className="col-md-3 mb-3">
                            <div className="card">
                              <img
                                src={image}
                                className="card-img-top"
                                alt={`Product ${index + 1}`}
                                style={{ height: '150px', objectFit: 'cover' }}
                                onError={(e) => {
                                  (e.target as HTMLImageElement).src = 'https://via.placeholder.com/150x150?text=Image+Error';
                                }}
                              />
                              <div className="card-body p-2">
                                <div className="d-flex justify-content-between align-items-center">
                                  <small className="text-muted">Image {index + 1}</small>
                                  <button
                                    type="button"
                                    className="btn btn-sm btn-outline-danger"
                                    onClick={() => removeImage(image)}
                                  >
                                    🗑️
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* SEO Tab */}
              {activeTab === 'seo' && (
                <div className="tab-content">
                  <div className="mb-3">
                    <label htmlFor="seoTitle" className="form-label">SEO Title</label>
                    <input
                      type="text"
                      className="form-control"
                      id="seoTitle"
                      name="seoTitle"
                      value={formData.seoTitle || ''}
                      onChange={handleInputChange}
                      placeholder="SEO optimized title"
                      maxLength={60}
                    />
                    <small className="text-muted">
                      {(formData.seoTitle || '').length}/60 characters
                    </small>
                  </div>

                  <div className="mb-3">
                    <label htmlFor="seoDescription" className="form-label">SEO Description</label>
                    <textarea
                      className="form-control"
                      id="seoDescription"
                      name="seoDescription"
                      rows={3}
                      value={formData.seoDescription || ''}
                      onChange={handleInputChange}
                      placeholder="SEO meta description"
                      maxLength={160}
                    />
                    <small className="text-muted">
                      {(formData.seoDescription || '').length}/160 characters
                    </small>
                  </div>

                  <div className="mb-3">
                    <label htmlFor="seoKeywords" className="form-label">SEO Keywords</label>
                    <input
                      type="text"
                      className="form-control"
                      id="seoKeywords"
                      name="seoKeywords"
                      value={formData.seoKeywords || ''}
                      onChange={handleInputChange}
                      placeholder="keyword1, keyword2, keyword3"
                    />
                    <small className="text-muted">
                      Separate keywords with commas
                    </small>
                  </div>

                  <div className="alert alert-info">
                    <h6 className="alert-heading">SEO Preview</h6>
                    <div className="border rounded p-3 bg-white">
                      <div className="text-primary" style={{ fontSize: '18px' }}>
                        {formData.seoTitle || formData.name || 'Product Title'}
                      </div>
                      <div className="text-success small">
                        https://yourstore.com/products/{formData.name.toLowerCase().replace(/\s+/g, '-')}
                      </div>
                      <div className="text-muted">
                        {formData.seoDescription || formData.shortDescription || 'Product description will appear here...'}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onCancel}>
                Cancel
              </button>
              <button type="submit" className="btn btn-primary">
                {product ? 'Update Product' : 'Create Product'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AdvancedProductForm;
