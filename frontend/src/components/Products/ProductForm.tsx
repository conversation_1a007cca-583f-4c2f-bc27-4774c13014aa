import React, { useState, useEffect } from 'react';

interface Product {
  id?: string;
  name: string;
  sku: string;
  category: string;
  price: number;
  stock: number;
  minStock: number;
  description: string;
  status: 'active' | 'inactive';
}

interface ProductFormProps {
  product?: Product;
  onSave: (product: Product) => void;
  onCancel: () => void;
  isEdit?: boolean;
}

interface ProductFormErrors {
  name?: string;
  sku?: string;
  category?: string;
  price?: string;
  stock?: string;
  minStock?: string;
  description?: string;
}

const ProductForm: React.FC<ProductFormProps> = ({
  product,
  onSave,
  onCancel,
  isEdit = false
}) => {
  const [formData, setFormData] = useState<Product>({
    name: '',
    sku: '',
    category: '',
    price: 0,
    stock: 0,
    minStock: 10,
    description: '',
    status: 'active'
  });

  const [errors, setErrors] = useState<ProductFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const categories = [
    'Electronics',
    'Accessories',
    'Clothing',
    'Books',
    'Home & Garden',
    'Sports',
    'Toys',
    'Other'
  ];

  useEffect(() => {
    if (product) {
      setFormData(product);
    }
  }, [product]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));

    // Clear error when user starts typing
    if (errors[name as keyof ProductFormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: ProductFormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.sku.trim()) {
      newErrors.sku = 'SKU is required';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    if (formData.price <= 0) {
      newErrors.price = 'Price must be greater than 0';
    }

    if (formData.stock < 0) {
      newErrors.stock = 'Stock cannot be negative';
    }

    if (formData.minStock < 0) {
      newErrors.minStock = 'Minimum stock cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      onSave(formData);
    } catch (error) {
      console.error('Error saving product:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const generateSKU = () => {
    const prefix = formData.category.substring(0, 3).toUpperCase();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    setFormData(prev => ({
      ...prev,
      sku: `${prefix}-${random}`
    }));
  };

  return (
    <div className="card">
      <div className="card-header">
        <h5 className="card-title mb-0">
          {isEdit ? '✏️ Edit Product' : '➕ Add New Product'}
        </h5>
      </div>
      
      <form onSubmit={handleSubmit}>
        <div className="card-body">
          <div className="row">
            {/* Product Name */}
            <div className="col-md-6 mb-3">
              <label htmlFor="name" className="form-label">
                Product Name <span className="text-danger">*</span>
              </label>
              <input
                type="text"
                className={`form-control ${errors.name ? 'is-invalid' : ''}`}
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter product name"
              />
              {errors.name && (
                <div className="invalid-feedback">{errors.name}</div>
              )}
            </div>

            {/* SKU */}
            <div className="col-md-6 mb-3">
              <label htmlFor="sku" className="form-label">
                SKU <span className="text-danger">*</span>
              </label>
              <div className="input-group">
                <input
                  type="text"
                  className={`form-control ${errors.sku ? 'is-invalid' : ''}`}
                  id="sku"
                  name="sku"
                  value={formData.sku}
                  onChange={handleInputChange}
                  placeholder="Enter SKU"
                />
                <button
                  type="button"
                  className="btn btn-outline-secondary"
                  onClick={generateSKU}
                  title="Generate SKU"
                >
                  🎲
                </button>
                {errors.sku && (
                  <div className="invalid-feedback">{errors.sku}</div>
                )}
              </div>
            </div>

            {/* Category */}
            <div className="col-md-6 mb-3">
              <label htmlFor="category" className="form-label">
                Category <span className="text-danger">*</span>
              </label>
              <select
                className={`form-select ${errors.category ? 'is-invalid' : ''}`}
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
              >
                <option value="">Select category</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
              {errors.category && (
                <div className="invalid-feedback">{errors.category}</div>
              )}
            </div>

            {/* Price */}
            <div className="col-md-6 mb-3">
              <label htmlFor="price" className="form-label">
                Price ($) <span className="text-danger">*</span>
              </label>
              <input
                type="number"
                step="0.01"
                className={`form-control ${errors.price ? 'is-invalid' : ''}`}
                id="price"
                name="price"
                value={formData.price}
                onChange={handleInputChange}
                placeholder="0.00"
              />
              {errors.price && (
                <div className="invalid-feedback">{errors.price}</div>
              )}
            </div>

            {/* Stock */}
            <div className="col-md-6 mb-3">
              <label htmlFor="stock" className="form-label">
                Current Stock
              </label>
              <input
                type="number"
                className={`form-control ${errors.stock ? 'is-invalid' : ''}`}
                id="stock"
                name="stock"
                value={formData.stock}
                onChange={handleInputChange}
                placeholder="0"
              />
              {errors.stock && (
                <div className="invalid-feedback">{errors.stock}</div>
              )}
            </div>

            {/* Minimum Stock */}
            <div className="col-md-6 mb-3">
              <label htmlFor="minStock" className="form-label">
                Minimum Stock Level
              </label>
              <input
                type="number"
                className={`form-control ${errors.minStock ? 'is-invalid' : ''}`}
                id="minStock"
                name="minStock"
                value={formData.minStock}
                onChange={handleInputChange}
                placeholder="10"
              />
              {errors.minStock && (
                <div className="invalid-feedback">{errors.minStock}</div>
              )}
            </div>

            {/* Status */}
            <div className="col-md-12 mb-3">
              <label htmlFor="status" className="form-label">Status</label>
              <select
                className="form-select"
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            {/* Description */}
            <div className="col-md-12 mb-3">
              <label htmlFor="description" className="form-label">Description</label>
              <textarea
                className="form-control"
                id="description"
                name="description"
                rows={3}
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter product description"
              />
            </div>
          </div>
        </div>

        <div className="card-footer">
          <div className="d-flex justify-content-end gap-2">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" />
                  Saving...
                </>
              ) : (
                <>
                  {isEdit ? '💾 Update Product' : '➕ Add Product'}
                </>
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ProductForm;
