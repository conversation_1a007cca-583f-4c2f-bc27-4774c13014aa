import React, { useState } from 'react';

interface ProductOption {
  id: string;
  name: string;
  type: 'text' | 'select' | 'multiselect' | 'number' | 'boolean' | 'date' | 'color' | 'file' | 'textarea' | 'url' | 'email' | 'range';
  required: boolean;
  values: string[];
  defaultValue?: any;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
    minLength?: number;
    maxLength?: number;
  };
  placeholder?: string;
  helpText?: string;
  group?: string;
  sortOrder?: number;
  conditional?: {
    dependsOn: string;
    value: any;
  };
}

interface ProductOptionsManagerProps {
  options: ProductOption[];
  onChange: (options: ProductOption[]) => void;
  maxOptions?: number;
}

const ProductOptionsManager: React.FC<ProductOptionsManagerProps> = ({
  options,
  onChange,
  maxOptions = 50
}) => {
  const [expandedOption, setExpandedOption] = useState<string | null>(null);
  const [newOptionValue, setNewOptionValue] = useState<{ [optionId: string]: string }>({});

  const optionTypes = [
    { value: 'text', label: 'Text Input', icon: '📝' },
    { value: 'textarea', label: 'Text Area', icon: '📄' },
    { value: 'select', label: 'Dropdown Select', icon: '📋' },
    { value: 'multiselect', label: 'Multi-Select', icon: '☑️' },
    { value: 'number', label: 'Number', icon: '🔢' },
    { value: 'range', label: 'Range Slider', icon: '🎚️' },
    { value: 'boolean', label: 'Yes/No Toggle', icon: '🔘' },
    { value: 'date', label: 'Date Picker', icon: '📅' },
    { value: 'color', label: 'Color Picker', icon: '🎨' },
    { value: 'file', label: 'File Upload', icon: '📎' },
    { value: 'url', label: 'URL Input', icon: '🔗' },
    { value: 'email', label: 'Email Input', icon: '📧' }
  ];

  const addOption = () => {
    if (options.length >= maxOptions) {
      alert(`Maximum ${maxOptions} options allowed`);
      return;
    }

    const newOption: ProductOption = {
      id: `option_${Date.now()}`,
      name: '',
      type: 'text',
      required: false,
      values: [],
      sortOrder: options.length,
      placeholder: '',
      helpText: '',
      group: 'General'
    };

    onChange([...options, newOption]);
    setExpandedOption(newOption.id);
  };

  const updateOption = (optionId: string, updates: Partial<ProductOption>) => {
    onChange(options.map(option =>
      option.id === optionId ? { ...option, ...updates } : option
    ));
  };

  const removeOption = (optionId: string) => {
    if (confirm('Are you sure you want to remove this option?')) {
      onChange(options.filter(option => option.id !== optionId));
      if (expandedOption === optionId) {
        setExpandedOption(null);
      }
    }
  };

  const duplicateOption = (optionId: string) => {
    const optionToDuplicate = options.find(opt => opt.id === optionId);
    if (optionToDuplicate) {
      const duplicatedOption: ProductOption = {
        ...optionToDuplicate,
        id: `option_${Date.now()}`,
        name: `${optionToDuplicate.name} (Copy)`,
        sortOrder: options.length
      };
      onChange([...options, duplicatedOption]);
    }
  };

  const moveOption = (optionId: string, direction: 'up' | 'down') => {
    const currentIndex = options.findIndex(opt => opt.id === optionId);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= options.length) return;

    const newOptions = [...options];
    [newOptions[currentIndex], newOptions[newIndex]] = [newOptions[newIndex], newOptions[currentIndex]];
    
    // Update sort orders
    newOptions.forEach((option, index) => {
      option.sortOrder = index;
    });

    onChange(newOptions);
  };

  const addOptionValue = (optionId: string) => {
    const value = newOptionValue[optionId]?.trim();
    if (!value) return;

    const option = options.find(opt => opt.id === optionId);
    if (option && !option.values.includes(value)) {
      updateOption(optionId, {
        values: [...option.values, value]
      });
      setNewOptionValue(prev => ({ ...prev, [optionId]: '' }));
    }
  };

  const removeOptionValue = (optionId: string, valueToRemove: string) => {
    const option = options.find(opt => opt.id === optionId);
    if (option) {
      updateOption(optionId, {
        values: option.values.filter(v => v !== valueToRemove)
      });
    }
  };

  const getOptionTypeIcon = (type: string) => {
    return optionTypes.find(t => t.value === type)?.icon || '📝';
  };

  const renderValidationFields = (option: ProductOption) => {
    switch (option.type) {
      case 'text':
      case 'textarea':
      case 'url':
      case 'email':
        return (
          <div className="row">
            <div className="col-md-6 mb-3">
              <label className="form-label">Min Length</label>
              <input
                type="number"
                className="form-control"
                value={option.validation?.minLength || ''}
                onChange={(e) => updateOption(option.id, {
                  validation: { ...option.validation, minLength: parseInt(e.target.value) || undefined }
                })}
                min="0"
              />
            </div>
            <div className="col-md-6 mb-3">
              <label className="form-label">Max Length</label>
              <input
                type="number"
                className="form-control"
                value={option.validation?.maxLength || ''}
                onChange={(e) => updateOption(option.id, {
                  validation: { ...option.validation, maxLength: parseInt(e.target.value) || undefined }
                })}
                min="0"
              />
            </div>
          </div>
        );

      case 'number':
      case 'range':
        return (
          <div className="row">
            <div className="col-md-6 mb-3">
              <label className="form-label">Min Value</label>
              <input
                type="number"
                className="form-control"
                value={option.validation?.min || ''}
                onChange={(e) => updateOption(option.id, {
                  validation: { ...option.validation, min: parseFloat(e.target.value) || undefined }
                })}
              />
            </div>
            <div className="col-md-6 mb-3">
              <label className="form-label">Max Value</label>
              <input
                type="number"
                className="form-control"
                value={option.validation?.max || ''}
                onChange={(e) => updateOption(option.id, {
                  validation: { ...option.validation, max: parseFloat(e.target.value) || undefined }
                })}
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const renderOptionPreview = (option: ProductOption) => {
    switch (option.type) {
      case 'text':
      case 'url':
      case 'email':
        return (
          <input
            type={option.type}
            className="form-control"
            placeholder={option.placeholder || `Enter ${option.name.toLowerCase()}`}
            disabled
          />
        );

      case 'textarea':
        return (
          <textarea
            className="form-control"
            rows={3}
            placeholder={option.placeholder || `Enter ${option.name.toLowerCase()}`}
            disabled
          />
        );

      case 'select':
        return (
          <select className="form-select" disabled>
            <option>Select {option.name.toLowerCase()}</option>
            {option.values.map((value, index) => (
              <option key={index} value={value}>{value}</option>
            ))}
          </select>
        );

      case 'multiselect':
        return (
          <div className="border rounded p-2" style={{ minHeight: '38px' }}>
            {option.values.length === 0 ? (
              <small className="text-muted">No options available</small>
            ) : (
              option.values.slice(0, 3).map((value, index) => (
                <span key={index} className="badge bg-light text-dark me-1 mb-1">
                  {value}
                </span>
              ))
            )}
            {option.values.length > 3 && (
              <span className="badge bg-secondary">+{option.values.length - 3} more</span>
            )}
          </div>
        );

      case 'number':
        return (
          <input
            type="number"
            className="form-control"
            placeholder={option.placeholder || '0'}
            min={option.validation?.min}
            max={option.validation?.max}
            disabled
          />
        );

      case 'range':
        return (
          <div>
            <input
              type="range"
              className="form-range"
              min={option.validation?.min || 0}
              max={option.validation?.max || 100}
              disabled
            />
            <div className="d-flex justify-content-between">
              <small>{option.validation?.min || 0}</small>
              <small>{option.validation?.max || 100}</small>
            </div>
          </div>
        );

      case 'boolean':
        return (
          <div className="form-check form-switch">
            <input className="form-check-input" type="checkbox" disabled />
            <label className="form-check-label">
              {option.name}
            </label>
          </div>
        );

      case 'date':
        return (
          <input
            type="date"
            className="form-control"
            disabled
          />
        );

      case 'color':
        return (
          <div className="d-flex align-items-center">
            <input
              type="color"
              className="form-control form-control-color"
              style={{ width: '56px', height: '38px' }}
              disabled
            />
            <span className="ms-2 text-muted">Color picker</span>
          </div>
        );

      case 'file':
        return (
          <input
            type="file"
            className="form-control"
            disabled
          />
        );

      default:
        return <div className="text-muted">Preview not available</div>;
    }
  };

  return (
    <div className="product-options-manager">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h5 className="mb-1">Product Options</h5>
          <small className="text-muted">
            {options.length}/{maxOptions} options • Create custom fields for your products
          </small>
        </div>
        <button
          type="button"
          className="btn btn-primary"
          onClick={addOption}
          disabled={options.length >= maxOptions}
        >
          ➕ Add Option
        </button>
      </div>

      {options.length === 0 ? (
        <div className="text-center py-5">
          <div className="text-muted mb-3" style={{ fontSize: '64px' }}>⚙️</div>
          <h6 className="text-muted">No options created yet</h6>
          <p className="text-muted mb-3">
            Add custom options to make your products more configurable
          </p>
          <button className="btn btn-outline-primary" onClick={addOption}>
            Create Your First Option
          </button>
        </div>
      ) : (
        <div className="options-list">
          {options.map((option, index) => (
            <div key={option.id} className="card mb-3">
              <div className="card-header">
                <div className="d-flex justify-content-between align-items-center">
                  <div className="d-flex align-items-center">
                    <span className="me-2" style={{ fontSize: '20px' }}>
                      {getOptionTypeIcon(option.type)}
                    </span>
                    <div>
                      <h6 className="mb-0">
                        {option.name || `Option ${index + 1}`}
                        {option.required && <span className="text-danger ms-1">*</span>}
                      </h6>
                      <small className="text-muted">
                        {optionTypes.find(t => t.value === option.type)?.label}
                        {option.group && ` • ${option.group}`}
                      </small>
                    </div>
                  </div>

                  <div className="d-flex align-items-center gap-2">
                    <button
                      type="button"
                      className="btn btn-sm btn-outline-secondary"
                      onClick={() => moveOption(option.id, 'up')}
                      disabled={index === 0}
                      title="Move up"
                    >
                      ↑
                    </button>
                    <button
                      type="button"
                      className="btn btn-sm btn-outline-secondary"
                      onClick={() => moveOption(option.id, 'down')}
                      disabled={index === options.length - 1}
                      title="Move down"
                    >
                      ↓
                    </button>
                    <button
                      type="button"
                      className="btn btn-sm btn-outline-primary"
                      onClick={() => setExpandedOption(
                        expandedOption === option.id ? null : option.id
                      )}
                    >
                      {expandedOption === option.id ? '▼' : '▶'} Edit
                    </button>
                    <div className="dropdown">
                      <button
                        className="btn btn-sm btn-outline-secondary dropdown-toggle"
                        type="button"
                        data-bs-toggle="dropdown"
                      >
                        ⚙️
                      </button>
                      <ul className="dropdown-menu">
                        <li>
                          <button
                            className="dropdown-item"
                            onClick={() => duplicateOption(option.id)}
                          >
                            📋 Duplicate
                          </button>
                        </li>
                        <li><hr className="dropdown-divider" /></li>
                        <li>
                          <button
                            className="dropdown-item text-danger"
                            onClick={() => removeOption(option.id)}
                          >
                            🗑️ Delete
                          </button>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {expandedOption === option.id && (
                <div className="card-body">
                  <div className="row">
                    <div className="col-md-6">
                      <h6 className="mb-3">Configuration</h6>
                      
                      <div className="mb-3">
                        <label className="form-label">Option Name</label>
                        <input
                          type="text"
                          className="form-control"
                          value={option.name}
                          onChange={(e) => updateOption(option.id, { name: e.target.value })}
                          placeholder="e.g., Size, Color, Material"
                        />
                      </div>

                      <div className="mb-3">
                        <label className="form-label">Type</label>
                        <select
                          className="form-select"
                          value={option.type}
                          onChange={(e) => updateOption(option.id, { type: e.target.value as any })}
                        >
                          {optionTypes.map(type => (
                            <option key={type.value} value={type.value}>
                              {type.icon} {type.label}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="mb-3">
                        <label className="form-label">Group</label>
                        <input
                          type="text"
                          className="form-control"
                          value={option.group || ''}
                          onChange={(e) => updateOption(option.id, { group: e.target.value })}
                          placeholder="e.g., Appearance, Specifications"
                        />
                      </div>

                      <div className="mb-3">
                        <label className="form-label">Placeholder Text</label>
                        <input
                          type="text"
                          className="form-control"
                          value={option.placeholder || ''}
                          onChange={(e) => updateOption(option.id, { placeholder: e.target.value })}
                          placeholder="Placeholder text for the input"
                        />
                      </div>

                      <div className="mb-3">
                        <label className="form-label">Help Text</label>
                        <textarea
                          className="form-control"
                          rows={2}
                          value={option.helpText || ''}
                          onChange={(e) => updateOption(option.id, { helpText: e.target.value })}
                          placeholder="Additional help text for users"
                        />
                      </div>

                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id={`required_${option.id}`}
                          checked={option.required}
                          onChange={(e) => updateOption(option.id, { required: e.target.checked })}
                        />
                        <label className="form-check-label" htmlFor={`required_${option.id}`}>
                          Required field
                        </label>
                      </div>
                    </div>

                    <div className="col-md-6">
                      <h6 className="mb-3">Preview</h6>
                      <div className="border rounded p-3 bg-light">
                        <label className="form-label">
                          {option.name || 'Option Name'}
                          {option.required && <span className="text-danger">*</span>}
                        </label>
                        {renderOptionPreview(option)}
                        {option.helpText && (
                          <small className="text-muted d-block mt-1">{option.helpText}</small>
                        )}
                      </div>

                      {(option.type === 'select' || option.type === 'multiselect') && (
                        <div className="mt-3">
                          <label className="form-label">Option Values</label>
                          <div className="input-group mb-2">
                            <input
                              type="text"
                              className="form-control"
                              value={newOptionValue[option.id] || ''}
                              onChange={(e) => setNewOptionValue(prev => ({
                                ...prev,
                                [option.id]: e.target.value
                              }))}
                              placeholder="Add option value"
                              onKeyPress={(e) => {
                                if (e.key === 'Enter') {
                                  e.preventDefault();
                                  addOptionValue(option.id);
                                }
                              }}
                            />
                            <button
                              type="button"
                              className="btn btn-outline-secondary"
                              onClick={() => addOptionValue(option.id)}
                            >
                              Add
                            </button>
                          </div>
                          <div className="d-flex flex-wrap gap-2">
                            {option.values.map((value, valueIndex) => (
                              <span key={valueIndex} className="badge bg-light text-dark d-flex align-items-center">
                                {value}
                                <button
                                  type="button"
                                  className="btn-close ms-2"
                                  style={{ fontSize: '0.7em' }}
                                  onClick={() => removeOptionValue(option.id, value)}
                                ></button>
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {renderValidationFields(option)}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProductOptionsManager;
