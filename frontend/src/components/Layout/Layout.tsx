import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import SimpleHeader from './SimpleHeader';
import SimpleSidebar from './SimpleSidebar';

interface LayoutProps {
  children?: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSidebarClose = () => {
    setSidebarOpen(false);
  };

  return (
    <div id="app-layout">
      {/* Sidebar */}
      <SimpleSidebar open={sidebarOpen} onClose={handleSidebarClose} />

      {/* Main content area */}
      <div className="content-page">
        {/* Header */}
        <SimpleHeader onMenuToggle={handleSidebarToggle} />

        {/* Main content */}
        <div className="content">
          <div className="container-fluid">
            {children || <Outlet />}
          </div>
        </div>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={handleSidebarClose}
        />
      )}
    </div>
  );
};

export default Layout;
