import React, { useRef, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '../../contexts/UserContext';

interface UserProfileDropdownProps {
  isOpen: boolean;
  onClose: () => void;
}

const UserProfileDropdown: React.FC<UserProfileDropdownProps> = ({ isOpen, onClose }) => {
  const { user, logout } = useUser();
  const navigate = useNavigate();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
        setShowLogoutConfirm(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen || !user) return null;

  const handleLogout = () => {
    setShowLogoutConfirm(false);
    onClose();
    logout();
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-danger';
      case 'manager': return 'bg-warning';
      case 'staff': return 'bg-info';
      default: return 'bg-secondary';
    }
  };

  const menuItems = [
    {
      icon: '👤',
      label: 'My Profile',
      action: () => {
        onClose();
        navigate('/profile');
      }
    },
    {
      icon: '⚙️',
      label: 'Account Settings',
      action: () => {
        onClose();
        navigate('/settings');
      }
    },
    {
      icon: '🔔',
      label: 'Notification Settings',
      action: () => {
        onClose();
        // For now, just show an alert - can be implemented later
        alert('Notification settings coming soon!');
      }
    },
    {
      icon: '🌙',
      label: 'Dark Mode',
      action: () => {
        // Toggle dark mode - can be implemented later
        alert('Dark mode toggle coming soon!');
      }
    },
    {
      icon: '❓',
      label: 'Help & Support',
      action: () => {
        onClose();
        // Open help in new tab
        window.open('https://help.example.com', '_blank');
      }
    }
  ];

  return (
    <div
      ref={dropdownRef}
      className="position-absolute bg-white border rounded shadow-lg"
      style={{
        top: '100%',
        right: '0',
        width: '280px',
        zIndex: 1050,
        marginTop: '8px'
      }}
    >
      {/* User Info Header */}
      <div className="p-3 border-bottom">
        <div className="d-flex align-items-center">
          <img
            src={user.avatar || `https://via.placeholder.com/48x48/6c5ce7/ffffff?text=${user.firstName.charAt(0)}${user.lastName.charAt(0)}`}
            alt="User Avatar"
            className="rounded-circle me-3"
            width="48"
            height="48"
          />
          <div className="flex-grow-1">
            <h6 className="mb-1 fw-bold">
              {user.firstName} {user.lastName}
            </h6>
            <p className="mb-1 text-muted small">{user.email}</p>
            <span className={`badge ${getRoleBadgeColor(user.role)} text-white`} style={{ fontSize: '10px' }}>
              {user.role.toUpperCase()}
            </span>
          </div>
        </div>
      </div>

      {/* Menu Items */}
      <div className="py-2">
        {menuItems.map((item, index) => (
          <button
            key={index}
            className="btn btn-link w-100 text-start d-flex align-items-center px-3 py-2 text-decoration-none text-dark"
            onClick={item.action}
            style={{ border: 'none' }}
          >
            <span className="me-3" style={{ fontSize: '16px' }}>
              {item.icon}
            </span>
            <span style={{ fontSize: '14px' }}>{item.label}</span>
          </button>
        ))}
      </div>

      {/* Divider */}
      <hr className="my-2" />

      {/* Logout Section */}
      <div className="p-2">
        {!showLogoutConfirm ? (
          <button
            className="btn btn-outline-danger w-100 d-flex align-items-center justify-content-center"
            onClick={() => setShowLogoutConfirm(true)}
          >
            <span className="me-2">🚪</span>
            <span>Sign Out</span>
          </button>
        ) : (
          <div className="text-center">
            <p className="mb-2 small text-muted">Are you sure you want to sign out?</p>
            <div className="d-flex gap-2">
              <button
                className="btn btn-sm btn-outline-secondary flex-grow-1"
                onClick={() => setShowLogoutConfirm(false)}
              >
                Cancel
              </button>
              <button
                className="btn btn-sm btn-danger flex-grow-1"
                onClick={handleLogout}
              >
                Sign Out
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-3 py-2 border-top bg-light">
        <small className="text-muted d-flex justify-content-between">
          <span>Version 1.0.0</span>
          <span>© 2024 Hando</span>
        </small>
      </div>
    </div>
  );
};

export default UserProfileDropdown;
