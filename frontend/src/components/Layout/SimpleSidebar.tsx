import React from 'react';
import { Link, useLocation } from 'react-router-dom';

interface SidebarProps {
  open: boolean;
  onClose: () => void;
}

const SimpleSidebar: React.FC<SidebarProps> = ({ open, onClose }) => {
  const location = useLocation();

  const menuItems = [
    { text: 'Dashboard', path: '/dashboard' },
    { text: 'Products', path: '/products' },
    { text: 'Product Catalog', path: '/product-catalog' },
    { text: 'Inventory', path: '/inventory' },
    { text: 'Orders', path: '/orders' },
    { text: 'Customers', path: '/customers' },
    { text: 'Reports', path: '/reports' },
    { text: 'Plugins', path: '/plugins' },
    { text: 'Tally Integration', path: '/tally-integration' },
    { text: 'Client Management', path: '/client-management' },
    { text: 'Analytics', path: '/analytics' },
    { text: 'Billing', path: '/billing' },
    { text: 'Payment Gateways', path: '/payment-gateways' },
    { text: 'License Validation', path: '/license-validation' },
    { text: 'Client Portal', path: '/client/dashboard' },
    { text: 'Settings', path: '/settings' }
  ];

  const isItemActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className="simple-sidebar">
      {/* Logo */}
      <div className="text-center py-3 border-bottom">
        <Link to="/dashboard" className="text-decoration-none">
          <h4 className="text-primary fw-bold mb-0">Hando</h4>
        </Link>
      </div>

      {/* Navigation Menu */}
      <div className="p-3">
        <h6 className="text-muted text-uppercase fw-bold mb-3" style={{fontSize: '11px', letterSpacing: '0.5px'}}>
          MENU
        </h6>
        
        <ul className="list-unstyled mb-0">
          {menuItems.map((item) => {
            const isActive = isItemActive(item.path);
            return (
              <li key={item.text} className="mb-1">
                <Link 
                  to={item.path} 
                  className={`d-flex align-items-center px-3 py-2 text-decoration-none rounded ${
                    isActive 
                      ? 'bg-primary text-white' 
                      : 'text-dark'
                  }`}
                  style={{
                    fontSize: '14px',
                    fontWeight: '500',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.backgroundColor = '#f8f9fa';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }
                  }}
                >
                  <span>{item.text}</span>
                </Link>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};

export default SimpleSidebar;
