import React, { useRef, useEffect } from 'react';
import { useUser } from '../../contexts/UserContext';

interface NotificationDropdownProps {
  isOpen: boolean;
  onClose: () => void;
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({ isOpen, onClose }) => {
  const { notifications, unreadCount, markNotificationAsRead, markAllNotificationsAsRead } = useUser();
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'warning': return '⚠️';
      case 'error': return '❌';
      case 'success': return '✅';
      case 'info':
      default: return 'ℹ️';
    }
  };

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  return (
    <div
      ref={dropdownRef}
      className="position-absolute bg-white border rounded shadow-lg"
      style={{
        top: '100%',
        right: '0',
        width: '350px',
        maxHeight: '400px',
        zIndex: 1050,
        marginTop: '8px'
      }}
    >
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center p-3 border-bottom">
        <h6 className="mb-0 fw-bold">
          Notifications
          {unreadCount > 0 && (
            <span className="badge bg-danger ms-2">{unreadCount}</span>
          )}
        </h6>
        {unreadCount > 0 && (
          <button
            className="btn btn-sm btn-outline-primary"
            onClick={markAllNotificationsAsRead}
          >
            Mark all read
          </button>
        )}
      </div>

      {/* Notifications List */}
      <div className="overflow-auto" style={{ maxHeight: '300px' }}>
        {notifications.length === 0 ? (
          <div className="text-center py-4">
            <div className="text-muted mb-2" style={{ fontSize: '48px' }}>🔔</div>
            <p className="text-muted mb-0">No notifications</p>
          </div>
        ) : (
          notifications.map((notification) => (
            <div
              key={notification.id}
              className={`p-3 border-bottom cursor-pointer ${
                !notification.read ? 'bg-light' : ''
              }`}
              style={{ cursor: 'pointer' }}
              onClick={() => markNotificationAsRead(notification.id)}
            >
              <div className="d-flex align-items-start">
                <div className="flex-shrink-0 me-3">
                  <span style={{ fontSize: '20px' }}>
                    {getNotificationIcon(notification.type)}
                  </span>
                </div>
                <div className="flex-grow-1">
                  <div className="d-flex justify-content-between align-items-start">
                    <h6 className="mb-1 fw-medium" style={{ fontSize: '14px' }}>
                      {notification.title}
                    </h6>
                    <small className="text-muted">
                      {getTimeAgo(notification.createdAt)}
                    </small>
                  </div>
                  <p className="mb-0 text-muted" style={{ fontSize: '13px' }}>
                    {notification.message}
                  </p>
                  {!notification.read && (
                    <div className="mt-1">
                      <span className="badge bg-primary" style={{ fontSize: '10px' }}>
                        New
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Footer */}
      {notifications.length > 0 && (
        <div className="p-3 border-top text-center">
          <button className="btn btn-sm btn-outline-primary w-100">
            View All Notifications
          </button>
        </div>
      )}
    </div>
  );
};

export default NotificationDropdown;
