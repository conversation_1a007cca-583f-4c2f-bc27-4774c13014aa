import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import SimpleSidebar from './SimpleSidebar';
import SimpleHeader from './SimpleHeader';

interface LayoutProps {
  children?: React.ReactNode;
}

const SimpleLayout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSidebarClose = () => {
    setSidebarOpen(false);
  };

  return (
    <div id="simple-layout-wrapper">
      {/* Sidebar */}
      <SimpleSidebar open={sidebarOpen} onClose={handleSidebarClose} />

      {/* Header */}
      <SimpleHeader onMenuToggle={handleSidebarToggle} />

      {/* Main Content */}
      <div className="simple-main">
        <div className="container-fluid">
          {children || <Outlet />}
        </div>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="d-lg-none position-fixed w-100 h-100 bg-dark bg-opacity-50"
          style={{ zIndex: 1040, top: 0, left: 0 }}
          onClick={handleSidebarClose}
        />
      )}
    </div>
  );
};

export default SimpleLayout;
