import React, { useState } from 'react';

interface HeaderProps {
  onMenuToggle: () => void;
}

const HandoHeader: React.FC<HeaderProps> = ({ onMenuToggle }) => {
  const [notificationMenuOpen, setNotificationMenuOpen] = useState(false);
  const [profileMenuOpen, setProfileMenuOpen] = useState(false);

  return (
    <header id="page-topbar" className="bg-white border-bottom">
      <div className="navbar-header d-flex justify-content-between align-items-center px-3" style={{minHeight: '70px'}}>
        <div className="d-flex align-items-center">
          <button
            type="button"
            className="btn btn-sm me-3"
            onClick={onMenuToggle}
          >
            <span>☰</span>
          </button>

          <form className="d-none d-lg-block">
            <div className="position-relative">
              <input
                type="text"
                className="form-control"
                placeholder="Search..."
                style={{paddingLeft: '35px', width: '300px'}}
              />
              <span className="position-absolute top-50 translate-middle-y ms-2">🔍</span>
            </div>
          </form>
        </div>

        <div className="d-flex align-items-center">
          <button
            type="button"
            className="btn btn-sm me-2 position-relative"
            onClick={() => setNotificationMenuOpen(!notificationMenuOpen)}
          >
            <span>🔔</span>
            <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
              3
            </span>
          </button>

          <button
            type="button"
            className="btn btn-sm d-flex align-items-center"
            onClick={() => setProfileMenuOpen(!profileMenuOpen)}
          >
            <img
              className="rounded-circle me-2"
              src="https://via.placeholder.com/32x32/6c5ce7/ffffff?text=A"
              alt="Avatar"
              width="32"
              height="32"
            />
            <span className="d-none d-xl-inline-block">Admin</span>
          </button>
        </div>
      </div>
    </header>
  );
};

export default HandoHeader;
