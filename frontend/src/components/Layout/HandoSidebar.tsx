import React from 'react';
import { Link, useLocation } from 'react-router-dom';

interface SidebarProps {
  open: boolean;
  onClose: () => void;
}

const HandoSidebar: React.FC<SidebarProps> = ({ open, onClose }) => {
  const location = useLocation();

  const menuItems = [
    { text: 'Dashboard', path: '/dashboard' },
    { text: 'CRM', path: '/crm' },
    { text: 'Analytics', path: '/analytics' },
    { text: 'Ecommerce', path: '/dashboard' },
    { text: 'Products', path: '/products' },
    { text: 'Projects', path: '/projects' },
    { text: 'Tasks', path: '/tasks' },
    { text: 'HRM', path: '/hrm' }
  ];

  const isItemActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className="vertical-menu">
      <div className="h-100">
        {/* Logo */}
        <div className="navbar-brand-box text-center py-3">
          <Link to="/dashboard" className="logo">
            <span className="text-primary fw-bold fs-4">Hando</span>
          </Link>
        </div>

        {/* Navigation Menu */}
        <div id="sidebar-menu" className="px-2">
          <ul className="list-unstyled" id="side-menu">
            <li className="menu-title px-3 py-2 text-muted text-uppercase fs-6 fw-bold">Menu</li>

            {menuItems.map((item) => {
              const isActive = isItemActive(item.path);
              return (
                <li key={item.text} className="mb-1">
                  <Link
                    to={item.path}
                    className={`d-flex align-items-center px-3 py-2 text-decoration-none rounded ${
                      isActive
                        ? 'bg-primary text-white'
                        : 'text-dark hover-bg-light'
                    }`}
                  >
                    <span className="fs-6">{item.text}</span>
                  </Link>
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default HandoSidebar;
