import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import HandoSidebar from './HandoSidebar';
import HandoHeader from './HandoHeader';

interface LayoutProps {
  children?: React.ReactNode;
}

const HandoLayout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSidebarClose = () => {
    setSidebarOpen(false);
  };

  return (
    <div id="layout-wrapper">
      {/* Sidebar */}
      <HandoSidebar open={sidebarOpen} onClose={handleSidebarClose} />

      {/* Header */}
      <HandoHeader onMenuToggle={handleSidebarToggle} />

      {/* Main Content */}
      <div className="main-content">
        <div className="page-content">
          <div className="container-fluid">
            {children || <Outlet />}
          </div>
        </div>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="d-lg-none position-fixed w-100 h-100 bg-dark bg-opacity-50"
          style={{ zIndex: 1040, top: 0, left: 0 }}
          onClick={handleSidebarClose}
        />
      )}
    </div>
  );
};

export default HandoLayout;
