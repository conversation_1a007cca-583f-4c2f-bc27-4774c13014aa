import React, { useState } from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import { CLIENT_MODULES, ClientModule } from '../../types/client';

const ClientLayout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  // Filter modules based on user role (for demo, show all)
  const userRole = 'client_admin'; // This would come from auth context
  const availableModules = CLIENT_MODULES.filter(module => 
    module.enabled && module.requiredRole.includes(userRole as any)
  );

  const isModuleActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const getModulesByCategory = (category: string) => {
    return availableModules.filter(module => module.category === category);
  };

  return (
    <div className="d-flex">
      {/* Sidebar */}
      <div className={`client-sidebar ${sidebarOpen ? 'open' : ''}`}>
        <div className="sidebar-content">
          {/* Logo */}
          <div className="text-center py-3 border-bottom">
            <Link to="/client/dashboard" className="text-decoration-none">
              <h4 className="text-primary fw-bold mb-0">Client Portal</h4>
            </Link>
          </div>

          {/* Navigation Menu */}
          <div className="p-3">
            {/* Core Modules */}
            <div className="mb-4">
              <h6 className="text-muted text-uppercase fw-bold mb-3" style={{fontSize: '11px', letterSpacing: '0.5px'}}>
                CORE
              </h6>
              <ul className="list-unstyled mb-0">
                {getModulesByCategory('core').map((module) => {
                  const isActive = isModuleActive(module.path);
                  return (
                    <li key={module.id} className="mb-1">
                      <Link 
                        to={module.path} 
                        className={`d-flex align-items-center px-3 py-2 text-decoration-none rounded ${
                          isActive 
                            ? 'bg-primary text-white' 
                            : 'text-dark'
                        }`}
                        style={{
                          fontSize: '14px',
                          fontWeight: '500',
                          transition: 'all 0.2s ease'
                        }}
                        onMouseEnter={(e) => {
                          if (!isActive) {
                            e.currentTarget.style.backgroundColor = '#f8f9fa';
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (!isActive) {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }
                        }}
                      >
                        <span className="me-2">{module.icon}</span>
                        <span>{module.name}</span>
                      </Link>
                    </li>
                  );
                })}
              </ul>
            </div>

            {/* Inventory Modules */}
            {getModulesByCategory('inventory').length > 0 && (
              <div className="mb-4">
                <h6 className="text-muted text-uppercase fw-bold mb-3" style={{fontSize: '11px', letterSpacing: '0.5px'}}>
                  INVENTORY
                </h6>
                <ul className="list-unstyled mb-0">
                  {getModulesByCategory('inventory').map((module) => {
                    const isActive = isModuleActive(module.path);
                    return (
                      <li key={module.id} className="mb-1">
                        <Link 
                          to={module.path} 
                          className={`d-flex align-items-center px-3 py-2 text-decoration-none rounded ${
                            isActive 
                              ? 'bg-primary text-white' 
                              : 'text-dark'
                          }`}
                          style={{
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <span className="me-2">{module.icon}</span>
                          <span>{module.name}</span>
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </div>
            )}

            {/* Warehouse Modules */}
            {getModulesByCategory('warehouse').length > 0 && (
              <div className="mb-4">
                <h6 className="text-muted text-uppercase fw-bold mb-3" style={{fontSize: '11px', letterSpacing: '0.5px'}}>
                  WAREHOUSE
                </h6>
                <ul className="list-unstyled mb-0">
                  {getModulesByCategory('warehouse').map((module) => {
                    const isActive = isModuleActive(module.path);
                    return (
                      <li key={module.id} className="mb-1">
                        <Link 
                          to={module.path} 
                          className={`d-flex align-items-center px-3 py-2 text-decoration-none rounded ${
                            isActive 
                              ? 'bg-primary text-white' 
                              : 'text-dark'
                          }`}
                          style={{
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <span className="me-2">{module.icon}</span>
                          <span>{module.name}</span>
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </div>
            )}

            {/* POS Modules */}
            {getModulesByCategory('pos').length > 0 && (
              <div className="mb-4">
                <h6 className="text-muted text-uppercase fw-bold mb-3" style={{fontSize: '11px', letterSpacing: '0.5px'}}>
                  POINT OF SALE
                </h6>
                <ul className="list-unstyled mb-0">
                  {getModulesByCategory('pos').map((module) => {
                    const isActive = isModuleActive(module.path);
                    return (
                      <li key={module.id} className="mb-1">
                        <Link 
                          to={module.path} 
                          className={`d-flex align-items-center px-3 py-2 text-decoration-none rounded ${
                            isActive 
                              ? 'bg-primary text-white' 
                              : 'text-dark'
                          }`}
                          style={{
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <span className="me-2">{module.icon}</span>
                          <span>{module.name}</span>
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </div>
            )}

            {/* Sales Modules */}
            {getModulesByCategory('sales').length > 0 && (
              <div className="mb-4">
                <h6 className="text-muted text-uppercase fw-bold mb-3" style={{fontSize: '11px', letterSpacing: '0.5px'}}>
                  SALES
                </h6>
                <ul className="list-unstyled mb-0">
                  {getModulesByCategory('sales').map((module) => {
                    const isActive = isModuleActive(module.path);
                    return (
                      <li key={module.id} className="mb-1">
                        <Link 
                          to={module.path} 
                          className={`d-flex align-items-center px-3 py-2 text-decoration-none rounded ${
                            isActive 
                              ? 'bg-primary text-white' 
                              : 'text-dark'
                          }`}
                          style={{
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <span className="me-2">{module.icon}</span>
                          <span>{module.name}</span>
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </div>
            )}

            {/* Reports Modules */}
            {getModulesByCategory('reports').length > 0 && (
              <div className="mb-4">
                <h6 className="text-muted text-uppercase fw-bold mb-3" style={{fontSize: '11px', letterSpacing: '0.5px'}}>
                  REPORTS
                </h6>
                <ul className="list-unstyled mb-0">
                  {getModulesByCategory('reports').map((module) => {
                    const isActive = isModuleActive(module.path);
                    return (
                      <li key={module.id} className="mb-1">
                        <Link 
                          to={module.path} 
                          className={`d-flex align-items-center px-3 py-2 text-decoration-none rounded ${
                            isActive 
                              ? 'bg-primary text-white' 
                              : 'text-dark'
                          }`}
                          style={{
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <span className="me-2">{module.icon}</span>
                          <span>{module.name}</span>
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </div>
            )}

            {/* Settings Modules */}
            {getModulesByCategory('settings').length > 0 && (
              <div className="mb-4">
                <h6 className="text-muted text-uppercase fw-bold mb-3" style={{fontSize: '11px', letterSpacing: '0.5px'}}>
                  SETTINGS
                </h6>
                <ul className="list-unstyled mb-0">
                  {getModulesByCategory('settings').map((module) => {
                    const isActive = isModuleActive(module.path);
                    return (
                      <li key={module.id} className="mb-1">
                        <Link 
                          to={module.path} 
                          className={`d-flex align-items-center px-3 py-2 text-decoration-none rounded ${
                            isActive 
                              ? 'bg-primary text-white' 
                              : 'text-dark'
                          }`}
                          style={{
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <span className="me-2">{module.icon}</span>
                          <span>{module.name}</span>
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </div>
            )}

            {/* Back to Admin */}
            <div className="border-top pt-3 mt-4">
              <Link 
                to="/dashboard" 
                className="d-flex align-items-center px-3 py-2 text-decoration-none text-muted rounded"
                style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  transition: 'all 0.2s ease'
                }}
              >
                <span className="me-2">🔙</span>
                <span>Back to Admin</span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-grow-1">
        {/* Top Header */}
        <div className="client-header">
          <div className="d-flex justify-content-between align-items-center px-4 py-3 border-bottom">
            <div className="d-flex align-items-center">
              <button
                className="btn btn-outline-secondary me-3 d-lg-none"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                ☰
              </button>
              <div>
                <h6 className="mb-0">Client Portal</h6>
                <small className="text-muted">Acme Corporation</small>
              </div>
            </div>
            <div className="d-flex align-items-center gap-3">
              <div className="dropdown">
                <button
                  className="btn btn-outline-secondary dropdown-toggle"
                  type="button"
                  data-bs-toggle="dropdown"
                >
                  👤 John Doe
                </button>
                <ul className="dropdown-menu">
                  <li><Link className="dropdown-item" to="/client/profile">👤 Profile</Link></li>
                  <li><Link className="dropdown-item" to="/client/settings">⚙️ Settings</Link></li>
                  <li><hr className="dropdown-divider" /></li>
                  <li><Link className="dropdown-item" to="/logout">🚪 Logout</Link></li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Page Content */}
        <div className="client-content">
          <Outlet />
        </div>
      </div>

      {/* Sidebar Overlay for Mobile */}
      {sidebarOpen && (
        <div 
          className="sidebar-overlay d-lg-none"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      <style jsx>{`
        .client-sidebar {
          width: 280px;
          height: 100vh;
          background: white;
          border-right: 1px solid #e9ecef;
          position: fixed;
          left: -280px;
          top: 0;
          transition: left 0.3s ease;
          z-index: 1000;
        }

        .client-sidebar.open {
          left: 0;
        }

        @media (min-width: 992px) {
          .client-sidebar {
            position: relative;
            left: 0;
          }
        }

        .sidebar-content {
          height: 100%;
          overflow-y: auto;
        }

        .client-header {
          background: white;
          border-bottom: 1px solid #e9ecef;
        }

        .client-content {
          min-height: calc(100vh - 73px);
          background: #f8f9fa;
        }

        .sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          z-index: 999;
        }

        @media (min-width: 992px) {
          .client-content {
            margin-left: 0;
          }
        }
      `}</style>
    </div>
  );
};

export default ClientLayout;
