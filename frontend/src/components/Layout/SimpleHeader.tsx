import React, { useState } from 'react';
import { useUser } from '../../contexts/UserContext';
import NotificationDropdown from './NotificationDropdown';
import UserProfileDropdown from './UserProfileDropdown';

interface HeaderProps {
  onMenuToggle: () => void;
}

const SimpleHeader: React.FC<HeaderProps> = ({ onMenuToggle }) => {
  const { user, unreadCount } = useUser();
  const [notificationMenuOpen, setNotificationMenuOpen] = useState(false);
  const [profileMenuOpen, setProfileMenuOpen] = useState(false);

  return (
    <header className="simple-header d-flex align-items-center justify-content-between px-3">
      {/* Left side */}
      <div className="d-flex align-items-center">
        <button
          type="button"
          className="btn btn-light btn-sm me-3"
          onClick={onMenuToggle}
          style={{border: 'none', fontSize: '16px'}}
        >
          ☰
        </button>

        <form className="d-none d-lg-block">
          <div className="position-relative">
            <input 
              type="text" 
              className="form-control form-control-sm" 
              placeholder="Search..." 
              style={{
                paddingLeft: '35px', 
                width: '300px',
                border: '1px solid #e9ecef',
                borderRadius: '20px'
              }}
            />
            <span 
              className="position-absolute top-50 translate-middle-y text-muted"
              style={{left: '12px', fontSize: '14px'}}
            >
              🔍
            </span>
          </div>
        </form>
      </div>

      {/* Right side */}
      <div className="d-flex align-items-center">
        {/* Notifications */}
        <div className="position-relative me-2">
          <button
            type="button"
            className={`btn btn-light btn-sm position-relative ${notificationMenuOpen ? 'active' : ''}`}
            onClick={() => {
              setNotificationMenuOpen(!notificationMenuOpen);
              setProfileMenuOpen(false);
            }}
            style={{border: 'none', fontSize: '16px'}}
            title="Notifications"
          >
            🔔
            {unreadCount > 0 && (
              <span
                className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                style={{fontSize: '10px'}}
              >
                {unreadCount}
              </span>
            )}
          </button>
          <NotificationDropdown
            isOpen={notificationMenuOpen}
            onClose={() => setNotificationMenuOpen(false)}
          />
        </div>

        {/* User Profile */}
        <div className="position-relative">
          <button
            type="button"
            className={`btn btn-light btn-sm d-flex align-items-center ${profileMenuOpen ? 'active' : ''}`}
            onClick={() => {
              setProfileMenuOpen(!profileMenuOpen);
              setNotificationMenuOpen(false);
            }}
            style={{border: 'none'}}
            title="User Profile"
          >
            <img
              className="rounded-circle me-2"
              src={user?.avatar || `https://via.placeholder.com/32x32/6c5ce7/ffffff?text=${user?.firstName?.charAt(0) || 'U'}`}
              alt="Avatar"
              width="32"
              height="32"
            />
            <span className="d-none d-xl-inline-block" style={{fontSize: '14px'}}>
              {user?.firstName || 'User'}
            </span>
            <span className="ms-2" style={{fontSize: '12px'}}>▼</span>
          </button>
          <UserProfileDropdown
            isOpen={profileMenuOpen}
            onClose={() => setProfileMenuOpen(false)}
          />
        </div>
      </div>
    </header>
  );
};

export default SimpleHeader;
