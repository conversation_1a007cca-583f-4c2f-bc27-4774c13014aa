import React from 'react';

interface Product {
  id: string;
  name: string;
  sku: string;
  currentStock: number;
  minStock: number;
  category: string;
}

interface LowStockAlertProps {
  products?: Product[];
}

const LowStockAlert: React.FC<LowStockAlertProps> = ({ products = [] }) => {
  // Sample data if no products provided
  const sampleProducts: Product[] = [
    {
      id: 'PRD-001',
      name: 'Wireless Mouse',
      sku: 'WM-001',
      currentStock: 5,
      minStock: 10,
      category: 'Electronics'
    },
    {
      id: 'PRD-002',
      name: 'USB Cable',
      sku: 'UC-002',
      currentStock: 2,
      minStock: 15,
      category: 'Accessories'
    },
    {
      id: 'PRD-003',
      name: 'Keyboard',
      sku: 'KB-003',
      currentStock: 8,
      minStock: 12,
      category: 'Electronics'
    },
    {
      id: 'PRD-004',
      name: 'Phone Case',
      sku: 'PC-004',
      currentStock: 3,
      minStock: 20,
      category: 'Accessories'
    }
  ];

  const displayProducts = products.length > 0 ? products : sampleProducts;

  const getStockLevel = (current: number, min: number) => {
    const percentage = (current / min) * 100;
    if (percentage <= 25) return { level: 'critical', color: 'danger' };
    if (percentage <= 50) return { level: 'low', color: 'warning' };
    return { level: 'normal', color: 'success' };
  };

  return (
    <div className="card h-100">
      <div className="card-header d-flex justify-content-between align-items-center">
        <h5 className="card-title mb-0">
          <span className="text-danger me-2">⚠️</span>
          Low Stock Alert
        </h5>
        <span className="badge bg-danger">{displayProducts.length}</span>
      </div>
      <div className="card-body">
        {displayProducts.length === 0 ? (
          <div className="text-center py-4">
            <div className="text-success mb-2" style={{fontSize: '48px'}}>✅</div>
            <p className="text-muted">All products are well stocked!</p>
          </div>
        ) : (
          <div className="list-group list-group-flush">
            {displayProducts.map((product) => {
              const stockInfo = getStockLevel(product.currentStock, product.minStock);
              return (
                <div key={product.id} className="list-group-item border-0 px-0 py-3">
                  <div className="d-flex justify-content-between align-items-start">
                    <div className="flex-grow-1">
                      <h6 className="mb-1 fw-medium">{product.name}</h6>
                      <p className="mb-1 text-muted small">SKU: {product.sku}</p>
                      <span className="badge bg-light text-dark">{product.category}</span>
                    </div>
                    <div className="text-end">
                      <div className="d-flex align-items-center mb-1">
                        <span className="me-2 small text-muted">Stock:</span>
                        <span className={`badge bg-${stockInfo.color}`}>
                          {product.currentStock}
                        </span>
                      </div>
                      <small className="text-muted">Min: {product.minStock}</small>
                    </div>
                  </div>
                  
                  {/* Stock Level Progress Bar */}
                  <div className="mt-2">
                    <div className="progress" style={{height: '4px'}}>
                      <div 
                        className={`progress-bar bg-${stockInfo.color}`}
                        style={{
                          width: `${Math.min((product.currentStock / product.minStock) * 100, 100)}%`
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
        
        {displayProducts.length > 0 && (
          <div className="mt-3">
            <button className="btn btn-outline-primary btn-sm w-100">
              Restock All Items
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default LowStockAlert;
