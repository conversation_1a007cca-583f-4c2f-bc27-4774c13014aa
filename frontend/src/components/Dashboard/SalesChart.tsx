import React from 'react';

interface SalesData {
  month: string;
  sales: number;
  orders: number;
}

interface SalesChartProps {
  data?: SalesData[];
}

const SalesChart: React.FC<SalesChartProps> = ({ data = [] }) => {
  // Sample data if no data provided
  const sampleData: SalesData[] = [
    { month: 'Jan', sales: 12000, orders: 45 },
    { month: 'Feb', sales: 15000, orders: 52 },
    { month: 'Mar', sales: 18000, orders: 61 },
    { month: 'Apr', sales: 22000, orders: 73 },
    { month: 'May', sales: 25000, orders: 84 },
    { month: 'Jun', sales: 28000, orders: 92 }
  ];

  const displayData = data.length > 0 ? data : sampleData;
  const maxSales = Math.max(...displayData.map(d => d.sales));

  return (
    <div className="card h-100">
      <div className="card-header d-flex justify-content-between align-items-center">
        <h5 className="card-title mb-0">Sales Overview</h5>
        <div className="dropdown">
          <button 
            className="btn btn-sm btn-outline-secondary dropdown-toggle" 
            type="button" 
            data-bs-toggle="dropdown"
          >
            Last 6 Months
          </button>
          <ul className="dropdown-menu">
            <li><a className="dropdown-item" href="#">Last 3 Months</a></li>
            <li><a className="dropdown-item" href="#">Last 6 Months</a></li>
            <li><a className="dropdown-item" href="#">Last Year</a></li>
          </ul>
        </div>
      </div>
      <div className="card-body">
        {/* Simple Bar Chart */}
        <div className="chart-container">
          <div className="d-flex align-items-end justify-content-between" style={{height: '200px'}}>
            {displayData.map((item, index) => {
              const height = (item.sales / maxSales) * 160; // Max height 160px
              return (
                <div key={index} className="text-center flex-grow-1 mx-1">
                  <div 
                    className="bg-primary rounded-top position-relative"
                    style={{
                      height: `${height}px`,
                      minHeight: '20px',
                      transition: 'all 0.3s ease'
                    }}
                    title={`${item.month}: $${item.sales.toLocaleString()}`}
                  >
                    {/* Tooltip on hover */}
                    <div 
                      className="position-absolute top-0 start-50 translate-middle-x bg-dark text-white px-2 py-1 rounded small opacity-0"
                      style={{
                        marginTop: '-30px',
                        fontSize: '10px',
                        transition: 'opacity 0.3s ease'
                      }}
                    >
                      ${item.sales.toLocaleString()}
                    </div>
                  </div>
                  <div className="mt-2">
                    <small className="fw-medium">{item.month}</small>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Chart Legend */}
        <div className="row mt-4">
          <div className="col-6">
            <div className="d-flex align-items-center">
              <div className="bg-primary rounded me-2" style={{width: '12px', height: '12px'}}></div>
              <small className="text-muted">Sales Revenue</small>
            </div>
          </div>
          <div className="col-6">
            <div className="d-flex align-items-center">
              <div className="bg-success rounded me-2" style={{width: '12px', height: '12px'}}></div>
              <small className="text-muted">Total Orders</small>
            </div>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="row mt-3 pt-3 border-top">
          <div className="col-4 text-center">
            <div className="fw-bold text-primary">
              ${displayData.reduce((sum, item) => sum + item.sales, 0).toLocaleString()}
            </div>
            <small className="text-muted">Total Sales</small>
          </div>
          <div className="col-4 text-center">
            <div className="fw-bold text-success">
              {displayData.reduce((sum, item) => sum + item.orders, 0)}
            </div>
            <small className="text-muted">Total Orders</small>
          </div>
          <div className="col-4 text-center">
            <div className="fw-bold text-info">
              ${Math.round(displayData.reduce((sum, item) => sum + item.sales, 0) / displayData.reduce((sum, item) => sum + item.orders, 0)).toLocaleString()}
            </div>
            <small className="text-muted">Avg Order</small>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesChart;
