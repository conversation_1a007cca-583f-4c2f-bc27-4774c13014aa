import React from 'react';

interface StatsCardProps {
  title: string;
  value: string | number;
  change: string;
  changeType: 'positive' | 'negative';
  icon: string;
  color: 'primary' | 'success' | 'info' | 'warning' | 'danger';
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  change,
  changeType,
  icon,
  color
}) => {
  const colorClasses = {
    primary: 'bg-primary',
    success: 'bg-success',
    info: 'bg-info',
    warning: 'bg-warning',
    danger: 'bg-danger'
  };

  const changeClasses = {
    positive: 'bg-success-subtle text-success',
    negative: 'bg-danger-subtle text-danger'
  };

  return (
    <div className="col-xl-3 col-md-6 mb-4">
      <div className="card h-100">
        <div className="card-body">
          <div className="d-flex align-items-center">
            <div className="flex-grow-1">
              <p className="text-muted mb-1 fs-6">{title}</p>
              <h4 className="mb-0 fs-4 fw-bold">{value}</h4>
              <div className="d-flex align-items-center mt-2">
                <span className={`badge ${changeClasses[changeType]} px-2 py-1`}>
                  <span className="me-1">
                    {changeType === 'positive' ? '↗' : '↘'}
                  </span>
                  {change}
                </span>
                <span className="text-muted fs-6 ms-2">vs last month</span>
              </div>
            </div>
            <div className="flex-shrink-0">
              <div 
                className={`${colorClasses[color]} text-white rounded-circle d-flex align-items-center justify-content-center`}
                style={{width: '48px', height: '48px', fontSize: '20px'}}
              >
                {icon}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatsCard;
