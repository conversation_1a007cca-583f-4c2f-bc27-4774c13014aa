import React from 'react';

interface Order {
  id: string;
  customer: string;
  product: string;
  amount: number;
  status: 'pending' | 'completed' | 'cancelled';
  date: string;
}

interface RecentOrdersProps {
  orders?: Order[];
}

const RecentOrders: React.FC<RecentOrdersProps> = ({ orders = [] }) => {
  // Sample data if no orders provided
  const sampleOrders: Order[] = [
    {
      id: 'ORD-001',
      customer: '<PERSON>',
      product: 'Laptop Pro',
      amount: 1299.99,
      status: 'completed',
      date: '2024-01-15'
    },
    {
      id: 'ORD-002',
      customer: '<PERSON>',
      product: 'Wireless Mouse',
      amount: 49.99,
      status: 'pending',
      date: '2024-01-14'
    },
    {
      id: 'ORD-003',
      customer: '<PERSON>',
      product: 'Keyboard',
      amount: 89.99,
      status: 'completed',
      date: '2024-01-13'
    },
    {
      id: 'ORD-004',
      customer: '<PERSON> Brown',
      product: 'Monitor',
      amount: 299.99,
      status: 'cancelled',
      date: '2024-01-12'
    },
    {
      id: 'ORD-005',
      customer: '<PERSON>',
      product: 'Headphones',
      amount: 159.99,
      status: 'pending',
      date: '2024-01-11'
    }
  ];

  const displayOrders = orders.length > 0 ? orders : sampleOrders;

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      completed: 'bg-success text-white',
      pending: 'bg-warning text-dark',
      cancelled: 'bg-danger text-white'
    };

    return (
      <span className={`badge ${statusClasses[status as keyof typeof statusClasses]} px-2 py-1`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  return (
    <div className="card h-100">
      <div className="card-header d-flex justify-content-between align-items-center">
        <h5 className="card-title mb-0">Recent Orders</h5>
        <button className="btn btn-sm btn-outline-primary">View All</button>
      </div>
      <div className="card-body p-0">
        <div className="table-responsive">
          <table className="table table-hover mb-0">
            <thead className="table-light">
              <tr>
                <th className="border-0 px-3 py-2">Order ID</th>
                <th className="border-0 px-3 py-2">Customer</th>
                <th className="border-0 px-3 py-2">Product</th>
                <th className="border-0 px-3 py-2">Amount</th>
                <th className="border-0 px-3 py-2">Status</th>
                <th className="border-0 px-3 py-2">Date</th>
              </tr>
            </thead>
            <tbody>
              {displayOrders.map((order) => (
                <tr key={order.id}>
                  <td className="px-3 py-2">
                    <span className="fw-medium">{order.id}</span>
                  </td>
                  <td className="px-3 py-2">{order.customer}</td>
                  <td className="px-3 py-2">{order.product}</td>
                  <td className="px-3 py-2">
                    <span className="fw-medium">${order.amount.toFixed(2)}</span>
                  </td>
                  <td className="px-3 py-2">{getStatusBadge(order.status)}</td>
                  <td className="px-3 py-2 text-muted">
                    {new Date(order.date).toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default RecentOrders;
