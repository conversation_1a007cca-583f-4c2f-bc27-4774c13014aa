import React, { useState, useEffect } from 'react';

interface Category {
  id: string;
  name: string;
  description: string;
  parentId: string | null;
  slug: string;
  image: string;
  status: 'active' | 'inactive';
  sortOrder: number;
  metaTitle: string;
  metaDescription: string;
  children?: Category[];
  productCount: number;
  createdAt: string;
  updatedAt: string;
}

interface CategoryManagerProps {
  onCategorySelect?: (category: Category) => void;
  selectedCategories?: string[];
  multiSelect?: boolean;
}

const CategoryManager: React.FC<CategoryManagerProps> = ({
  onCategorySelect,
  selectedCategories = [],
  multiSelect = false
}) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);

  // Sample categories data
  const sampleCategories: Category[] = [
    {
      id: 'cat_1',
      name: 'Electronics',
      description: 'Electronic devices and accessories',
      parentId: null,
      slug: 'electronics',
      image: '',
      status: 'active',
      sortOrder: 1,
      metaTitle: 'Electronics - Best Deals',
      metaDescription: 'Find the best electronic devices and accessories',
      productCount: 45,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      children: [
        {
          id: 'cat_2',
          name: 'Smartphones',
          description: 'Mobile phones and accessories',
          parentId: 'cat_1',
          slug: 'smartphones',
          image: '',
          status: 'active',
          sortOrder: 1,
          metaTitle: 'Smartphones',
          metaDescription: 'Latest smartphones and mobile accessories',
          productCount: 25,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 'cat_3',
          name: 'Laptops',
          description: 'Laptops and computer accessories',
          parentId: 'cat_1',
          slug: 'laptops',
          image: '',
          status: 'active',
          sortOrder: 2,
          metaTitle: 'Laptops',
          metaDescription: 'High-performance laptops and accessories',
          productCount: 20,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      ]
    },
    {
      id: 'cat_4',
      name: 'Clothing',
      description: 'Fashion and apparel',
      parentId: null,
      slug: 'clothing',
      image: '',
      status: 'active',
      sortOrder: 2,
      metaTitle: 'Clothing & Fashion',
      metaDescription: 'Trendy clothing and fashion accessories',
      productCount: 78,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      children: [
        {
          id: 'cat_5',
          name: 'Men\'s Clothing',
          description: 'Men\'s fashion and apparel',
          parentId: 'cat_4',
          slug: 'mens-clothing',
          image: '',
          status: 'active',
          sortOrder: 1,
          metaTitle: 'Men\'s Clothing',
          metaDescription: 'Stylish men\'s clothing and accessories',
          productCount: 40,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 'cat_6',
          name: 'Women\'s Clothing',
          description: 'Women\'s fashion and apparel',
          parentId: 'cat_4',
          slug: 'womens-clothing',
          image: '',
          status: 'active',
          sortOrder: 2,
          metaTitle: 'Women\'s Clothing',
          metaDescription: 'Elegant women\'s clothing and accessories',
          productCount: 38,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      ]
    }
  ];

  useEffect(() => {
    setCategories(sampleCategories);
  }, []);

  const toggleExpanded = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  const handleCategoryClick = (category: Category) => {
    if (onCategorySelect) {
      onCategorySelect(category);
    }
  };

  const isCategorySelected = (categoryId: string) => {
    return selectedCategories.includes(categoryId);
  };

  const renderCategoryTree = (categories: Category[], level = 0) => {
    return categories.map((category) => (
      <div key={category.id} className="category-item">
        <div 
          className={`d-flex align-items-center p-2 border-bottom category-row ${
            isCategorySelected(category.id) ? 'bg-primary bg-opacity-10' : ''
          }`}
          style={{ paddingLeft: `${level * 20 + 10}px` }}
        >
          {category.children && category.children.length > 0 && (
            <button
              className="btn btn-sm btn-link p-0 me-2"
              onClick={() => toggleExpanded(category.id)}
              style={{ minWidth: '20px' }}
            >
              {expandedCategories.has(category.id) ? '▼' : '▶'}
            </button>
          )}
          
          <div 
            className="flex-grow-1 cursor-pointer"
            onClick={() => handleCategoryClick(category)}
            style={{ cursor: 'pointer' }}
          >
            <div className="d-flex align-items-center">
              <div className="me-3">
                {category.image ? (
                  <img 
                    src={category.image} 
                    alt={category.name}
                    className="rounded"
                    style={{ width: '32px', height: '32px', objectFit: 'cover' }}
                  />
                ) : (
                  <div 
                    className="bg-light rounded d-flex align-items-center justify-content-center"
                    style={{ width: '32px', height: '32px' }}
                  >
                    📁
                  </div>
                )}
              </div>
              
              <div className="flex-grow-1">
                <div className="d-flex justify-content-between align-items-center">
                  <div>
                    <h6 className="mb-0 fw-medium">{category.name}</h6>
                    <small className="text-muted">{category.description}</small>
                  </div>
                  
                  <div className="d-flex align-items-center gap-2">
                    <span className="badge bg-light text-dark">
                      {category.productCount} products
                    </span>
                    
                    <span className={`badge ${
                      category.status === 'active' ? 'bg-success' : 'bg-secondary'
                    }`}>
                      {category.status}
                    </span>
                    
                    <div className="dropdown">
                      <button
                        className="btn btn-sm btn-outline-secondary dropdown-toggle"
                        type="button"
                        data-bs-toggle="dropdown"
                      >
                        ⚙️
                      </button>
                      <ul className="dropdown-menu">
                        <li>
                          <button 
                            className="dropdown-item"
                            onClick={() => setEditingCategory(category)}
                          >
                            ✏️ Edit
                          </button>
                        </li>
                        <li>
                          <button 
                            className="dropdown-item"
                            onClick={() => handleAddSubcategory(category)}
                          >
                            ➕ Add Subcategory
                          </button>
                        </li>
                        <li><hr className="dropdown-divider" /></li>
                        <li>
                          <button 
                            className="dropdown-item text-danger"
                            onClick={() => handleDeleteCategory(category.id)}
                          >
                            🗑️ Delete
                          </button>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {category.children && 
         category.children.length > 0 && 
         expandedCategories.has(category.id) && (
          <div className="subcategories">
            {renderCategoryTree(category.children, level + 1)}
          </div>
        )}
      </div>
    ));
  };

  const handleAddSubcategory = (parentCategory: Category) => {
    // Implementation for adding subcategory
    console.log('Add subcategory to:', parentCategory.name);
  };

  const handleDeleteCategory = (categoryId: string) => {
    if (confirm('Are you sure you want to delete this category?')) {
      // Implementation for deleting category
      console.log('Delete category:', categoryId);
    }
  };

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="category-manager">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h5 className="mb-1 fw-bold">Category Management</h5>
          <p className="text-muted mb-0">Organize your products with categories</p>
        </div>
        <button 
          className="btn btn-primary"
          onClick={() => setShowAddForm(true)}
        >
          ➕ Add Category
        </button>
      </div>

      {/* Search */}
      <div className="mb-3">
        <input
          type="text"
          className="form-control"
          placeholder="Search categories..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Category Tree */}
      <div className="card">
        <div className="card-body p-0">
          {loading ? (
            <div className="text-center py-4">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : filteredCategories.length === 0 ? (
            <div className="text-center py-4">
              <div className="text-muted mb-2" style={{ fontSize: '48px' }}>📁</div>
              <p className="text-muted mb-0">No categories found</p>
            </div>
          ) : (
            <div className="category-tree">
              {renderCategoryTree(filteredCategories)}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CategoryManager;
