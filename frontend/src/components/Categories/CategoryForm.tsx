import React, { useState, useEffect } from 'react';

interface Category {
  id: string;
  name: string;
  description: string;
  parentId: string | null;
  slug: string;
  image: string;
  status: 'active' | 'inactive';
  sortOrder: number;
  metaTitle: string;
  metaDescription: string;
  seoKeywords: string;
  customFields: { [key: string]: any };
}

interface CategoryFormProps {
  category?: Category | null;
  parentCategories?: Category[];
  onSave: (category: Omit<Category, 'id'>) => void;
  onCancel: () => void;
  isOpen: boolean;
}

const CategoryForm: React.FC<CategoryFormProps> = ({
  category,
  parentCategories = [],
  onSave,
  onCancel,
  isOpen
}) => {
  const [formData, setFormData] = useState<Omit<Category, 'id'>>({
    name: '',
    description: '',
    parentId: null,
    slug: '',
    image: '',
    status: 'active',
    sortOrder: 0,
    metaTitle: '',
    metaDescription: '',
    seoKeywords: '',
    customFields: {}
  });

  const [customFieldInputs, setCustomFieldInputs] = useState<Array<{
    key: string;
    value: any;
    type: 'text' | 'number' | 'boolean' | 'date' | 'url' | 'email';
  }>>([]);

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    if (category) {
      setFormData({
        name: category.name,
        description: category.description,
        parentId: category.parentId,
        slug: category.slug,
        image: category.image,
        status: category.status,
        sortOrder: category.sortOrder,
        metaTitle: category.metaTitle,
        metaDescription: category.metaDescription,
        seoKeywords: category.seoKeywords || '',
        customFields: category.customFields || {}
      });

      // Convert custom fields to input format
      const customInputs = Object.entries(category.customFields || {}).map(([key, value]) => ({
        key,
        value,
        type: typeof value === 'number' ? 'number' : 
              typeof value === 'boolean' ? 'boolean' :
              value instanceof Date ? 'date' : 'text'
      }));
      setCustomFieldInputs(customInputs);
    } else {
      // Reset form for new category
      setFormData({
        name: '',
        description: '',
        parentId: null,
        slug: '',
        image: '',
        status: 'active',
        sortOrder: 0,
        metaTitle: '',
        metaDescription: '',
        seoKeywords: '',
        customFields: {}
      });
      setCustomFieldInputs([]);
    }
    setErrors({});
  }, [category, isOpen]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));

    // Auto-generate slug from name
    if (name === 'name') {
      const slug = value.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      
      setFormData(prev => ({
        ...prev,
        slug: slug
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const addCustomField = () => {
    setCustomFieldInputs(prev => [
      ...prev,
      { key: '', value: '', type: 'text' }
    ]);
  };

  const updateCustomField = (index: number, field: 'key' | 'value' | 'type', value: any) => {
    setCustomFieldInputs(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], [field]: value };
      return updated;
    });
  };

  const removeCustomField = (index: number) => {
    setCustomFieldInputs(prev => prev.filter((_, i) => i !== index));
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Category name is required';
    }

    if (!formData.slug.trim()) {
      newErrors.slug = 'Slug is required';
    }

    if (formData.sortOrder < 0) {
      newErrors.sortOrder = 'Sort order must be a positive number';
    }

    // Validate custom fields
    customFieldInputs.forEach((field, index) => {
      if (field.key && !field.value) {
        newErrors[`customField_${index}`] = 'Custom field value is required';
      }
      if (field.value && !field.key) {
        newErrors[`customFieldKey_${index}`] = 'Custom field key is required';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // Process custom fields
    const customFields: { [key: string]: any } = {};
    customFieldInputs.forEach(field => {
      if (field.key && field.value !== '') {
        let processedValue = field.value;
        
        // Convert value based on type
        switch (field.type) {
          case 'number':
            processedValue = parseFloat(field.value) || 0;
            break;
          case 'boolean':
            processedValue = field.value === 'true' || field.value === true;
            break;
          case 'date':
            processedValue = new Date(field.value);
            break;
          default:
            processedValue = field.value;
        }
        
        customFields[field.key] = processedValue;
      }
    });

    const categoryData = {
      ...formData,
      customFields
    };

    onSave(categoryData);
  };

  if (!isOpen) return null;

  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-lg">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">
              {category ? 'Edit Category' : 'Add New Category'}
            </h5>
            <button type="button" className="btn-close" onClick={onCancel}></button>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              {/* Tabs */}
              <ul className="nav nav-tabs mb-3">
                <li className="nav-item">
                  <button
                    type="button"
                    className={`nav-link ${activeTab === 'basic' ? 'active' : ''}`}
                    onClick={() => setActiveTab('basic')}
                  >
                    📝 Basic Info
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    type="button"
                    className={`nav-link ${activeTab === 'seo' ? 'active' : ''}`}
                    onClick={() => setActiveTab('seo')}
                  >
                    🔍 SEO
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    type="button"
                    className={`nav-link ${activeTab === 'custom' ? 'active' : ''}`}
                    onClick={() => setActiveTab('custom')}
                  >
                    ⚙️ Custom Fields
                  </button>
                </li>
              </ul>

              {/* Basic Info Tab */}
              {activeTab === 'basic' && (
                <div className="tab-content">
                  <div className="row">
                    <div className="col-md-6 mb-3">
                      <label htmlFor="name" className="form-label">
                        Category Name <span className="text-danger">*</span>
                      </label>
                      <input
                        type="text"
                        className={`form-control ${errors.name ? 'is-invalid' : ''}`}
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="Enter category name"
                      />
                      {errors.name && <div className="invalid-feedback">{errors.name}</div>}
                    </div>

                    <div className="col-md-6 mb-3">
                      <label htmlFor="slug" className="form-label">
                        Slug <span className="text-danger">*</span>
                      </label>
                      <input
                        type="text"
                        className={`form-control ${errors.slug ? 'is-invalid' : ''}`}
                        id="slug"
                        name="slug"
                        value={formData.slug}
                        onChange={handleInputChange}
                        placeholder="category-slug"
                      />
                      {errors.slug && <div className="invalid-feedback">{errors.slug}</div>}
                    </div>
                  </div>

                  <div className="mb-3">
                    <label htmlFor="description" className="form-label">Description</label>
                    <textarea
                      className="form-control"
                      id="description"
                      name="description"
                      rows={3}
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="Enter category description"
                    />
                  </div>

                  <div className="row">
                    <div className="col-md-6 mb-3">
                      <label htmlFor="parentId" className="form-label">Parent Category</label>
                      <select
                        className="form-select"
                        id="parentId"
                        name="parentId"
                        value={formData.parentId || ''}
                        onChange={handleInputChange}
                      >
                        <option value="">No Parent (Root Category)</option>
                        {parentCategories.map(cat => (
                          <option key={cat.id} value={cat.id}>{cat.name}</option>
                        ))}
                      </select>
                    </div>

                    <div className="col-md-6 mb-3">
                      <label htmlFor="status" className="form-label">Status</label>
                      <select
                        className="form-select"
                        id="status"
                        name="status"
                        value={formData.status}
                        onChange={handleInputChange}
                      >
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                      </select>
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-md-6 mb-3">
                      <label htmlFor="sortOrder" className="form-label">Sort Order</label>
                      <input
                        type="number"
                        className={`form-control ${errors.sortOrder ? 'is-invalid' : ''}`}
                        id="sortOrder"
                        name="sortOrder"
                        value={formData.sortOrder}
                        onChange={handleInputChange}
                        min="0"
                      />
                      {errors.sortOrder && <div className="invalid-feedback">{errors.sortOrder}</div>}
                    </div>

                    <div className="col-md-6 mb-3">
                      <label htmlFor="image" className="form-label">Category Image URL</label>
                      <input
                        type="url"
                        className="form-control"
                        id="image"
                        name="image"
                        value={formData.image}
                        onChange={handleInputChange}
                        placeholder="https://example.com/image.jpg"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* SEO Tab */}
              {activeTab === 'seo' && (
                <div className="tab-content">
                  <div className="mb-3">
                    <label htmlFor="metaTitle" className="form-label">Meta Title</label>
                    <input
                      type="text"
                      className="form-control"
                      id="metaTitle"
                      name="metaTitle"
                      value={formData.metaTitle}
                      onChange={handleInputChange}
                      placeholder="SEO title for this category"
                      maxLength={60}
                    />
                    <small className="text-muted">
                      {formData.metaTitle.length}/60 characters
                    </small>
                  </div>

                  <div className="mb-3">
                    <label htmlFor="metaDescription" className="form-label">Meta Description</label>
                    <textarea
                      className="form-control"
                      id="metaDescription"
                      name="metaDescription"
                      rows={3}
                      value={formData.metaDescription}
                      onChange={handleInputChange}
                      placeholder="SEO description for this category"
                      maxLength={160}
                    />
                    <small className="text-muted">
                      {formData.metaDescription.length}/160 characters
                    </small>
                  </div>

                  <div className="mb-3">
                    <label htmlFor="seoKeywords" className="form-label">SEO Keywords</label>
                    <input
                      type="text"
                      className="form-control"
                      id="seoKeywords"
                      name="seoKeywords"
                      value={formData.seoKeywords}
                      onChange={handleInputChange}
                      placeholder="keyword1, keyword2, keyword3"
                    />
                    <small className="text-muted">
                      Separate keywords with commas
                    </small>
                  </div>
                </div>
              )}

              {/* Custom Fields Tab */}
              {activeTab === 'custom' && (
                <div className="tab-content">
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <h6 className="mb-0">Custom Fields</h6>
                    <button
                      type="button"
                      className="btn btn-sm btn-outline-primary"
                      onClick={addCustomField}
                    >
                      ➕ Add Field
                    </button>
                  </div>

                  {customFieldInputs.length === 0 ? (
                    <div className="text-center py-4">
                      <div className="text-muted mb-2" style={{ fontSize: '48px' }}>⚙️</div>
                      <p className="text-muted mb-0">No custom fields added</p>
                      <small className="text-muted">Add custom fields to store additional category information</small>
                    </div>
                  ) : (
                    <div className="custom-fields">
                      {customFieldInputs.map((field, index) => (
                        <div key={index} className="card mb-3">
                          <div className="card-body">
                            <div className="row align-items-end">
                              <div className="col-md-3">
                                <label className="form-label">Field Name</label>
                                <input
                                  type="text"
                                  className={`form-control ${errors[`customFieldKey_${index}`] ? 'is-invalid' : ''}`}
                                  value={field.key}
                                  onChange={(e) => updateCustomField(index, 'key', e.target.value)}
                                  placeholder="field_name"
                                />
                                {errors[`customFieldKey_${index}`] && (
                                  <div className="invalid-feedback">{errors[`customFieldKey_${index}`]}</div>
                                )}
                              </div>

                              <div className="col-md-2">
                                <label className="form-label">Type</label>
                                <select
                                  className="form-select"
                                  value={field.type}
                                  onChange={(e) => updateCustomField(index, 'type', e.target.value)}
                                >
                                  <option value="text">Text</option>
                                  <option value="number">Number</option>
                                  <option value="boolean">Boolean</option>
                                  <option value="date">Date</option>
                                  <option value="url">URL</option>
                                  <option value="email">Email</option>
                                </select>
                              </div>

                              <div className="col-md-5">
                                <label className="form-label">Value</label>
                                {field.type === 'boolean' ? (
                                  <select
                                    className={`form-select ${errors[`customField_${index}`] ? 'is-invalid' : ''}`}
                                    value={field.value.toString()}
                                    onChange={(e) => updateCustomField(index, 'value', e.target.value === 'true')}
                                  >
                                    <option value="true">True</option>
                                    <option value="false">False</option>
                                  </select>
                                ) : (
                                  <input
                                    type={field.type}
                                    className={`form-control ${errors[`customField_${index}`] ? 'is-invalid' : ''}`}
                                    value={field.value}
                                    onChange={(e) => updateCustomField(index, 'value', e.target.value)}
                                    placeholder="Enter value"
                                  />
                                )}
                                {errors[`customField_${index}`] && (
                                  <div className="invalid-feedback">{errors[`customField_${index}`]}</div>
                                )}
                              </div>

                              <div className="col-md-2">
                                <button
                                  type="button"
                                  className="btn btn-outline-danger btn-sm w-100"
                                  onClick={() => removeCustomField(index)}
                                >
                                  🗑️ Remove
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onCancel}>
                Cancel
              </button>
              <button type="submit" className="btn btn-primary">
                {category ? 'Update Category' : 'Create Category'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CategoryForm;
