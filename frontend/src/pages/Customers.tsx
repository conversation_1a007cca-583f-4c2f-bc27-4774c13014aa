import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  status: 'active' | 'inactive' | 'blocked';
  customerGroup: string;
  addresses: Address[];
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lastOrderDate?: string;
  notes?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

interface Address {
  id: string;
  type: 'billing' | 'shipping';
  isDefault: boolean;
  firstName: string;
  lastName: string;
  company?: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone?: string;
}

interface CustomerFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other' | '';
  status: 'active' | 'inactive' | 'blocked';
  customerGroup: string;
  notes: string;
  tags: string[];
  addresses: Address[];
}

interface CustomerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (customer: Customer) => void;
  customer?: Customer | null;
}

const CustomerModal: React.FC<CustomerModalProps> = ({ isOpen, onClose, onSave, customer }) => {
  const [formData, setFormData] = useState<CustomerFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: '',
    status: 'active',
    customerGroup: 'regular',
    notes: '',
    tags: [],
    addresses: []
  });

  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    if (customer) {
      setFormData({
        firstName: customer.firstName,
        lastName: customer.lastName,
        email: customer.email,
        phone: customer.phone,
        dateOfBirth: customer.dateOfBirth || '',
        gender: customer.gender || '',
        status: customer.status,
        customerGroup: customer.customerGroup,
        notes: customer.notes || '',
        tags: customer.tags,
        addresses: customer.addresses
      });
    } else {
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        dateOfBirth: '',
        gender: '',
        status: 'active',
        customerGroup: 'regular',
        notes: '',
        tags: [],
        addresses: []
      });
    }
  }, [customer]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.firstName || !formData.lastName || !formData.email) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const customerData: Customer = {
        id: customer?.id || Date.now().toString(),
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        dateOfBirth: formData.dateOfBirth || undefined,
        gender: formData.gender || undefined,
        status: formData.status,
        customerGroup: formData.customerGroup,
        addresses: formData.addresses,
        totalOrders: customer?.totalOrders || 0,
        totalSpent: customer?.totalSpent || 0,
        averageOrderValue: customer?.averageOrderValue || 0,
        lastOrderDate: customer?.lastOrderDate,
        notes: formData.notes,
        tags: formData.tags,
        createdAt: customer?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      onSave(customerData);
      toast.success(customer ? 'Customer updated successfully' : 'Customer created successfully');
    } catch (error) {
      toast.error('Failed to save customer');
      console.error('Save customer error:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-lg">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">
              {customer ? 'Edit Customer' : 'Add New Customer'}
            </h5>
            <button type="button" className="btn-close" onClick={onClose}></button>
          </div>
          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              {/* Tabs */}
              <ul className="nav nav-tabs mb-3">
                <li className="nav-item">
                  <button
                    type="button"
                    className={`nav-link ${activeTab === 'basic' ? 'active' : ''}`}
                    onClick={() => setActiveTab('basic')}
                  >
                    Basic Info
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    type="button"
                    className={`nav-link ${activeTab === 'contact' ? 'active' : ''}`}
                    onClick={() => setActiveTab('contact')}
                  >
                    Contact & Address
                  </button>
                </li>
                <li className="nav-item">
                  <button
                    type="button"
                    className={`nav-link ${activeTab === 'additional' ? 'active' : ''}`}
                    onClick={() => setActiveTab('additional')}
                  >
                    Additional Info
                  </button>
                </li>
              </ul>

              {/* Basic Info Tab */}
              {activeTab === 'basic' && (
                <div>
                  <div className="row mb-3">
                    <div className="col-md-6">
                      <label className="form-label">First Name *</label>
                      <input
                        type="text"
                        className="form-control"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div className="col-md-6">
                      <label className="form-label">Last Name *</label>
                      <input
                        type="text"
                        className="form-control"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                  </div>

                  <div className="row mb-3">
                    <div className="col-md-6">
                      <label className="form-label">Email *</label>
                      <input
                        type="email"
                        className="form-control"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div className="col-md-6">
                      <label className="form-label">Phone</label>
                      <input
                        type="tel"
                        className="form-control"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>

                  <div className="row mb-3">
                    <div className="col-md-6">
                      <label className="form-label">Date of Birth</label>
                      <input
                        type="date"
                        className="form-control"
                        name="dateOfBirth"
                        value={formData.dateOfBirth}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div className="col-md-6">
                      <label className="form-label">Gender</label>
                      <select
                        className="form-select"
                        name="gender"
                        value={formData.gender}
                        onChange={handleInputChange}
                      >
                        <option value="">Select Gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>

                  <div className="row mb-3">
                    <div className="col-md-6">
                      <label className="form-label">Status</label>
                      <select
                        className="form-select"
                        name="status"
                        value={formData.status}
                        onChange={handleInputChange}
                      >
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="blocked">Blocked</option>
                      </select>
                    </div>
                    <div className="col-md-6">
                      <label className="form-label">Customer Group</label>
                      <select
                        className="form-select"
                        name="customerGroup"
                        value={formData.customerGroup}
                        onChange={handleInputChange}
                      >
                        <option value="regular">Regular</option>
                        <option value="vip">VIP</option>
                        <option value="wholesale">Wholesale</option>
                        <option value="premium">Premium</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Contact & Address Tab */}
              {activeTab === 'contact' && (
                <div>
                  <h6 className="mb-3">Contact Information</h6>
                  <div className="row mb-3">
                    <div className="col-md-6">
                      <label className="form-label">Primary Email</label>
                      <input
                        type="email"
                        className="form-control"
                        value={formData.email}
                        disabled
                      />
                    </div>
                    <div className="col-md-6">
                      <label className="form-label">Primary Phone</label>
                      <input
                        type="tel"
                        className="form-control"
                        value={formData.phone}
                        disabled
                      />
                    </div>
                  </div>

                  <div className="alert alert-info">
                    <small>Address management will be available in the full version. For now, addresses are managed through order creation.</small>
                  </div>
                </div>
              )}

              {/* Additional Info Tab */}
              {activeTab === 'additional' && (
                <div>
                  <div className="mb-3">
                    <label className="form-label">Notes</label>
                    <textarea
                      className="form-control"
                      name="notes"
                      rows={4}
                      value={formData.notes}
                      onChange={handleInputChange}
                      placeholder="Add any additional notes about this customer..."
                    />
                  </div>

                  <div className="alert alert-info">
                    <small>Tags and advanced customer segmentation will be available in the full version.</small>
                  </div>
                </div>
              )}
            </div>
            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onClose}>
                Cancel
              </button>
              <button type="submit" className="btn btn-primary" disabled={loading}>
                {loading ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    {customer ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  customer ? 'Update Customer' : 'Create Customer'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

const Customers: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showCustomerForm, setShowCustomerForm] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [groupFilter, setGroupFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [customersPerPage] = useState(10);
  const [isExporting, setIsExporting] = useState(false);

  // Sample customer data
  const sampleCustomers: Customer[] = [
    {
      id: '1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+****************',
      dateOfBirth: '1985-06-15',
      gender: 'male',
      status: 'active',
      customerGroup: 'vip',
      addresses: [
        {
          id: 'addr_1',
          type: 'billing',
          isDefault: true,
          firstName: 'John',
          lastName: 'Doe',
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA',
          phone: '+****************'
        }
      ],
      totalOrders: 15,
      totalSpent: 2450.75,
      averageOrderValue: 163.38,
      lastOrderDate: '2024-01-15T10:30:00Z',
      notes: 'VIP customer, prefers express shipping',
      tags: ['vip', 'frequent-buyer'],
      createdAt: '2023-03-15T08:00:00Z',
      updatedAt: '2024-01-15T10:30:00Z'
    },
    {
      id: '2',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '+****************',
      dateOfBirth: '1990-09-22',
      gender: 'female',
      status: 'active',
      customerGroup: 'premium',
      addresses: [
        {
          id: 'addr_2',
          type: 'shipping',
          isDefault: true,
          firstName: 'Jane',
          lastName: 'Smith',
          street: '456 Oak Ave',
          city: 'Los Angeles',
          state: 'CA',
          zipCode: '90210',
          country: 'USA',
          phone: '+****************'
        }
      ],
      totalOrders: 8,
      totalSpent: 1200.50,
      averageOrderValue: 150.06,
      lastOrderDate: '2024-01-20T14:15:00Z',
      notes: 'Prefers eco-friendly packaging',
      tags: ['premium', 'eco-conscious'],
      createdAt: '2023-07-10T12:00:00Z',
      updatedAt: '2024-01-20T14:15:00Z'
    },
    {
      id: '3',
      firstName: 'Mike',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'active',
      customerGroup: 'regular',
      addresses: [],
      totalOrders: 3,
      totalSpent: 450.25,
      averageOrderValue: 150.08,
      lastOrderDate: '2024-01-18T09:45:00Z',
      tags: ['new-customer'],
      createdAt: '2024-01-05T16:30:00Z',
      updatedAt: '2024-01-18T09:45:00Z'
    },
    {
      id: '4',
      firstName: 'Sarah',
      lastName: 'Wilson',
      email: '<EMAIL>',
      phone: '+****************',
      dateOfBirth: '1988-12-03',
      gender: 'female',
      status: 'inactive',
      customerGroup: 'regular',
      addresses: [],
      totalOrders: 1,
      totalSpent: 89.99,
      averageOrderValue: 89.99,
      lastOrderDate: '2023-11-15T11:20:00Z',
      notes: 'Has not ordered in a while, consider re-engagement campaign',
      tags: ['inactive'],
      createdAt: '2023-11-10T14:00:00Z',
      updatedAt: '2023-11-15T11:20:00Z'
    }
  ];

  useEffect(() => {
    setCustomers(sampleCustomers);
  }, []);

  const getStatusBadge = (status: string) => {
    const badges = {
      active: 'bg-success',
      inactive: 'bg-warning',
      blocked: 'bg-danger'
    };
    return badges[status as keyof typeof badges] || 'bg-secondary';
  };

  const getGroupBadge = (group: string) => {
    const badges = {
      regular: 'bg-light text-dark',
      premium: 'bg-info',
      vip: 'bg-warning text-dark',
      wholesale: 'bg-primary'
    };
    return badges[group as keyof typeof badges] || 'bg-secondary';
  };

  const handleCreateCustomer = () => {
    setEditingCustomer(null);
    setShowCustomerForm(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer);
    setShowCustomerForm(true);
  };

  const handleDeleteCustomer = async (customerId: string) => {
    if (confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
      try {
        setLoading(true);

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));

        setCustomers(prev => prev.filter(customer => customer.id !== customerId));
        toast.success('Customer deleted successfully');
      } catch (error) {
        toast.error('Failed to delete customer');
        console.error('Delete customer error:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSaveCustomer = (customerData: Customer) => {
    if (editingCustomer) {
      // Update existing customer
      setCustomers(prev => prev.map(customer =>
        customer.id === editingCustomer.id ? customerData : customer
      ));
    } else {
      // Add new customer
      setCustomers(prev => [customerData, ...prev]);
    }

    setShowCustomerForm(false);
    setEditingCustomer(null);
  };

  const handleExportCustomers = async () => {
    try {
      setIsExporting(true);

      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Create CSV content
      const csvHeaders = ['First Name', 'Last Name', 'Email', 'Phone', 'Status', 'Customer Group', 'Total Orders', 'Total Spent', 'Last Order Date', 'Created Date'];
      const csvRows = filteredCustomers.map(customer => [
        customer.firstName,
        customer.lastName,
        customer.email,
        customer.phone,
        customer.status,
        customer.customerGroup,
        customer.totalOrders,
        customer.totalSpent.toFixed(2),
        customer.lastOrderDate ? new Date(customer.lastOrderDate).toLocaleDateString() : 'Never',
        new Date(customer.createdAt).toLocaleDateString()
      ]);

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `customers-export-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success(`Successfully exported ${filteredCustomers.length} customers`);
    } catch (error) {
      toast.error('Failed to export customers');
      console.error('Export error:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const filteredCustomers = customers.filter(customer => {
    const matchesStatus = statusFilter === 'all' || customer.status === statusFilter;
    const matchesGroup = groupFilter === 'all' || customer.customerGroup === groupFilter;
    const matchesSearch =
      customer.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone.includes(searchTerm);
    return matchesStatus && matchesGroup && matchesSearch;
  });

  // Pagination
  const indexOfLastCustomer = currentPage * customersPerPage;
  const indexOfFirstCustomer = indexOfLastCustomer - customersPerPage;
  const currentCustomers = filteredCustomers.slice(indexOfFirstCustomer, indexOfLastCustomer);
  const totalPages = Math.ceil(filteredCustomers.length / customersPerPage);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Customer Management</h1>
          <p className="text-muted mb-0">Manage customer information and relationships</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-primary"
            onClick={handleExportCustomers}
            disabled={isExporting || filteredCustomers.length === 0}
          >
            {isExporting ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Exporting...
              </>
            ) : (
              <>📊 Export Customers</>
            )}
          </button>
          <button
            className="btn btn-primary"
            onClick={handleCreateCustomer}
          >
            ➕ Add Customer
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="card mb-4">
        <div className="card-body">
          <div className="row g-3">
            <div className="col-md-4">
              <label className="form-label">Search Customers</label>
              <input
                type="text"
                className="form-control"
                placeholder="Search by name, email, or phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="col-md-3">
              <label className="form-label">Status</label>
              <select
                className="form-select"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="blocked">Blocked</option>
              </select>
            </div>
            <div className="col-md-3">
              <label className="form-label">Customer Group</label>
              <select
                className="form-select"
                value={groupFilter}
                onChange={(e) => setGroupFilter(e.target.value)}
              >
                <option value="all">All Groups</option>
                <option value="regular">Regular</option>
                <option value="premium">Premium</option>
                <option value="vip">VIP</option>
                <option value="wholesale">Wholesale</option>
              </select>
            </div>
            <div className="col-md-2 d-flex align-items-end">
              <button
                className="btn btn-outline-secondary w-100"
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setGroupFilter('all');
                  setCurrentPage(1);
                }}
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Customer Statistics */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <h5 className="card-title text-primary">{customers.length}</h5>
              <p className="card-text">Total Customers</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <h5 className="card-title text-success">{customers.filter(c => c.status === 'active').length}</h5>
              <p className="card-text">Active Customers</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <h5 className="card-title text-info">{customers.filter(c => c.customerGroup === 'vip').length}</h5>
              <p className="card-text">VIP Customers</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <h5 className="card-title text-warning">{formatCurrency(customers.reduce((sum, c) => sum + c.totalSpent, 0))}</h5>
              <p className="card-text">Total Revenue</p>
            </div>
          </div>
        </div>
      </div>

      {/* Customers Table */}
      <div className="card">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h5 className="mb-0">
            Customers ({filteredCustomers.length})
          </h5>
          <small className="text-muted">
            Showing {indexOfFirstCustomer + 1}-{Math.min(indexOfLastCustomer, filteredCustomers.length)} of {filteredCustomers.length}
          </small>
        </div>
        <div className="card-body p-0">
          {filteredCustomers.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <i className="fas fa-users fa-3x text-muted"></i>
              </div>
              <h5 className="text-muted">No customers found</h5>
              <p className="text-muted">
                {searchTerm || statusFilter !== 'all' || groupFilter !== 'all'
                  ? 'Try adjusting your filters or search terms.'
                  : 'Start by adding your first customer.'}
              </p>
              {!searchTerm && statusFilter === 'all' && groupFilter === 'all' && (
                <button className="btn btn-primary" onClick={handleCreateCustomer}>
                  ➕ Add First Customer
                </button>
              )}
            </div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead className="table-light">
                  <tr>
                    <th>Customer</th>
                    <th>Contact</th>
                    <th>Status</th>
                    <th>Group</th>
                    <th>Orders</th>
                    <th>Total Spent</th>
                    <th>Last Order</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {currentCustomers.map((customer) => (
                    <tr key={customer.id}>
                      <td>
                        <div className="d-flex align-items-center">
                          <div className="avatar-circle me-3">
                            {customer.firstName.charAt(0)}{customer.lastName.charAt(0)}
                          </div>
                          <div>
                            <div className="fw-medium">
                              {customer.firstName} {customer.lastName}
                            </div>
                            <small className="text-muted">ID: {customer.id}</small>
                          </div>
                        </div>
                      </td>
                      <td>
                        <div>
                          <div>{customer.email}</div>
                          <small className="text-muted">{customer.phone}</small>
                        </div>
                      </td>
                      <td>
                        <span className={`badge ${getStatusBadge(customer.status)}`}>
                          {customer.status.charAt(0).toUpperCase() + customer.status.slice(1)}
                        </span>
                      </td>
                      <td>
                        <span className={`badge ${getGroupBadge(customer.customerGroup)}`}>
                          {customer.customerGroup.charAt(0).toUpperCase() + customer.customerGroup.slice(1)}
                        </span>
                      </td>
                      <td>
                        <div>
                          <div className="fw-medium">{customer.totalOrders}</div>
                          <small className="text-muted">
                            Avg: {formatCurrency(customer.averageOrderValue)}
                          </small>
                        </div>
                      </td>
                      <td>
                        <div className="fw-medium text-success">
                          {formatCurrency(customer.totalSpent)}
                        </div>
                      </td>
                      <td>
                        <div>
                          {customer.lastOrderDate ? (
                            <>
                              <div>{formatDate(customer.lastOrderDate)}</div>
                              <small className="text-muted">
                                {Math.floor((Date.now() - new Date(customer.lastOrderDate).getTime()) / (1000 * 60 * 60 * 24))} days ago
                              </small>
                            </>
                          ) : (
                            <span className="text-muted">Never</span>
                          )}
                        </div>
                      </td>
                      <td>
                        <div className="d-flex gap-1">
                          <button
                            className="btn btn-sm btn-outline-primary"
                            onClick={() => setSelectedCustomer(customer)}
                            title="View Details"
                          >
                            👁️
                          </button>
                          <button
                            className="btn btn-sm btn-outline-secondary"
                            onClick={() => handleEditCustomer(customer)}
                            title="Edit Customer"
                          >
                            ✏️
                          </button>
                          <button
                            className="btn btn-sm btn-outline-danger"
                            onClick={() => handleDeleteCustomer(customer.id)}
                            title="Delete Customer"
                            disabled={loading}
                          >
                            🗑️
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="card-footer">
            <nav>
              <ul className="pagination justify-content-center mb-0">
                <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                  <button
                    className="page-link"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </button>
                </li>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <li key={page} className={`page-item ${currentPage === page ? 'active' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </button>
                  </li>
                ))}
                <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                  <button
                    className="page-link"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </button>
                </li>
              </ul>
            </nav>
          </div>
        )}
      </div>

      {/* Customer Form Modal */}
      <CustomerModal
        isOpen={showCustomerForm}
        onClose={() => {
          setShowCustomerForm(false);
          setEditingCustomer(null);
        }}
        onSave={handleSaveCustomer}
        customer={editingCustomer}
      />

      {/* Customer Details Modal */}
      {selectedCustomer && (
        <CustomerDetailsModal
          customer={selectedCustomer}
          isOpen={!!selectedCustomer}
          onClose={() => setSelectedCustomer(null)}
        />
      )}

      {/* Custom CSS for avatar circles */}
      <style dangerouslySetInnerHTML={{
        __html: `
          .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
          }
        `
      }} />
    </div>
  );
};

interface CustomerDetailsModalProps {
  customer: Customer;
  isOpen: boolean;
  onClose: () => void;
}

const CustomerDetailsModal: React.FC<CustomerDetailsModalProps> = ({ customer, isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-lg">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">Customer Details - {customer.firstName} {customer.lastName}</h5>
            <button type="button" className="btn-close" onClick={onClose}></button>
          </div>
          <div className="modal-body">
            <div className="row">
              {/* Customer Information */}
              <div className="col-md-6">
                <h6 className="mb-3">Personal Information</h6>
                <table className="table table-sm">
                  <tbody>
                    <tr>
                      <td><strong>Name:</strong></td>
                      <td>{customer.firstName} {customer.lastName}</td>
                    </tr>
                    <tr>
                      <td><strong>Email:</strong></td>
                      <td>{customer.email}</td>
                    </tr>
                    <tr>
                      <td><strong>Phone:</strong></td>
                      <td>{customer.phone}</td>
                    </tr>
                    {customer.dateOfBirth && (
                      <tr>
                        <td><strong>Date of Birth:</strong></td>
                        <td>{new Date(customer.dateOfBirth).toLocaleDateString()}</td>
                      </tr>
                    )}
                    {customer.gender && (
                      <tr>
                        <td><strong>Gender:</strong></td>
                        <td>{customer.gender.charAt(0).toUpperCase() + customer.gender.slice(1)}</td>
                      </tr>
                    )}
                    <tr>
                      <td><strong>Status:</strong></td>
                      <td>
                        <span className={`badge ${customer.status === 'active' ? 'bg-success' : customer.status === 'inactive' ? 'bg-warning' : 'bg-danger'}`}>
                          {customer.status.charAt(0).toUpperCase() + customer.status.slice(1)}
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td><strong>Customer Group:</strong></td>
                      <td>
                        <span className={`badge ${customer.customerGroup === 'vip' ? 'bg-warning text-dark' : customer.customerGroup === 'premium' ? 'bg-info' : customer.customerGroup === 'wholesale' ? 'bg-primary' : 'bg-light text-dark'}`}>
                          {customer.customerGroup.charAt(0).toUpperCase() + customer.customerGroup.slice(1)}
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td><strong>Customer Since:</strong></td>
                      <td>{new Date(customer.createdAt).toLocaleDateString()}</td>
                    </tr>
                  </tbody>
                </table>
              </div>

              {/* Order Statistics */}
              <div className="col-md-6">
                <h6 className="mb-3">Order Statistics</h6>
                <table className="table table-sm">
                  <tbody>
                    <tr>
                      <td><strong>Total Orders:</strong></td>
                      <td>{customer.totalOrders}</td>
                    </tr>
                    <tr>
                      <td><strong>Total Spent:</strong></td>
                      <td className="text-success fw-bold">
                        {new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(customer.totalSpent)}
                      </td>
                    </tr>
                    <tr>
                      <td><strong>Average Order Value:</strong></td>
                      <td>
                        {new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(customer.averageOrderValue)}
                      </td>
                    </tr>
                    <tr>
                      <td><strong>Last Order:</strong></td>
                      <td>
                        {customer.lastOrderDate ? (
                          <>
                            {new Date(customer.lastOrderDate).toLocaleDateString()}
                            <br />
                            <small className="text-muted">
                              {Math.floor((Date.now() - new Date(customer.lastOrderDate).getTime()) / (1000 * 60 * 60 * 24))} days ago
                            </small>
                          </>
                        ) : (
                          <span className="text-muted">Never</span>
                        )}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* Addresses */}
            {customer.addresses.length > 0 && (
              <div className="row mt-4">
                <div className="col-12">
                  <h6 className="mb-3">Addresses</h6>
                  <div className="row">
                    {customer.addresses.map((address) => (
                      <div key={address.id} className="col-md-6 mb-3">
                        <div className="card">
                          <div className="card-header d-flex justify-content-between align-items-center">
                            <span className="fw-medium">
                              {address.type.charAt(0).toUpperCase() + address.type.slice(1)} Address
                            </span>
                            {address.isDefault && (
                              <span className="badge bg-primary">Default</span>
                            )}
                          </div>
                          <div className="card-body">
                            <address className="mb-0">
                              {address.firstName} {address.lastName}<br />
                              {address.company && <>{address.company}<br /></>}
                              {address.street}<br />
                              {address.city}, {address.state} {address.zipCode}<br />
                              {address.country}
                              {address.phone && <><br />Phone: {address.phone}</>}
                            </address>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Notes */}
            {customer.notes && (
              <div className="row mt-4">
                <div className="col-12">
                  <h6 className="mb-3">Notes</h6>
                  <div className="alert alert-info">
                    {customer.notes}
                  </div>
                </div>
              </div>
            )}

            {/* Tags */}
            {customer.tags.length > 0 && (
              <div className="row mt-4">
                <div className="col-12">
                  <h6 className="mb-3">Tags</h6>
                  <div>
                    {customer.tags.map((tag, index) => (
                      <span key={index} className="badge bg-secondary me-2">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Customers;
