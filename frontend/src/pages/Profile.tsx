import React, { useState } from 'react';
import { useUser } from '../contexts/UserContext';

const Profile: React.FC = () => {
  const { user, updateProfile } = useUser();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    username: user?.username || ''
  });
  const [loading, setLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      await updateProfile(formData);
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      username: user?.username || ''
    });
    setIsEditing(false);
  };

  if (!user) {
    return (
      <div className="container-fluid">
        <div className="text-center py-5">
          <h4>User not found</h4>
        </div>
      </div>
    );
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-danger';
      case 'manager': return 'bg-warning';
      case 'staff': return 'bg-info';
      default: return 'bg-secondary';
    }
  };

  return (
    <div className="container-fluid">
      {/* Page Title */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-sm-flex align-items-center justify-content-between">
            <div>
              <h4 className="mb-1 fw-bold">My Profile</h4>
              <p className="text-muted mb-0">Manage your account information and preferences</p>
            </div>
            <div className="d-flex gap-2">
              {!isEditing ? (
                <button 
                  className="btn btn-primary btn-sm"
                  onClick={() => setIsEditing(true)}
                >
                  ✏️ Edit Profile
                </button>
              ) : (
                <>
                  <button 
                    className="btn btn-outline-secondary btn-sm"
                    onClick={handleCancel}
                    disabled={loading}
                  >
                    Cancel
                  </button>
                  <button 
                    className="btn btn-primary btn-sm"
                    onClick={handleSave}
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" />
                        Saving...
                      </>
                    ) : (
                      '💾 Save Changes'
                    )}
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="row">
        {/* Profile Information */}
        <div className="col-lg-8">
          <div className="card">
            <div className="card-header">
              <h5 className="card-title mb-0">Profile Information</h5>
            </div>
            <div className="card-body">
              <div className="row">
                {/* Avatar Section */}
                <div className="col-md-4 text-center mb-4">
                  <img
                    src={user.avatar || `https://via.placeholder.com/120x120/6c5ce7/ffffff?text=${user.firstName.charAt(0)}${user.lastName.charAt(0)}`}
                    alt="Profile Avatar"
                    className="rounded-circle mb-3"
                    width="120"
                    height="120"
                  />
                  <div>
                    <h5 className="mb-1">{user.firstName} {user.lastName}</h5>
                    <span className={`badge ${getRoleBadgeColor(user.role)} text-white`}>
                      {user.role.toUpperCase()}
                    </span>
                  </div>
                  {isEditing && (
                    <button className="btn btn-outline-primary btn-sm mt-3">
                      📷 Change Photo
                    </button>
                  )}
                </div>

                {/* Form Fields */}
                <div className="col-md-8">
                  <div className="row">
                    <div className="col-md-6 mb-3">
                      <label htmlFor="firstName" className="form-label">First Name</label>
                      {isEditing ? (
                        <input
                          type="text"
                          className="form-control"
                          id="firstName"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleInputChange}
                        />
                      ) : (
                        <div className="form-control-plaintext">{user.firstName}</div>
                      )}
                    </div>

                    <div className="col-md-6 mb-3">
                      <label htmlFor="lastName" className="form-label">Last Name</label>
                      {isEditing ? (
                        <input
                          type="text"
                          className="form-control"
                          id="lastName"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleInputChange}
                        />
                      ) : (
                        <div className="form-control-plaintext">{user.lastName}</div>
                      )}
                    </div>

                    <div className="col-md-6 mb-3">
                      <label htmlFor="username" className="form-label">Username</label>
                      {isEditing ? (
                        <input
                          type="text"
                          className="form-control"
                          id="username"
                          name="username"
                          value={formData.username}
                          onChange={handleInputChange}
                        />
                      ) : (
                        <div className="form-control-plaintext">{user.username}</div>
                      )}
                    </div>

                    <div className="col-md-6 mb-3">
                      <label htmlFor="email" className="form-label">Email</label>
                      {isEditing ? (
                        <input
                          type="email"
                          className="form-control"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                        />
                      ) : (
                        <div className="form-control-plaintext">{user.email}</div>
                      )}
                    </div>

                    <div className="col-md-6 mb-3">
                      <label className="form-label">Role</label>
                      <div className="form-control-plaintext">
                        <span className={`badge ${getRoleBadgeColor(user.role)} text-white`}>
                          {user.role.toUpperCase()}
                        </span>
                      </div>
                    </div>

                    <div className="col-md-6 mb-3">
                      <label className="form-label">User ID</label>
                      <div className="form-control-plaintext text-muted">{user.id}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Account Settings */}
        <div className="col-lg-4">
          <div className="card">
            <div className="card-header">
              <h5 className="card-title mb-0">Account Settings</h5>
            </div>
            <div className="card-body">
              <div className="list-group list-group-flush">
                <button className="list-group-item list-group-item-action d-flex align-items-center">
                  <span className="me-3">🔒</span>
                  <div>
                    <div className="fw-medium">Change Password</div>
                    <small className="text-muted">Update your password</small>
                  </div>
                </button>
                
                <button className="list-group-item list-group-item-action d-flex align-items-center">
                  <span className="me-3">🔔</span>
                  <div>
                    <div className="fw-medium">Notifications</div>
                    <small className="text-muted">Manage notification preferences</small>
                  </div>
                </button>
                
                <button className="list-group-item list-group-item-action d-flex align-items-center">
                  <span className="me-3">🔐</span>
                  <div>
                    <div className="fw-medium">Two-Factor Auth</div>
                    <small className="text-muted">Enable 2FA for security</small>
                  </div>
                </button>
                
                <button className="list-group-item list-group-item-action d-flex align-items-center">
                  <span className="me-3">📱</span>
                  <div>
                    <div className="fw-medium">Connected Devices</div>
                    <small className="text-muted">Manage logged in devices</small>
                  </div>
                </button>
              </div>
            </div>
          </div>

          {/* Activity Summary */}
          <div className="card mt-4">
            <div className="card-header">
              <h5 className="card-title mb-0">Activity Summary</h5>
            </div>
            <div className="card-body">
              <div className="row text-center">
                <div className="col-6 mb-3">
                  <div className="fw-bold text-primary fs-4">156</div>
                  <small className="text-muted">Products Added</small>
                </div>
                <div className="col-6 mb-3">
                  <div className="fw-bold text-success fs-4">89</div>
                  <small className="text-muted">Orders Processed</small>
                </div>
                <div className="col-6">
                  <div className="fw-bold text-info fs-4">23</div>
                  <small className="text-muted">Reports Generated</small>
                </div>
                <div className="col-6">
                  <div className="fw-bold text-warning fs-4">7</div>
                  <small className="text-muted">Days Active</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
