import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import { saveAs } from 'file-saver';

interface Plugin {
  id: string;
  name: string;
  platform: string;
  description: string;
  version: string;
  icon: string;
  status: 'available' | 'installed' | 'configured';
  features: string[];
  requirements: string[];
  setupSteps: string[];
  folderPath: string;
}

const Plugins: React.FC = () => {
  const [activeTab, setActiveTab] = useState('available');
  const [selectedPlugin, setSelectedPlugin] = useState<Plugin | null>(null);
  const [isDownloading, setIsDownloading] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [platformFilter, setPlatformFilter] = useState('all');

  const plugins: Plugin[] = [
    {
      id: 'woocommerce-sync',
      name: 'WooCommerce Inventory Sync',
      platform: 'WooCommerce',
      description: 'Real-time inventory synchronization with WooCommerce stores. Automatically sync product stock, prices, and order data.',
      version: '2.1.4',
      icon: '🛒',
      status: 'available',
      folderPath: '/plugins/woocommerce-sync',
      features: [
        'Real-time inventory sync',
        'Automatic price updates',
        'Order import/export',
        'Product category mapping',
        'Multi-store support',
        'Webhook notifications'
      ],
      requirements: [
        'WooCommerce 5.0 or higher',
        'WordPress 5.8 or higher',
        'PHP 7.4 or higher',
        'MySQL 5.6 or higher',
        'SSL certificate required'
      ],
      setupSteps: [
        'Download and install the plugin from WordPress admin',
        'Activate the plugin in WooCommerce settings',
        'Configure API credentials in plugin settings',
        'Set up webhook endpoints for real-time sync',
        'Map product categories and attributes',
        'Test the connection and run initial sync'
      ]
    },
    {
      id: 'shopify-connector',
      name: 'Shopify Store Connector',
      platform: 'Shopify',
      description: 'Complete Shopify integration for inventory management, order processing, and customer data synchronization.',
      version: '3.0.2',
      icon: '🏪',
      status: 'available',
      folderPath: '/plugins/shopify-connector',
      features: [
        'Inventory synchronization',
        'Order management',
        'Customer data sync',
        'Product variant support',
        'Fulfillment tracking',
        'Analytics integration'
      ],
      requirements: [
        'Shopify store with admin access',
        'Private app or custom app permissions',
        'HTTPS enabled',
        'Shopify API version 2023-04 or higher'
      ],
      setupSteps: [
        'Create a private app in Shopify admin',
        'Generate API credentials with required permissions',
        'Install the connector plugin',
        'Configure API settings in the plugin',
        'Set up product mapping and sync rules',
        'Initialize first-time data synchronization'
      ]
    },
    {
      id: 'laravel-api',
      name: 'Laravel API Integration',
      platform: 'Laravel',
      description: 'RESTful API integration for Laravel applications with comprehensive inventory management endpoints.',
      version: '4.2.1',
      icon: '🔗',
      status: 'available',
      folderPath: '/plugins/laravel-api',
      features: [
        'RESTful API endpoints',
        'Authentication middleware',
        'Rate limiting',
        'Data validation',
        'Error handling',
        'API documentation'
      ],
      requirements: [
        'Laravel 9.0 or higher',
        'PHP 8.0 or higher',
        'Composer package manager',
        'MySQL or PostgreSQL',
        'Redis for caching (optional)'
      ],
      setupSteps: [
        'Install package via Composer',
        'Publish configuration files',
        'Run database migrations',
        'Configure API routes',
        'Set up authentication',
        'Test API endpoints'
      ]
    },
    {
      id: 'magento-bridge',
      name: 'Magento Commerce Bridge',
      platform: 'Magento',
      description: 'Enterprise-grade Magento integration with advanced inventory management and multi-store support.',
      version: '1.8.7',
      icon: '🏬',
      status: 'available',
      folderPath: '/plugins/magento-bridge',
      features: [
        'Multi-store inventory sync',
        'Advanced product management',
        'Order workflow automation',
        'Customer segmentation',
        'Catalog management',
        'Performance optimization'
      ],
      requirements: [
        'Magento 2.4 or higher',
        'PHP 8.1 or higher',
        'MySQL 8.0 or higher',
        'Elasticsearch 7.x',
        'Redis for caching'
      ],
      setupSteps: [
        'Install the module via Composer',
        'Run Magento setup upgrade command',
        'Configure module settings in admin panel',
        'Set up API authentication',
        'Configure store mappings',
        'Run initial data synchronization'
      ]
    },
    {
      id: 'prestashop-sync',
      name: 'PrestaShop Synchronizer',
      platform: 'PrestaShop',
      description: 'Complete PrestaShop integration with inventory sync, order management, and customer data synchronization.',
      version: '2.3.5',
      icon: '🛍️',
      status: 'available',
      folderPath: '/plugins/prestashop-sync',
      features: [
        'Product synchronization',
        'Stock level management',
        'Order processing',
        'Customer management',
        'Multi-language support',
        'Currency conversion'
      ],
      requirements: [
        'PrestaShop 1.7.8 or higher',
        'PHP 7.4 or higher',
        'MySQL 5.7 or higher',
        'Web service enabled',
        'SSL certificate'
      ],
      setupSteps: [
        'Upload module to PrestaShop modules directory',
        'Install and configure the module',
        'Enable web services in PrestaShop',
        'Generate API key',
        'Configure synchronization settings',
        'Test connection and sync data'
      ]
    },
    {
      id: 'opencart-connector',
      name: 'OpenCart Connector',
      platform: 'OpenCart',
      description: 'Seamless OpenCart integration for inventory management with real-time synchronization capabilities.',
      version: '1.9.3',
      icon: '🛒',
      status: 'available',
      folderPath: '/plugins/opencart-connector',
      features: [
        'Real-time inventory sync',
        'Product management',
        'Order synchronization',
        'Customer data sync',
        'Multi-store support',
        'Automated backups'
      ],
      requirements: [
        'OpenCart 3.0 or higher',
        'PHP 7.4 or higher',
        'MySQL 5.6 or higher',
        'cURL extension enabled',
        'JSON extension enabled'
      ],
      setupSteps: [
        'Upload extension files to OpenCart',
        'Install extension through admin panel',
        'Configure API settings',
        'Set up synchronization rules',
        'Map product categories',
        'Initialize data synchronization'
      ]
    }
  ];

  const filteredPlugins = plugins.filter(plugin => {
    const matchesSearch = plugin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         plugin.platform.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         plugin.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesPlatform = platformFilter === 'all' || plugin.platform.toLowerCase() === platformFilter.toLowerCase();
    return matchesSearch && matchesPlatform;
  });

  const platforms = ['all', ...Array.from(new Set(plugins.map(p => p.platform)))];

  const handleDownload = async (plugin: Plugin) => {
    try {
      setIsDownloading(plugin.id);
      
      // Create ZIP file from plugin folder
      const zip = new JSZip();
      
      // Fetch files from the plugin folder
      const files = await fetchPluginFiles(plugin.folderPath);
      
      // Add files to ZIP
      for (const [filePath, content] of Object.entries(files)) {
        zip.file(filePath, content);
      }
      
      // Generate ZIP file
      const zipBlob = await zip.generateAsync({ type: 'blob' });
      
      // Download the ZIP file
      const fileName = `${plugin.id}-v${plugin.version}.zip`;
      saveAs(zipBlob, fileName);
      
      toast.success(`${plugin.name} downloaded successfully!`);
    } catch (error) {
      toast.error('Failed to download plugin');
      console.error('Download error:', error);
    } finally {
      setIsDownloading(null);
    }
  };

  const fetchPluginFiles = async (folderPath: string): Promise<Record<string, string>> => {
    const files: Record<string, string> = {};
    
    try {
      // Define the files for each plugin based on what we created
      const pluginFiles: Record<string, string[]> = {
        '/plugins/woocommerce-sync': [
          'inventory-woocommerce-sync.php',
          'README.md'
        ],
        '/plugins/shopify-connector': [
          'shopify-inventory-sync.js',
          'package.json',
          'README.md'
        ],
        '/plugins/laravel-api': [
          'composer.json',
          'src/InventoryServiceProvider.php',
          'src/Services/InventoryClient.php',
          'config/inventory.php',
          'routes/api.php',
          'README.md'
        ],
        '/plugins/magento-bridge': [
          'README.md'
        ],
        '/plugins/prestashop-sync': [
          'README.md'
        ],
        '/plugins/opencart-connector': [
          'README.md'
        ]
      };

      const fileList = pluginFiles[folderPath] || [];
      
      for (const fileName of fileList) {
        try {
          const response = await fetch(`${folderPath}/${fileName}`);
          if (response.ok) {
            const content = await response.text();
            files[fileName] = content;
          } else {
            // If file doesn't exist, create a placeholder
            files[fileName] = `# ${fileName}\n\nThis file is part of the ${folderPath} plugin.\nPlease refer to the documentation for more information.`;
          }
        } catch (error) {
          console.warn(`Could not fetch ${fileName}:`, error);
          // Create a placeholder file
          files[fileName] = `# ${fileName}\n\nThis file is part of the ${folderPath} plugin.\nPlease refer to the documentation for more information.`;
        }
      }
      
      // If no files were found, create a basic structure
      if (Object.keys(files).length === 0) {
        files['README.md'] = `# ${folderPath.split('/').pop()}\n\nPlugin files will be available here.`;
      }
      
    } catch (error) {
      console.error('Error fetching plugin files:', error);
      // Create a basic README as fallback
      files['README.md'] = `# ${folderPath.split('/').pop()}\n\nPlugin files will be available here.`;
    }
    
    return files;
  };

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Plugins & Integrations</h1>
          <p className="text-muted mb-0">Download and configure plugins for various e-commerce platforms</p>
        </div>
        <div className="d-flex gap-2">
          <button className="btn btn-outline-info">
            📚 Documentation
          </button>
          <button className="btn btn-outline-success">
            🔄 Check Updates
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="card mb-4">
        <div className="card-body">
          <div className="row g-3">
            <div className="col-md-6">
              <label className="form-label">Search Plugins</label>
              <input
                type="text"
                className="form-control"
                placeholder="Search by name, platform, or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="col-md-4">
              <label className="form-label">Platform</label>
              <select
                className="form-select"
                value={platformFilter}
                onChange={(e) => setPlatformFilter(e.target.value)}
              >
                {platforms.map((platform) => (
                  <option key={platform} value={platform}>
                    {platform === 'all' ? 'All Platforms' : platform}
                  </option>
                ))}
              </select>
            </div>
            <div className="col-md-2 d-flex align-items-end">
              <button
                className="btn btn-outline-secondary w-100"
                onClick={() => {
                  setSearchTerm('');
                  setPlatformFilter('all');
                }}
              >
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <ul className="nav nav-tabs mb-4">
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'available' ? 'active' : ''}`}
            onClick={() => setActiveTab('available')}
          >
            📦 Available Plugins ({filteredPlugins.length})
          </button>
        </li>
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'installed' ? 'active' : ''}`}
            onClick={() => setActiveTab('installed')}
          >
            ✅ Installed (0)
          </button>
        </li>
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'documentation' ? 'active' : ''}`}
            onClick={() => setActiveTab('documentation')}
          >
            📖 Setup Guides
          </button>
        </li>
      </ul>

      {/* Available Plugins Tab */}
      {activeTab === 'available' && (
        <div className="row">
          {filteredPlugins.length === 0 ? (
            <div className="col-12">
              <div className="text-center py-5">
                <div className="mb-3">
                  <i className="fas fa-puzzle-piece fa-3x text-muted"></i>
                </div>
                <h5 className="text-muted">No plugins found</h5>
                <p className="text-muted">
                  {searchTerm || platformFilter !== 'all'
                    ? 'Try adjusting your search or filter criteria.'
                    : 'No plugins available at the moment.'}
                </p>
              </div>
            </div>
          ) : (
            filteredPlugins.map((plugin) => (
              <div key={plugin.id} className="col-lg-6 col-xl-4 mb-4">
                <div className="card h-100">
                  <div className="card-header d-flex justify-content-between align-items-center">
                    <div className="d-flex align-items-center">
                      <span className="me-2" style={{ fontSize: '1.5rem' }}>{plugin.icon}</span>
                      <div>
                        <h6 className="mb-0">{plugin.name}</h6>
                        <small className="text-muted">v{plugin.version}</small>
                      </div>
                    </div>
                    <span className="badge bg-primary">{plugin.platform}</span>
                  </div>
                  <div className="card-body">
                    <p className="card-text text-muted small mb-3">{plugin.description}</p>

                    <div className="mb-3">
                      <h6 className="small fw-bold mb-2">Key Features:</h6>
                      <div className="d-flex flex-wrap gap-1">
                        {plugin.features.slice(0, 3).map((feature, index) => (
                          <span key={index} className="badge bg-light text-dark">
                            {feature}
                          </span>
                        ))}
                        {plugin.features.length > 3 && (
                          <span className="badge bg-secondary">
                            +{plugin.features.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="mb-3">
                      <h6 className="small fw-bold mb-2">Requirements:</h6>
                      <ul className="list-unstyled small text-muted">
                        {plugin.requirements.slice(0, 2).map((req, index) => (
                          <li key={index}>• {req}</li>
                        ))}
                        {plugin.requirements.length > 2 && (
                          <li>• +{plugin.requirements.length - 2} more requirements</li>
                        )}
                      </ul>
                    </div>
                  </div>
                  <div className="card-footer">
                    <div className="d-flex gap-2">
                      <button
                        className="btn btn-primary flex-grow-1"
                        onClick={() => handleDownload(plugin)}
                        disabled={isDownloading === plugin.id}
                      >
                        {isDownloading === plugin.id ? (
                          <>
                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Downloading...
                          </>
                        ) : (
                          <>📥 Download</>
                        )}
                      </button>
                      <button
                        className="btn btn-outline-secondary"
                        onClick={() => setSelectedPlugin(plugin)}
                      >
                        📋 Details
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {/* Installed Plugins Tab */}
      {activeTab === 'installed' && (
        <div className="text-center py-5">
          <div className="mb-3">
            <i className="fas fa-check-circle fa-3x text-success"></i>
          </div>
          <h5 className="text-muted">No plugins installed yet</h5>
          <p className="text-muted">
            Download and install plugins from the Available tab to see them here.
          </p>
        </div>
      )}

      {/* Documentation Tab */}
      {activeTab === 'documentation' && (
        <div className="row">
          <div className="col-md-8">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">📖 General Setup Guide</h5>
              </div>
              <div className="card-body">
                <h6>Getting Started with E-commerce Integrations</h6>
                <ol>
                  <li><strong>Choose Your Platform:</strong> Select the appropriate plugin for your e-commerce platform (WooCommerce, Shopify, Magento, etc.)</li>
                  <li><strong>Download Plugin:</strong> Click the download button to get the plugin files</li>
                  <li><strong>Install Plugin:</strong> Follow platform-specific installation instructions</li>
                  <li><strong>Configure Settings:</strong> Set up API credentials and synchronization preferences</li>
                  <li><strong>Test Connection:</strong> Verify the integration is working correctly</li>
                  <li><strong>Monitor Sync:</strong> Check logs and ensure data is syncing properly</li>
                </ol>

                <h6 className="mt-4">Common Configuration Steps</h6>
                <ul>
                  <li><strong>API Credentials:</strong> Most integrations require API keys or tokens from your e-commerce platform</li>
                  <li><strong>Webhook Setup:</strong> Configure webhooks for real-time data synchronization</li>
                  <li><strong>Product Mapping:</strong> Map product categories and attributes between systems</li>
                  <li><strong>Sync Frequency:</strong> Choose how often data should be synchronized</li>
                  <li><strong>Error Handling:</strong> Set up error notifications and logging</li>
                </ul>

                <h6 className="mt-4">Troubleshooting</h6>
                <ul>
                  <li><strong>Connection Issues:</strong> Verify API credentials and network connectivity</li>
                  <li><strong>Sync Failures:</strong> Check error logs and data format compatibility</li>
                  <li><strong>Performance:</strong> Adjust sync frequency and batch sizes for optimal performance</li>
                  <li><strong>Data Conflicts:</strong> Resolve duplicate products and conflicting inventory levels</li>
                </ul>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card">
              <div className="card-header">
                <h6 className="mb-0">🔗 Quick Links</h6>
              </div>
              <div className="card-body">
                <div className="list-group list-group-flush">
                  <a href="#" className="list-group-item list-group-item-action">
                    📚 Complete Documentation
                  </a>
                  <a href="#" className="list-group-item list-group-item-action">
                    🎥 Video Tutorials
                  </a>
                  <a href="#" className="list-group-item list-group-item-action">
                    💬 Community Forum
                  </a>
                  <a href="#" className="list-group-item list-group-item-action">
                    🐛 Report Issues
                  </a>
                  <a href="#" className="list-group-item list-group-item-action">
                    💡 Feature Requests
                  </a>
                </div>
              </div>
            </div>

            <div className="card mt-3">
              <div className="card-header">
                <h6 className="mb-0">📊 Plugin Statistics</h6>
              </div>
              <div className="card-body">
                <div className="row text-center">
                  <div className="col-6">
                    <h4 className="text-primary">{plugins.length}</h4>
                    <small className="text-muted">Available</small>
                  </div>
                  <div className="col-6">
                    <h4 className="text-success">0</h4>
                    <small className="text-muted">Installed</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Plugin Details Modal */}
      {selectedPlugin && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">
                  {selectedPlugin.icon} {selectedPlugin.name} - Setup Guide
                </h5>
                <button type="button" className="btn-close" onClick={() => setSelectedPlugin(null)}></button>
              </div>
              <div className="modal-body">
                <div className="row">
                  <div className="col-md-8">
                    <h6>📋 Installation Steps</h6>
                    <ol>
                      {selectedPlugin.setupSteps.map((step, index) => (
                        <li key={index} className="mb-2">{step}</li>
                      ))}
                    </ol>

                    <h6 className="mt-4">✨ Features</h6>
                    <div className="row">
                      {selectedPlugin.features.map((feature, index) => (
                        <div key={index} className="col-md-6 mb-2">
                          <div className="d-flex align-items-center">
                            <i className="fas fa-check text-success me-2"></i>
                            <span>{feature}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="col-md-4">
                    <div className="card">
                      <div className="card-header">
                        <h6 className="mb-0">📋 Plugin Info</h6>
                      </div>
                      <div className="card-body">
                        <table className="table table-sm">
                          <tbody>
                            <tr>
                              <td><strong>Platform:</strong></td>
                              <td>{selectedPlugin.platform}</td>
                            </tr>
                            <tr>
                              <td><strong>Version:</strong></td>
                              <td>{selectedPlugin.version}</td>
                            </tr>
                            <tr>
                              <td><strong>Status:</strong></td>
                              <td>
                                <span className="badge bg-primary">
                                  {selectedPlugin.status.charAt(0).toUpperCase() + selectedPlugin.status.slice(1)}
                                </span>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <div className="card mt-3">
                      <div className="card-header">
                        <h6 className="mb-0">⚠️ Requirements</h6>
                      </div>
                      <div className="card-body">
                        <ul className="list-unstyled small">
                          {selectedPlugin.requirements.map((req, index) => (
                            <li key={index} className="mb-1">
                              <i className="fas fa-circle text-warning me-2" style={{ fontSize: '0.5rem' }}></i>
                              {req}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button type="button" className="btn btn-secondary" onClick={() => setSelectedPlugin(null)}>
                  Close
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={() => {
                    handleDownload(selectedPlugin);
                    setSelectedPlugin(null);
                  }}
                  disabled={isDownloading === selectedPlugin.id}
                >
                  {isDownloading === selectedPlugin.id ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Downloading...
                    </>
                  ) : (
                    <>📥 Download Plugin</>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Plugins;
