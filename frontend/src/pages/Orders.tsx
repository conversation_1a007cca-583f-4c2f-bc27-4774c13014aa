import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  sku: string;
  quantity: number;
  price: number;
  total: number;
  image?: string;
}

interface Order {
  id: string;
  orderNumber: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  billingAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  notes?: string;
  trackingNumber?: string;
  createdAt: string;
  updatedAt: string;
}

interface CreateOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (order: Order) => void;
}

const CreateOrderModal: React.FC<CreateOrderModalProps> = ({ isOpen, onClose, onSave }) => {
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    paymentMethod: 'Credit Card',
    shippingStreet: '',
    shippingCity: '',
    shippingState: '',
    shippingZipCode: '',
    shippingCountry: 'USA',
    items: [{ productName: '', sku: '', quantity: 1, price: 0 }],
    notes: ''
  });

  const [loading, setLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleItemChange = (index: number, field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { productName: '', sku: '', quantity: 1, price: 0 }]
    }));
  };

  const removeItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  const calculateTotal = () => {
    const subtotal = formData.items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
    const tax = subtotal * 0.08; // 8% tax
    const shipping = subtotal > 100 ? 0 : 15; // Free shipping over $100
    return { subtotal, tax, shipping, total: subtotal + tax + shipping };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.customerName || !formData.customerEmail || formData.items.some(item => !item.productName)) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const { subtotal, tax, shipping, total } = calculateTotal();

      const newOrder: Order = {
        id: Date.now().toString(),
        orderNumber: `ORD-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
        customerId: `cust_${Date.now()}`,
        customerName: formData.customerName,
        customerEmail: formData.customerEmail,
        status: 'pending',
        paymentStatus: 'pending',
        paymentMethod: formData.paymentMethod,
        shippingAddress: {
          street: formData.shippingStreet,
          city: formData.shippingCity,
          state: formData.shippingState,
          zipCode: formData.shippingZipCode,
          country: formData.shippingCountry
        },
        billingAddress: {
          street: formData.shippingStreet,
          city: formData.shippingCity,
          state: formData.shippingState,
          zipCode: formData.shippingZipCode,
          country: formData.shippingCountry
        },
        items: formData.items.map((item, index) => ({
          id: `item_${Date.now()}_${index}`,
          productId: `prod_${Date.now()}_${index}`,
          productName: item.productName,
          sku: item.sku,
          quantity: item.quantity,
          price: item.price,
          total: item.quantity * item.price
        })),
        subtotal,
        tax,
        shipping,
        discount: 0,
        total,
        notes: formData.notes,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      onSave(newOrder);
    } catch (error) {
      toast.error('Failed to create order');
      console.error('Create order error:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const { subtotal, tax, shipping, total } = calculateTotal();

  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-lg">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">Create New Order</h5>
            <button type="button" className="btn-close" onClick={onClose}></button>
          </div>
          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              {/* Customer Information */}
              <div className="row mb-3">
                <div className="col-md-6">
                  <label className="form-label">Customer Name *</label>
                  <input
                    type="text"
                    className="form-control"
                    name="customerName"
                    value={formData.customerName}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="col-md-6">
                  <label className="form-label">Customer Email *</label>
                  <input
                    type="email"
                    className="form-control"
                    name="customerEmail"
                    value={formData.customerEmail}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>

              {/* Payment Method */}
              <div className="row mb-3">
                <div className="col-md-6">
                  <label className="form-label">Payment Method</label>
                  <select
                    className="form-select"
                    name="paymentMethod"
                    value={formData.paymentMethod}
                    onChange={handleInputChange}
                  >
                    <option value="Credit Card">Credit Card</option>
                    <option value="PayPal">PayPal</option>
                    <option value="Bank Transfer">Bank Transfer</option>
                    <option value="Cash">Cash</option>
                  </select>
                </div>
              </div>

              {/* Shipping Address */}
              <h6 className="mb-3">Shipping Address</h6>
              <div className="row mb-3">
                <div className="col-md-12 mb-2">
                  <input
                    type="text"
                    className="form-control"
                    name="shippingStreet"
                    placeholder="Street Address"
                    value={formData.shippingStreet}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="col-md-4">
                  <input
                    type="text"
                    className="form-control"
                    name="shippingCity"
                    placeholder="City"
                    value={formData.shippingCity}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="col-md-4">
                  <input
                    type="text"
                    className="form-control"
                    name="shippingState"
                    placeholder="State"
                    value={formData.shippingState}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="col-md-4">
                  <input
                    type="text"
                    className="form-control"
                    name="shippingZipCode"
                    placeholder="ZIP Code"
                    value={formData.shippingZipCode}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              {/* Order Items */}
              <h6 className="mb-3">Order Items</h6>
              {formData.items.map((item, index) => (
                <div key={index} className="row mb-2 align-items-end">
                  <div className="col-md-4">
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Product Name *"
                      value={item.productName}
                      onChange={(e) => handleItemChange(index, 'productName', e.target.value)}
                      required
                    />
                  </div>
                  <div className="col-md-2">
                    <input
                      type="text"
                      className="form-control"
                      placeholder="SKU"
                      value={item.sku}
                      onChange={(e) => handleItemChange(index, 'sku', e.target.value)}
                    />
                  </div>
                  <div className="col-md-2">
                    <input
                      type="number"
                      className="form-control"
                      placeholder="Qty"
                      min="1"
                      value={item.quantity}
                      onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value) || 1)}
                    />
                  </div>
                  <div className="col-md-2">
                    <input
                      type="number"
                      className="form-control"
                      placeholder="Price"
                      min="0"
                      step="0.01"
                      value={item.price}
                      onChange={(e) => handleItemChange(index, 'price', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div className="col-md-2">
                    <div className="d-flex gap-1">
                      <button
                        type="button"
                        className="btn btn-sm btn-success"
                        onClick={addItem}
                        title="Add item"
                      >
                        +
                      </button>
                      {formData.items.length > 1 && (
                        <button
                          type="button"
                          className="btn btn-sm btn-danger"
                          onClick={() => removeItem(index)}
                          title="Remove item"
                        >
                          -
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* Order Summary */}
              <div className="row mt-4">
                <div className="col-md-6">
                  <label className="form-label">Order Notes</label>
                  <textarea
                    className="form-control"
                    name="notes"
                    rows={3}
                    value={formData.notes}
                    onChange={handleInputChange}
                    placeholder="Special instructions or notes..."
                  />
                </div>
                <div className="col-md-6">
                  <div className="card">
                    <div className="card-body">
                      <h6 className="card-title">Order Summary</h6>
                      <div className="d-flex justify-content-between">
                        <span>Subtotal:</span>
                        <span>${subtotal.toFixed(2)}</span>
                      </div>
                      <div className="d-flex justify-content-between">
                        <span>Tax (8%):</span>
                        <span>${tax.toFixed(2)}</span>
                      </div>
                      <div className="d-flex justify-content-between">
                        <span>Shipping:</span>
                        <span>${shipping.toFixed(2)}</span>
                      </div>
                      <hr />
                      <div className="d-flex justify-content-between fw-bold">
                        <span>Total:</span>
                        <span>${total.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onClose}>
                Cancel
              </button>
              <button type="submit" className="btn btn-primary" disabled={loading}>
                {loading ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    Creating...
                  </>
                ) : (
                  'Create Order'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

interface OrderDetailsModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
}

const OrderDetailsModal: React.FC<OrderDetailsModalProps> = ({ order, isOpen, onClose }) => {
  if (!isOpen || !order) return null;

  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-lg">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">Order Details - {order.orderNumber}</h5>
            <button type="button" className="btn-close" onClick={onClose}></button>
          </div>
          <div className="modal-body">
            <div className="row">
              {/* Order Information */}
              <div className="col-md-6">
                <h6 className="mb-3">Order Information</h6>
                <table className="table table-sm">
                  <tbody>
                    <tr>
                      <td><strong>Order Number:</strong></td>
                      <td>{order.orderNumber}</td>
                    </tr>
                    <tr>
                      <td><strong>Status:</strong></td>
                      <td>
                        <span className={`badge ${order.status === 'pending' ? 'bg-warning' :
                          order.status === 'processing' ? 'bg-info' :
                          order.status === 'shipped' ? 'bg-primary' :
                          order.status === 'delivered' ? 'bg-success' : 'bg-danger'}`}>
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td><strong>Payment Status:</strong></td>
                      <td>
                        <span className={`badge ${order.paymentStatus === 'paid' ? 'bg-success' :
                          order.paymentStatus === 'pending' ? 'bg-warning' : 'bg-danger'}`}>
                          {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
                        </span>
                      </td>
                    </tr>
                    <tr>
                      <td><strong>Payment Method:</strong></td>
                      <td>{order.paymentMethod}</td>
                    </tr>
                    <tr>
                      <td><strong>Created:</strong></td>
                      <td>{new Date(order.createdAt).toLocaleDateString()}</td>
                    </tr>
                    {order.trackingNumber && (
                      <tr>
                        <td><strong>Tracking:</strong></td>
                        <td>{order.trackingNumber}</td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Customer Information */}
              <div className="col-md-6">
                <h6 className="mb-3">Customer Information</h6>
                <table className="table table-sm">
                  <tbody>
                    <tr>
                      <td><strong>Name:</strong></td>
                      <td>{order.customerName}</td>
                    </tr>
                    <tr>
                      <td><strong>Email:</strong></td>
                      <td>{order.customerEmail}</td>
                    </tr>
                  </tbody>
                </table>

                <h6 className="mb-3 mt-4">Shipping Address</h6>
                <address>
                  {order.shippingAddress.street}<br />
                  {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}<br />
                  {order.shippingAddress.country}
                </address>
              </div>
            </div>

            {/* Order Items */}
            <div className="row mt-4">
              <div className="col-12">
                <h6 className="mb-3">Order Items</h6>
                <div className="table-responsive">
                  <table className="table table-sm">
                    <thead>
                      <tr>
                        <th>Product</th>
                        <th>SKU</th>
                        <th>Quantity</th>
                        <th>Price</th>
                        <th>Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {order.items.map((item) => (
                        <tr key={item.id}>
                          <td>
                            <div className="d-flex align-items-center">
                              {item.image && (
                                <img
                                  src={item.image}
                                  alt={item.productName}
                                  className="me-2"
                                  style={{ width: '40px', height: '40px', objectFit: 'cover' }}
                                />
                              )}
                              <span>{item.productName}</span>
                            </div>
                          </td>
                          <td>{item.sku}</td>
                          <td>{item.quantity}</td>
                          <td>${item.price.toFixed(2)}</td>
                          <td>${item.total.toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="row mt-4">
              <div className="col-md-6 offset-md-6">
                <div className="card">
                  <div className="card-body">
                    <h6 className="card-title">Order Summary</h6>
                    <div className="d-flex justify-content-between">
                      <span>Subtotal:</span>
                      <span>${order.subtotal.toFixed(2)}</span>
                    </div>
                    <div className="d-flex justify-content-between">
                      <span>Tax:</span>
                      <span>${order.tax.toFixed(2)}</span>
                    </div>
                    <div className="d-flex justify-content-between">
                      <span>Shipping:</span>
                      <span>${order.shipping.toFixed(2)}</span>
                    </div>
                    {order.discount > 0 && (
                      <div className="d-flex justify-content-between text-success">
                        <span>Discount:</span>
                        <span>-${order.discount.toFixed(2)}</span>
                      </div>
                    )}
                    <hr />
                    <div className="d-flex justify-content-between fw-bold">
                      <span>Total:</span>
                      <span>${order.total.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Notes */}
            {order.notes && (
              <div className="row mt-4">
                <div className="col-12">
                  <h6 className="mb-3">Order Notes</h6>
                  <div className="alert alert-info">
                    {order.notes}
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const Orders: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [ordersPerPage] = useState(10);
  const [showCreateOrderForm, setShowCreateOrderForm] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  // Sample orders data
  const sampleOrders: Order[] = [
    {
      id: '1',
      orderNumber: 'ORD-2024-001',
      customerId: 'cust_1',
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      status: 'processing',
      paymentStatus: 'paid',
      paymentMethod: 'Credit Card',
      shippingAddress: {
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        country: 'USA'
      },
      billingAddress: {
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        country: 'USA'
      },
      items: [
        {
          id: 'item_1',
          productId: '1',
          productName: 'Premium Wireless Headphones',
          sku: 'PWH-001',
          quantity: 1,
          price: 299.99,
          total: 299.99,
          image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=100'
        },
        {
          id: 'item_2',
          productId: '2',
          productName: 'Smart Fitness Watch',
          sku: 'SFW-002',
          quantity: 2,
          price: 199.99,
          total: 399.98,
          image: 'https://images.unsplash.com/photo-*************-37898b6baf30?w=100'
        }
      ],
      subtotal: 699.97,
      tax: 56.00,
      shipping: 15.00,
      discount: 0,
      total: 770.97,
      notes: 'Please deliver after 5 PM',
      trackingNumber: 'TRK123456789',
      createdAt: '2024-01-20T10:30:00Z',
      updatedAt: '2024-01-20T14:45:00Z'
    },
    {
      id: '2',
      orderNumber: 'ORD-2024-002',
      customerId: 'cust_2',
      customerName: 'Jane Smith',
      customerEmail: '<EMAIL>',
      status: 'shipped',
      paymentStatus: 'paid',
      paymentMethod: 'PayPal',
      shippingAddress: {
        street: '456 Oak Ave',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90210',
        country: 'USA'
      },
      billingAddress: {
        street: '456 Oak Ave',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90210',
        country: 'USA'
      },
      items: [
        {
          id: 'item_3',
          productId: '1',
          productName: 'Premium Wireless Headphones',
          sku: 'PWH-001',
          quantity: 1,
          price: 299.99,
          total: 299.99,
          image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=100'
        }
      ],
      subtotal: 299.99,
      tax: 24.00,
      shipping: 10.00,
      discount: 30.00,
      total: 303.99,
      trackingNumber: 'TRK987654321',
      createdAt: '2024-01-19T15:20:00Z',
      updatedAt: '2024-01-20T09:15:00Z'
    },
    {
      id: '3',
      orderNumber: 'ORD-2024-003',
      customerId: 'cust_3',
      customerName: 'Mike Johnson',
      customerEmail: '<EMAIL>',
      status: 'pending',
      paymentStatus: 'pending',
      paymentMethod: 'Bank Transfer',
      shippingAddress: {
        street: '789 Pine St',
        city: 'Chicago',
        state: 'IL',
        zipCode: '60601',
        country: 'USA'
      },
      billingAddress: {
        street: '789 Pine St',
        city: 'Chicago',
        state: 'IL',
        zipCode: '60601',
        country: 'USA'
      },
      items: [
        {
          id: 'item_4',
          productId: '2',
          productName: 'Smart Fitness Watch',
          sku: 'SFW-002',
          quantity: 1,
          price: 199.99,
          total: 199.99,
          image: 'https://images.unsplash.com/photo-*************-37898b6baf30?w=100'
        }
      ],
      subtotal: 199.99,
      tax: 16.00,
      shipping: 12.00,
      discount: 0,
      total: 227.99,
      createdAt: '2024-01-21T08:45:00Z',
      updatedAt: '2024-01-21T08:45:00Z'
    }
  ];

  useEffect(() => {
    setOrders(sampleOrders);
  }, []);

  const getStatusBadge = (status: string) => {
    const badges = {
      pending: 'bg-warning',
      processing: 'bg-info',
      shipped: 'bg-primary',
      delivered: 'bg-success',
      cancelled: 'bg-danger',
      refunded: 'bg-secondary'
    };
    return badges[status as keyof typeof badges] || 'bg-secondary';
  };

  const getPaymentStatusBadge = (status: string) => {
    const badges = {
      pending: 'bg-warning',
      paid: 'bg-success',
      failed: 'bg-danger',
      refunded: 'bg-secondary'
    };
    return badges[status as keyof typeof badges] || 'bg-secondary';
  };

  const handleStatusChange = async (orderId: string, newStatus: string) => {
    try {
      setLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setOrders(prev => prev.map(order => 
        order.id === orderId 
          ? { ...order, status: newStatus as any, updatedAt: new Date().toISOString() }
          : order
      ));
      
      toast.success('Order status updated successfully');
    } catch (error) {
      toast.error('Failed to update order status');
    } finally {
      setLoading(false);
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customerEmail.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  // Pagination
  const indexOfLastOrder = currentPage * ordersPerPage;
  const indexOfFirstOrder = indexOfLastOrder - ordersPerPage;
  const currentOrders = filteredOrders.slice(indexOfFirstOrder, indexOfLastOrder);
  const totalPages = Math.ceil(filteredOrders.length / ordersPerPage);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleExportOrders = async () => {
    try {
      setIsExporting(true);

      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Create CSV content
      const csvHeaders = ['Order Number', 'Customer Name', 'Customer Email', 'Status', 'Payment Status', 'Payment Method', 'Total', 'Items Count', 'Created Date'];
      const csvRows = filteredOrders.map(order => [
        order.orderNumber,
        order.customerName,
        order.customerEmail,
        order.status,
        order.paymentStatus,
        order.paymentMethod,
        order.total.toFixed(2),
        order.items.length,
        formatDate(order.createdAt)
      ]);

      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `orders-export-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success(`Successfully exported ${filteredOrders.length} orders`);
    } catch (error) {
      toast.error('Failed to export orders');
      console.error('Export error:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleCreateOrder = () => {
    console.log('Create Order button clicked');
    setShowCreateOrderForm(true);
  };

  return (
    <div className="container-fluid">
      {/* Page Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-sm-flex align-items-center justify-content-between">
            <div>
              <h4 className="mb-1 fw-bold">Orders Management</h4>
              <p className="text-muted mb-0">Track and manage customer orders</p>
            </div>
            <div className="d-flex gap-2">
              <button
                className="btn btn-outline-primary"
                onClick={handleExportOrders}
                disabled={isExporting || filteredOrders.length === 0}
              >
                {isExporting ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    Exporting...
                  </>
                ) : (
                  <>📊 Export Orders</>
                )}
              </button>
              <button
                className="btn btn-primary"
                onClick={handleCreateOrder}
              >
                ➕ Create Order
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="row mb-4">
        <div className="col-md-6">
          <input
            type="text"
            className="form-control"
            placeholder="Search orders by number, customer name, or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="col-md-3">
          <select
            className="form-select"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="processing">Processing</option>
            <option value="shipped">Shipped</option>
            <option value="delivered">Delivered</option>
            <option value="cancelled">Cancelled</option>
            <option value="refunded">Refunded</option>
          </select>
        </div>
        <div className="col-md-3">
          <div className="text-muted">
            Showing {currentOrders.length} of {filteredOrders.length} orders
          </div>
        </div>
      </div>

      {/* Orders Table */}
      <div className="card">
        <div className="table-responsive">
          <table className="table table-hover mb-0">
            <thead className="table-light">
              <tr>
                <th>Order #</th>
                <th>Customer</th>
                <th>Status</th>
                <th>Payment</th>
                <th>Items</th>
                <th>Total</th>
                <th>Date</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentOrders.length === 0 ? (
                <tr>
                  <td colSpan={8} className="text-center py-4">
                    <div className="text-muted">
                      {searchTerm || statusFilter !== 'all' ?
                        'No orders match your filters' :
                        'No orders found'
                      }
                    </div>
                  </td>
                </tr>
              ) : (
                currentOrders.map((order) => (
                  <tr key={order.id}>
                    <td>
                      <div className="fw-medium">{order.orderNumber}</div>
                      {order.trackingNumber && (
                        <small className="text-muted">Track: {order.trackingNumber}</small>
                      )}
                    </td>
                    <td>
                      <div>
                        <div className="fw-medium">{order.customerName}</div>
                        <small className="text-muted">{order.customerEmail}</small>
                      </div>
                    </td>
                    <td>
                      <span className={`badge ${getStatusBadge(order.status)}`}>
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </span>
                    </td>
                    <td>
                      <span className={`badge ${getPaymentStatusBadge(order.paymentStatus)}`}>
                        {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
                      </span>
                      <br />
                      <small className="text-muted">{order.paymentMethod}</small>
                    </td>
                    <td>
                      <span className="badge bg-light text-dark">
                        {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                      </span>
                    </td>
                    <td>
                      <span className="fw-medium">{formatCurrency(order.total)}</span>
                    </td>
                    <td>
                      <small className="text-muted">
                        {formatDate(order.createdAt)}
                      </small>
                    </td>
                    <td>
                      <div className="d-flex gap-1">
                        <button
                          className="btn btn-sm btn-outline-primary"
                          onClick={() => setSelectedOrder(order)}
                          title="View details"
                        >
                          👁️
                        </button>
                        <div className="dropdown position-relative">
                          <button
                            className="btn btn-sm btn-outline-secondary dropdown-toggle"
                            type="button"
                            onClick={() => setOpenDropdown(openDropdown === order.id ? null : order.id)}
                          >
                            Status
                          </button>
                          {openDropdown === order.id && (
                            <ul className="dropdown-menu show position-absolute" style={{ zIndex: 1050 }}>
                              <li><button className="dropdown-item" onClick={() => { handleStatusChange(order.id, 'pending'); setOpenDropdown(null); }}>Pending</button></li>
                              <li><button className="dropdown-item" onClick={() => { handleStatusChange(order.id, 'processing'); setOpenDropdown(null); }}>Processing</button></li>
                              <li><button className="dropdown-item" onClick={() => { handleStatusChange(order.id, 'shipped'); setOpenDropdown(null); }}>Shipped</button></li>
                              <li><button className="dropdown-item" onClick={() => { handleStatusChange(order.id, 'delivered'); setOpenDropdown(null); }}>Delivered</button></li>
                              <li><hr className="dropdown-divider" /></li>
                              <li><button className="dropdown-item text-danger" onClick={() => { handleStatusChange(order.id, 'cancelled'); setOpenDropdown(null); }}>Cancel</button></li>
                            </ul>
                          )}
                        </div>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create Order Modal */}
      {showCreateOrderForm && (
        <CreateOrderModal
          isOpen={showCreateOrderForm}
          onClose={() => setShowCreateOrderForm(false)}
          onSave={(newOrder) => {
            setOrders(prev => [newOrder, ...prev]);
            setShowCreateOrderForm(false);
            toast.success('Order created successfully');
          }}
        />
      )}

      {/* Order Details Modal */}
      <OrderDetailsModal
        order={selectedOrder}
        isOpen={!!selectedOrder}
        onClose={() => setSelectedOrder(null)}
      />
    </div>
  );
};

export default Orders;
