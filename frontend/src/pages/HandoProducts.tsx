import React, { useState } from 'react';

const HandoProducts: React.FC = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const products = [
    {
      id: 1,
      name: 'Wireless Bluetooth Headphones',
      category: 'Electronics',
      price: 89.99,
      stock: 45,
      status: 'Active',
      image: 'https://via.placeholder.com/150x150/3b82f6/ffffff?text=HP',
      rating: 4.5,
      sales: 234
    },
    {
      id: 2,
      name: 'Premium Leather Wallet',
      category: 'Fashion',
      price: 49.99,
      stock: 23,
      status: 'Active',
      image: 'https://via.placeholder.com/150x150/10b981/ffffff?text=LW',
      rating: 4.8,
      sales: 156
    },
    {
      id: 3,
      name: 'Smart Fitness Watch',
      category: 'Electronics',
      price: 199.99,
      stock: 0,
      status: 'Out of Stock',
      image: 'https://via.placeholder.com/150x150/f59e0b/ffffff?text=SW',
      rating: 4.3,
      sales: 89
    },
    {
      id: 4,
      name: 'Organic Cotton T-Shirt',
      category: 'Fashion',
      price: 29.99,
      stock: 67,
      status: 'Active',
      image: 'https://via.placeholder.com/150x150/ef4444/ffffff?text=TS',
      rating: 4.6,
      sales: 345
    },
    {
      id: 5,
      name: 'Professional Camera Lens',
      category: 'Electronics',
      price: 599.99,
      stock: 12,
      status: 'Low Stock',
      image: 'https://via.placeholder.com/150x150/8b5cf6/ffffff?text=CL',
      rating: 4.9,
      sales: 67
    },
    {
      id: 6,
      name: 'Yoga Mat Premium',
      category: 'Sports',
      price: 39.99,
      stock: 89,
      status: 'Active',
      image: 'https://via.placeholder.com/150x150/06b6d4/ffffff?text=YM',
      rating: 4.4,
      sales: 123
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-success-subtle text-success';
      case 'Out of Stock':
        return 'bg-danger-subtle text-danger';
      case 'Low Stock':
        return 'bg-warning-subtle text-warning';
      default:
        return 'bg-secondary-subtle text-secondary';
    }
  };

  return (
    <div className="container-fluid">
      {/* Page Title */}
      <div className="row">
        <div className="col-12">
          <div className="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 className="mb-sm-0 fs-18 fw-semibold">Products</h4>
            <div className="page-title-right">
              <ol className="breadcrumb m-0">
                <li className="breadcrumb-item"><a href="#">Hando</a></li>
                <li className="breadcrumb-item"><a href="#">Ecommerce</a></li>
                <li className="breadcrumb-item active">Products</li>
              </ol>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Actions */}
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-body">
              <div className="d-flex justify-content-between align-items-center flex-wrap gap-3">
                <div className="d-flex align-items-center gap-3">
                  <div className="search-box">
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Search products..."
                      style={{ width: '250px' }}
                    />
                  </div>
                  <select className="form-select" style={{ width: 'auto' }}>
                    <option>All Categories</option>
                    <option>Electronics</option>
                    <option>Fashion</option>
                    <option>Sports</option>
                  </select>
                  <select className="form-select" style={{ width: 'auto' }}>
                    <option>All Status</option>
                    <option>Active</option>
                    <option>Out of Stock</option>
                    <option>Low Stock</option>
                  </select>
                </div>
                
                <div className="d-flex align-items-center gap-2">
                  <div className="btn-group" role="group">
                    <button
                      type="button"
                      className={`btn btn-outline-secondary ${viewMode === 'grid' ? 'active' : ''}`}
                      onClick={() => setViewMode('grid')}
                    >
                      <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                      </svg>
                    </button>
                    <button
                      type="button"
                      className={`btn btn-outline-secondary ${viewMode === 'list' ? 'active' : ''}`}
                      onClick={() => setViewMode('list')}
                    >
                      <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                      </svg>
                    </button>
                  </div>
                  <button className="btn btn-primary">
                    <svg width="16" height="16" className="me-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Add Product
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Products Grid/List */}
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-body">
              {viewMode === 'grid' ? (
                <div className="row">
                  {products.map((product) => (
                    <div key={product.id} className="col-xl-3 col-lg-4 col-md-6 mb-4">
                      <div className="card product-card h-100">
                        <div className="position-relative">
                          <img
                            src={product.image}
                            className="card-img-top"
                            alt={product.name}
                            style={{ height: '200px', objectFit: 'cover' }}
                          />
                          <span className={`badge position-absolute top-0 end-0 m-2 ${getStatusBadge(product.status)}`}>
                            {product.status}
                          </span>
                        </div>
                        <div className="card-body d-flex flex-column">
                          <h6 className="card-title">{product.name}</h6>
                          <p className="text-muted small mb-2">{product.category}</p>
                          <div className="d-flex justify-content-between align-items-center mb-2">
                            <span className="fw-bold text-primary">${product.price}</span>
                            <span className="text-muted small">Stock: {product.stock}</span>
                          </div>
                          <div className="d-flex justify-content-between align-items-center mb-3">
                            <div className="d-flex align-items-center">
                              <span className="text-warning me-1">★</span>
                              <span className="small">{product.rating}</span>
                            </div>
                            <span className="text-muted small">{product.sales} sales</span>
                          </div>
                          <div className="mt-auto">
                            <div className="d-flex gap-2">
                              <button className="btn btn-outline-primary btn-sm flex-fill">Edit</button>
                              <button className="btn btn-outline-danger btn-sm">
                                <svg width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="table-responsive">
                  <table className="table table-hover align-middle">
                    <thead className="table-light">
                      <tr>
                        <th>Product</th>
                        <th>Category</th>
                        <th>Price</th>
                        <th>Stock</th>
                        <th>Rating</th>
                        <th>Sales</th>
                        <th>Status</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {products.map((product) => (
                        <tr key={product.id}>
                          <td>
                            <div className="d-flex align-items-center">
                              <img
                                src={product.image}
                                alt={product.name}
                                className="rounded me-3"
                                style={{ width: '50px', height: '50px', objectFit: 'cover' }}
                              />
                              <div>
                                <h6 className="mb-0">{product.name}</h6>
                                <small className="text-muted">ID: #{product.id}</small>
                              </div>
                            </div>
                          </td>
                          <td>{product.category}</td>
                          <td className="fw-bold text-primary">${product.price}</td>
                          <td>{product.stock}</td>
                          <td>
                            <div className="d-flex align-items-center">
                              <span className="text-warning me-1">★</span>
                              <span>{product.rating}</span>
                            </div>
                          </td>
                          <td>{product.sales}</td>
                          <td>
                            <span className={`badge ${getStatusBadge(product.status)}`}>
                              {product.status}
                            </span>
                          </td>
                          <td>
                            <div className="d-flex gap-2">
                              <button className="btn btn-sm btn-outline-primary">Edit</button>
                              <button className="btn btn-sm btn-outline-danger">Delete</button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HandoProducts;
