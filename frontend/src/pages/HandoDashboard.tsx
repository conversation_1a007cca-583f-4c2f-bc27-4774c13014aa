import React from 'react';

const HandoDashboard: React.FC = () => {
  return (
    <div className="container-fluid">
      {/* Page Title */}
      <div className="row">
        <div className="col-12">
          <div className="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 className="mb-sm-0 fs-18 fw-semibold">Dashboard</h4>
            <div className="page-title-right">
              <ol className="breadcrumb m-0">
                <li className="breadcrumb-item"><a href="#">Hando</a></li>
                <li className="breadcrumb-item active">Dashboard</li>
              </ol>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards Row */}
      <div className="row">
        <div className="col-xl-3 col-md-6">
          <div className="card widget-first">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-grow-1">
                  <p className="text-muted mb-1 fs-14">Overall Orders</p>
                  <h4 className="mb-0 fs-22 fw-semibold">57,800</h4>
                  <div className="d-flex align-items-center mt-2">
                    <span className="badge badge-custom-second bg-success-subtle text-success">
                      <svg className="w-3 h-3 me-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                      +2.5%
                    </span>
                    <span className="text-muted fs-14 ms-1">Data delivered for the last 7 days from worldwide</span>
                  </div>
                  <div className="progress mt-2" style={{height: '4px'}}>
                    <div className="progress-bar bg-primary" style={{width: '75%'}}></div>
                  </div>
                  <p className="text-muted fs-14 mt-1 mb-0">4,501 orders</p>
                </div>
                <div className="flex-shrink-0">
                  <div className="widget-icon bg-primary text-white">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6">
          <div className="card widget-first">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-grow-1">
                  <p className="text-muted mb-1 fs-14">Monthly Orders</p>
                  <h4 className="mb-0 fs-22 fw-semibold">57,800</h4>
                  <div className="d-flex align-items-center mt-2">
                    <span className="badge badge-custom-second bg-success-subtle text-success">
                      <svg width="12" height="12" className="me-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                      +0.8%
                    </span>
                    <span className="text-muted fs-14 ms-1">Data delivered for the last 7 days from worldwide</span>
                  </div>
                  <div className="progress mt-2" style={{height: '4px'}}>
                    <div className="progress-bar bg-success" style={{width: '60%'}}></div>
                  </div>
                  <p className="text-muted fs-14 mt-1 mb-0">25,548 orders</p>
                </div>
                <div className="flex-shrink-0">
                  <div className="widget-icon bg-success text-white">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6">
          <div className="card widget-first">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-grow-1">
                  <p className="text-muted mb-1 fs-14">Monthly Revenue</p>
                  <h4 className="mb-0 fs-22 fw-semibold">$637,254</h4>
                  <div className="d-flex align-items-center mt-2">
                    <span className="badge badge-custom-second bg-success-subtle text-success">
                      <svg width="12" height="12" className="me-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                      +2.5%
                    </span>
                    <span className="text-muted fs-14 ms-1">Data delivered for the last 7 days from worldwide</span>
                  </div>
                  <div className="progress mt-2" style={{height: '4px'}}>
                    <div className="progress-bar bg-info" style={{width: '85%'}}></div>
                  </div>
                  <p className="text-muted fs-14 mt-1 mb-0">$4,567 orders</p>
                </div>
                <div className="flex-shrink-0">
                  <div className="widget-icon bg-info text-white">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6">
          <div className="card widget-first">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-grow-1">
                  <p className="text-muted mb-1 fs-14">Out of Stock</p>
                  <h4 className="mb-0 fs-22 fw-semibold">142 Items</h4>
                  <div className="d-flex align-items-center mt-2">
                    <span className="badge badge-custom-second bg-success-subtle text-success">
                      <svg width="12" height="12" className="me-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                      +1.5%
                    </span>
                    <span className="text-muted fs-14 ms-1">Data delivered for the last 7 days from worldwide</span>
                  </div>
                  <div className="progress mt-2" style={{height: '4px'}}>
                    <div className="progress-bar bg-warning" style={{width: '45%'}}></div>
                  </div>
                  <p className="text-muted fs-14 mt-1 mb-0">45 orders</p>
                </div>
                <div className="flex-shrink-0">
                  <div className="widget-icon bg-warning text-white">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Overview and Sales by Country */}
      <div className="row">
        <div className="col-xl-8">
          <div className="card">
            <div className="card-header d-flex justify-content-between align-items-center">
              <h4 className="card-title mb-0">Performance Overview</h4>
              <div className="d-flex align-items-center">
                <span className="badge bg-primary me-2">Orders</span>
                <span className="badge bg-secondary">Sales</span>
              </div>
            </div>
            <div className="card-body">
              <div className="chart-container" style={{height: '300px', position: 'relative'}}>
                {/* Simulated Chart */}
                <div className="d-flex align-items-end justify-content-between h-100 px-3">
                  {['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].map((month, index) => (
                    <div key={month} className="d-flex flex-column align-items-center">
                      <div className="d-flex flex-column align-items-center mb-2">
                        <div
                          className="bg-primary rounded-top"
                          style={{
                            width: '20px',
                            height: `${Math.random() * 150 + 50}px`,
                            marginBottom: '2px'
                          }}
                        ></div>
                        <div
                          className="bg-secondary rounded-top"
                          style={{
                            width: '20px',
                            height: `${Math.random() * 100 + 30}px`
                          }}
                        ></div>
                      </div>
                      <small className="text-muted">{month}</small>
                    </div>
                  ))}
                </div>
                {/* Chart Legend */}
                <div className="position-absolute top-0 end-0 p-3">
                  <div className="d-flex flex-column">
                    <div className="d-flex align-items-center mb-1">
                      <div className="bg-primary rounded" style={{width: '12px', height: '12px', marginRight: '8px'}}></div>
                      <small>Orders</small>
                    </div>
                    <div className="d-flex align-items-center">
                      <div className="bg-secondary rounded" style={{width: '12px', height: '12px', marginRight: '8px'}}></div>
                      <small>Sales</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-4">
          <div className="card">
            <div className="card-header">
              <h4 className="card-title mb-0">Sales by Country</h4>
            </div>
            <div className="card-body">
              {/* World Map Placeholder */}
              <div className="text-center mb-4" style={{height: '150px', background: '#f8fafc', borderRadius: '8px', display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
                <div>
                  <svg width="48" height="48" className="text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-muted mb-0">World Map</p>
                </div>
              </div>

              {/* Country Stats */}
              <div className="country-stats">
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <div className="d-flex align-items-center">
                    <div className="flag-icon me-2" style={{width: '20px', height: '15px', background: '#3b82f6', borderRadius: '2px'}}></div>
                    <span className="fs-14">Germany</span>
                  </div>
                  <div className="text-end">
                    <div className="fs-14 fw-semibold">29%</div>
                    <div className="progress mt-1" style={{height: '4px', width: '60px'}}>
                      <div className="progress-bar bg-primary" style={{width: '29%'}}></div>
                    </div>
                  </div>
                </div>

                <div className="d-flex justify-content-between align-items-center mb-3">
                  <div className="d-flex align-items-center">
                    <div className="flag-icon me-2" style={{width: '20px', height: '15px', background: '#10b981', borderRadius: '2px'}}></div>
                    <span className="fs-14">USA</span>
                  </div>
                  <div className="text-end">
                    <div className="fs-14 fw-semibold">40%</div>
                    <div className="progress mt-1" style={{height: '4px', width: '60px'}}>
                      <div className="progress-bar bg-success" style={{width: '40%'}}></div>
                    </div>
                  </div>
                </div>

                <div className="d-flex justify-content-between align-items-center mb-3">
                  <div className="d-flex align-items-center">
                    <div className="flag-icon me-2" style={{width: '20px', height: '15px', background: '#f59e0b', borderRadius: '2px'}}></div>
                    <span className="fs-14">Australia</span>
                  </div>
                  <div className="text-end">
                    <div className="fs-14 fw-semibold">25%</div>
                    <div className="progress mt-1" style={{height: '4px', width: '60px'}}>
                      <div className="progress-bar bg-warning" style={{width: '25%'}}></div>
                    </div>
                  </div>
                </div>

                <div className="d-flex justify-content-between align-items-center">
                  <div className="d-flex align-items-center">
                    <div className="flag-icon me-2" style={{width: '20px', height: '15px', background: '#ef4444', borderRadius: '2px'}}></div>
                    <span className="fs-14">Argentina</span>
                  </div>
                  <div className="text-end">
                    <div className="fs-14 fw-semibold">15%</div>
                    <div className="progress mt-1" style={{height: '4px', width: '60px'}}>
                      <div className="progress-bar bg-danger" style={{width: '15%'}}></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Top Selling Products, Customer Rate, and Order Report */}
      <div className="row">
        <div className="col-xl-4">
          <div className="card">
            <div className="card-header d-flex justify-content-between align-items-center">
              <h4 className="card-title mb-0">Top Selling Products</h4>
              <a href="#" className="text-primary fs-14">View All →</a>
            </div>
            <div className="card-body">
              <div className="product-list">
                {[
                  { name: 'Headphone Stereo Laptop Bag', price: '$78,987', sales: 355, color: '#3b82f6' },
                  { name: 'Classic Stylish Watch', price: '$50,569', sales: 355, color: '#10b981' },
                  { name: 'New Headphones', price: '$50,987', sales: 355, color: '#f59e0b' },
                  { name: 'Leather Jacket', price: '$39,00', sales: 355, color: '#ef4444' },
                  { name: 'Nike Air Shoes', price: '$99,78', sales: 355, color: '#8b5cf6' }
                ].map((product, index) => (
                  <div key={index} className="d-flex align-items-center mb-3">
                    <div className="flex-shrink-0 me-3">
                      <div
                        className="rounded"
                        style={{
                          width: '40px',
                          height: '40px',
                          background: product.color,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <svg width="16" height="16" className="text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                      </div>
                    </div>
                    <div className="flex-grow-1">
                      <h6 className="mb-1 fs-14">{product.name}</h6>
                      <div className="d-flex justify-content-between">
                        <span className="text-muted fs-13">{product.price}</span>
                        <span className="text-muted fs-13">{product.sales}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-4">
          <div className="card">
            <div className="card-header">
              <h4 className="card-title mb-0">Repeat Customer Rate</h4>
            </div>
            <div className="card-body">
              {/* Circular Progress */}
              <div className="text-center mb-4">
                <div className="position-relative d-inline-block">
                  <svg width="120" height="120" className="circular-progress">
                    <circle
                      cx="60"
                      cy="60"
                      r="50"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="8"
                    />
                    <circle
                      cx="60"
                      cy="60"
                      r="50"
                      fill="none"
                      stroke="#3b82f6"
                      strokeWidth="8"
                      strokeDasharray={`${2 * Math.PI * 50}`}
                      strokeDashoffset={`${2 * Math.PI * 50 * (1 - 0.92)}`}
                      strokeLinecap="round"
                      transform="rotate(-90 60 60)"
                    />
                  </svg>
                  <div className="position-absolute top-50 start-50 translate-middle text-center">
                    <h3 className="mb-0 fw-bold">92%</h3>
                    <small className="text-muted">Orders</small>
                  </div>
                </div>
              </div>

              {/* Customer Stats */}
              <div className="customer-stats">
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <div className="d-flex align-items-center">
                    <div className="bg-primary rounded-circle me-2" style={{width: '8px', height: '8px'}}></div>
                    <span className="fs-14">New Customer</span>
                  </div>
                  <span className="fs-14 fw-semibold">4.8</span>
                </div>

                <div className="d-flex justify-content-between align-items-center mb-3">
                  <div className="d-flex align-items-center">
                    <div className="bg-success rounded-circle me-2" style={{width: '8px', height: '8px'}}></div>
                    <span className="fs-14">Old Customer</span>
                  </div>
                  <span className="fs-14 fw-semibold">4.6</span>
                </div>

                <div className="rating-bars">
                  {[5, 4, 3, 2, 1].map((stars) => (
                    <div key={stars} className="d-flex align-items-center mb-2">
                      <span className="fs-14 me-2">{stars} Stars</span>
                      <div className="flex-grow-1 mx-2">
                        <div className="progress" style={{height: '6px'}}>
                          <div
                            className="progress-bar bg-primary"
                            style={{width: `${stars === 5 ? 80 : stars === 4 ? 60 : stars === 3 ? 40 : stars === 2 ? 20 : 10}%`}}
                          ></div>
                        </div>
                      </div>
                      <span className="fs-14 text-muted">{stars === 5 ? '80%' : stars === 4 ? '60%' : stars === 3 ? '40%' : stars === 2 ? '20%' : '10%'}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-4">
          <div className="card">
            <div className="card-header">
              <h4 className="card-title mb-0">Order Report</h4>
            </div>
            <div className="card-body">
              <div className="text-center mb-4">
                <h3 className="text-primary mb-1">1584 Orders</h3>
                <p className="text-muted mb-0">+15%</p>
              </div>

              {/* Donut Chart Simulation */}
              <div className="text-center mb-4">
                <div className="position-relative d-inline-block">
                  <svg width="100" height="100">
                    <circle cx="50" cy="50" r="40" fill="none" stroke="#e5e7eb" strokeWidth="12"/>
                    <circle cx="50" cy="50" r="40" fill="none" stroke="#3b82f6" strokeWidth="12"
                            strokeDasharray={`${2 * Math.PI * 40 * 0.6}`}
                            strokeDashoffset="0"
                            transform="rotate(-90 50 50)"/>
                    <circle cx="50" cy="50" r="40" fill="none" stroke="#10b981" strokeWidth="12"
                            strokeDasharray={`${2 * Math.PI * 40 * 0.3}`}
                            strokeDashoffset={`-${2 * Math.PI * 40 * 0.6}`}
                            transform="rotate(-90 50 50)"/>
                  </svg>
                </div>
              </div>

              <div className="legend">
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <div className="d-flex align-items-center">
                    <div className="bg-primary rounded me-2" style={{width: '12px', height: '12px'}}></div>
                    <span className="fs-14">Completed</span>
                  </div>
                  <span className="fs-14 fw-semibold">60%</span>
                </div>
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <div className="d-flex align-items-center">
                    <div className="bg-success rounded me-2" style={{width: '12px', height: '12px'}}></div>
                    <span className="fs-14">Pending</span>
                  </div>
                  <span className="fs-14 fw-semibold">30%</span>
                </div>
                <div className="d-flex justify-content-between align-items-center">
                  <div className="d-flex align-items-center">
                    <div className="bg-secondary rounded me-2" style={{width: '12px', height: '12px'}}></div>
                    <span className="fs-14">Cancelled</span>
                  </div>
                  <span className="fs-14 fw-semibold">10%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Product Orders Table */}
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-header d-flex justify-content-between align-items-center">
              <h4 className="card-title mb-0">Product Orders</h4>
              <div className="d-flex align-items-center">
                <select className="form-select form-select-sm me-2" style={{width: 'auto'}}>
                  <option>Category</option>
                  <option>Electronics</option>
                  <option>Fashion</option>
                  <option>Sports</option>
                </select>
                <select className="form-select form-select-sm me-2" style={{width: 'auto'}}>
                  <option>Campaign</option>
                  <option>Summer Sale</option>
                  <option>Winter Sale</option>
                </select>
                <select className="form-select form-select-sm me-2" style={{width: 'auto'}}>
                  <option>Status</option>
                  <option>Active</option>
                  <option>Inactive</option>
                </select>
                <select className="form-select form-select-sm" style={{width: 'auto'}}>
                  <option>Stock</option>
                  <option>In Stock</option>
                  <option>Out of Stock</option>
                </select>
              </div>
            </div>
            <div className="card-body">
              <div className="table-responsive">
                <table className="table table-hover align-middle">
                  <thead className="table-light">
                    <tr>
                      <th>Order ID</th>
                      <th>Customer Name</th>
                      <th>Items</th>
                      <th>Total</th>
                      <th>Created</th>
                      <th>Modified</th>
                      <th>Status</th>
                      <th>Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {[
                      { id: '#2453', customer: 'Richard Dom', avatar: 'RD', items: 2, total: '$240.00', created: 'August 09, 2023', modified: 'August 16, 2023', status: 'Pending', statusColor: 'warning' },
                      { id: '#2456', customer: 'Susan Jane', avatar: 'SJ', items: 1, total: '$560.00', created: 'January 26, 2023', modified: 'March 09, 2023', status: 'Pending', statusColor: 'warning' },
                      { id: '#2352', customer: 'Stacie Bob', avatar: 'SB', items: 3, total: '$350.00', created: 'April 05, 2023', modified: 'June 21, 2023', status: 'Cancelled', statusColor: 'danger' },
                      { id: '#7565', customer: 'Emma Wilson', avatar: 'EW', items: 5, total: '$950.00', created: 'September 24, 2023', modified: 'November 12, 2023', status: 'Completed', statusColor: 'success' },
                      { id: '#4526', customer: 'Light Bulkins', avatar: 'LB', items: 2, total: '$750.00', created: 'July 26, 2023', modified: 'August 25, 2023', status: 'Pending', statusColor: 'warning' },
                      { id: '#9254', customer: 'Angelina Hose', avatar: 'AH', items: 4, total: '$650.00', created: 'June 09, 2023', modified: 'August 20, 2023', status: 'Completed', statusColor: 'success' }
                    ].map((order, index) => (
                      <tr key={index}>
                        <td>
                          <span className="fw-semibold text-primary">{order.id}</span>
                        </td>
                        <td>
                          <div className="d-flex align-items-center">
                            <div className="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                              <span className="fs-14">{order.avatar}</span>
                            </div>
                            <span>{order.customer}</span>
                          </div>
                        </td>
                        <td>
                          <span className="badge bg-light text-dark">{order.items}</span>
                        </td>
                        <td>
                          <span className="fw-semibold">{order.total}</span>
                        </td>
                        <td>
                          <span className="text-muted">{order.created}</span>
                        </td>
                        <td>
                          <span className="text-muted">{order.modified}</span>
                        </td>
                        <td>
                          <span className={`badge bg-${order.statusColor}-subtle text-${order.statusColor}`}>
                            {order.status}
                          </span>
                        </td>
                        <td>
                          <div className="d-flex gap-2">
                            <button className="btn btn-sm btn-outline-primary">
                              <svg width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                              </svg>
                            </button>
                            <button className="btn btn-sm btn-outline-danger">
                              <svg width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="d-flex justify-content-between align-items-center mt-4">
                <div className="text-muted">
                  1 of 3
                </div>
                <nav>
                  <ul className="pagination pagination-sm mb-0">
                    <li className="page-item">
                      <a className="page-link" href="#" aria-label="Previous">Prev</a>
                    </li>
                    <li className="page-item active">
                      <a className="page-link" href="#">1</a>
                    </li>
                    <li className="page-item">
                      <a className="page-link" href="#" aria-label="Next">Next</a>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HandoDashboard;
