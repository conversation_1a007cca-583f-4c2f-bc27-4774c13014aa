import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface AnalyticsData {
  overview: {
    totalClients: number;
    activeClients: number;
    trialClients: number;
    monthlyRevenue: number;
    yearlyRevenue: number;
    churnRate: number;
    growthRate: number;
    avgRevenuePerUser: number;
  };
  revenueByPlan: {
    starter: number;
    professional: number;
    enterprise: number;
    custom: number;
  };
  clientsByStatus: {
    active: number;
    trial: number;
    suspended: number;
    expired: number;
  };
  monthlyTrends: {
    month: string;
    newClients: number;
    revenue: number;
    churn: number;
  }[];
  topClients: {
    id: string;
    name: string;
    plan: string;
    revenue: number;
    status: string;
  }[];
  conversionMetrics: {
    trialToActive: number;
    starterToPro: number;
    proToEnterprise: number;
  };
}

const Analytics: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('30');
  const [selectedMetric, setSelectedMetric] = useState('revenue');

  useEffect(() => {
    loadAnalyticsData();
  }, [dateRange]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      // Simulate API call - in production, this would fetch real data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockData: AnalyticsData = {
        overview: {
          totalClients: 156,
          activeClients: 142,
          trialClients: 8,
          monthlyRevenue: 18450,
          yearlyRevenue: 221400,
          churnRate: 2.3,
          growthRate: 15.7,
          avgRevenuePerUser: 130
        },
        revenueByPlan: {
          starter: 4350,
          professional: 9480,
          enterprise: 3970,
          custom: 650
        },
        clientsByStatus: {
          active: 142,
          trial: 8,
          suspended: 4,
          expired: 2
        },
        monthlyTrends: [
          { month: 'Jan', newClients: 12, revenue: 15200, churn: 2 },
          { month: 'Feb', newClients: 18, revenue: 16800, churn: 1 },
          { month: 'Mar', newClients: 15, revenue: 17200, churn: 3 },
          { month: 'Apr', newClients: 22, revenue: 18100, churn: 2 },
          { month: 'May', newClients: 19, revenue: 18450, churn: 1 },
          { month: 'Jun', newClients: 25, revenue: 19200, churn: 2 }
        ],
        topClients: [
          { id: '1', name: 'Enterprise Corp', plan: 'Enterprise', revenue: 1990, status: 'active' },
          { id: '2', name: 'TechCorp Solutions', plan: 'Professional', revenue: 790, status: 'active' },
          { id: '3', name: 'Global Industries', plan: 'Enterprise', revenue: 1990, status: 'active' },
          { id: '4', name: 'StartupXYZ', plan: 'Professional', revenue: 790, status: 'active' },
          { id: '5', name: 'Innovation Labs', plan: 'Custom', revenue: 2500, status: 'active' }
        ],
        conversionMetrics: {
          trialToActive: 78.5,
          starterToPro: 34.2,
          proToEnterprise: 12.8
        }
      };
      
      setAnalyticsData(mockData);
    } catch (error) {
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'text-success',
      trial: 'text-info',
      suspended: 'text-warning',
      expired: 'text-danger'
    };
    return colors[status as keyof typeof colors] || 'text-secondary';
  };

  const getPlanColor = (plan: string) => {
    const colors = {
      starter: 'bg-primary',
      professional: 'bg-success',
      enterprise: 'bg-warning',
      custom: 'bg-dark'
    };
    return colors[plan.toLowerCase() as keyof typeof colors] || 'bg-secondary';
  };

  if (loading) {
    return (
      <div className="container-fluid p-4">
        <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
          <div className="text-center">
            <div className="spinner-border text-primary mb-3" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <h5>Loading Analytics...</h5>
          </div>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="container-fluid p-4">
        <div className="alert alert-danger">
          Failed to load analytics data. Please try again.
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Analytics Dashboard</h1>
          <p className="text-muted mb-0">Comprehensive insights into your SAAS business performance</p>
        </div>
        <div className="d-flex gap-2">
          <select 
            className="form-select"
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            style={{ width: 'auto' }}
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
            <option value="365">Last year</option>
          </select>
          <button 
            className="btn btn-outline-primary"
            onClick={loadAnalyticsData}
          >
            🔄 Refresh
          </button>
          <button className="btn btn-primary">
            📊 Export Report
          </button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="row mb-4">
        <div className="col-md-3 mb-3">
          <div className="card bg-primary text-white h-100">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{analyticsData.overview.totalClients}</h4>
                  <small>Total Clients</small>
                  <div className="mt-1">
                    <small className="opacity-75">
                      +{analyticsData.overview.growthRate}% growth
                    </small>
                  </div>
                </div>
                <div className="align-self-center">
                  <i className="fas fa-users fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3 mb-3">
          <div className="card bg-success text-white h-100">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{formatCurrency(analyticsData.overview.monthlyRevenue)}</h4>
                  <small>Monthly Revenue</small>
                  <div className="mt-1">
                    <small className="opacity-75">
                      ARR: {formatCurrency(analyticsData.overview.yearlyRevenue)}
                    </small>
                  </div>
                </div>
                <div className="align-self-center">
                  <i className="fas fa-dollar-sign fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3 mb-3">
          <div className="card bg-info text-white h-100">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{formatCurrency(analyticsData.overview.avgRevenuePerUser)}</h4>
                  <small>Avg Revenue Per User</small>
                  <div className="mt-1">
                    <small className="opacity-75">
                      ARPU (Monthly)
                    </small>
                  </div>
                </div>
                <div className="align-self-center">
                  <i className="fas fa-chart-line fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3 mb-3">
          <div className="card bg-warning text-white h-100">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{formatPercentage(analyticsData.overview.churnRate)}</h4>
                  <small>Churn Rate</small>
                  <div className="mt-1">
                    <small className="opacity-75">
                      Monthly churn
                    </small>
                  </div>
                </div>
                <div className="align-self-center">
                  <i className="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="row mb-4">
        <div className="col-md-8">
          <div className="card">
            <div className="card-header">
              <div className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">Revenue Trends</h5>
                <div className="btn-group btn-group-sm">
                  <button 
                    className={`btn ${selectedMetric === 'revenue' ? 'btn-primary' : 'btn-outline-primary'}`}
                    onClick={() => setSelectedMetric('revenue')}
                  >
                    Revenue
                  </button>
                  <button 
                    className={`btn ${selectedMetric === 'clients' ? 'btn-primary' : 'btn-outline-primary'}`}
                    onClick={() => setSelectedMetric('clients')}
                  >
                    New Clients
                  </button>
                  <button 
                    className={`btn ${selectedMetric === 'churn' ? 'btn-primary' : 'btn-outline-primary'}`}
                    onClick={() => setSelectedMetric('churn')}
                  >
                    Churn
                  </button>
                </div>
              </div>
            </div>
            <div className="card-body">
              <div className="row">
                {analyticsData.monthlyTrends.map((trend, index) => (
                  <div key={index} className="col-2 text-center mb-3">
                    <div className="mb-2">
                      <div 
                        className="bg-primary rounded"
                        style={{ 
                          height: `${selectedMetric === 'revenue' ? (trend.revenue / 200) : 
                                   selectedMetric === 'clients' ? (trend.newClients * 4) : 
                                   (trend.churn * 20)}px`,
                          minHeight: '20px',
                          width: '100%'
                        }}
                      ></div>
                    </div>
                    <small className="text-muted">{trend.month}</small>
                    <div className="small fw-bold">
                      {selectedMetric === 'revenue' ? formatCurrency(trend.revenue) :
                       selectedMetric === 'clients' ? trend.newClients :
                       trend.churn}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card">
            <div className="card-header">
              <h5 className="mb-0">Revenue by Plan</h5>
            </div>
            <div className="card-body">
              {Object.entries(analyticsData.revenueByPlan).map(([plan, revenue]) => (
                <div key={plan} className="mb-3">
                  <div className="d-flex justify-content-between align-items-center mb-1">
                    <span className="text-capitalize">{plan}</span>
                    <span className="fw-bold">{formatCurrency(revenue)}</span>
                  </div>
                  <div className="progress" style={{ height: '8px' }}>
                    <div 
                      className={`progress-bar ${getPlanColor(plan)}`}
                      style={{ 
                        width: `${(revenue / Math.max(...Object.values(analyticsData.revenueByPlan))) * 100}%` 
                      }}
                    ></div>
                  </div>
                  <small className="text-muted">
                    {((revenue / Object.values(analyticsData.revenueByPlan).reduce((a, b) => a + b, 0)) * 100).toFixed(1)}% of total
                  </small>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Client Status & Conversion Metrics */}
      <div className="row mb-4">
        <div className="col-md-4">
          <div className="card">
            <div className="card-header">
              <h5 className="mb-0">Client Status Distribution</h5>
            </div>
            <div className="card-body">
              {Object.entries(analyticsData.clientsByStatus).map(([status, count]) => (
                <div key={status} className="d-flex justify-content-between align-items-center mb-2">
                  <div className="d-flex align-items-center">
                    <div
                      className={`rounded-circle me-2 ${getStatusColor(status)}`}
                      style={{ width: '12px', height: '12px', backgroundColor: 'currentColor' }}
                    ></div>
                    <span className="text-capitalize">{status}</span>
                  </div>
                  <div className="d-flex align-items-center">
                    <span className="fw-bold me-2">{count}</span>
                    <small className="text-muted">
                      ({((count / analyticsData.overview.totalClients) * 100).toFixed(1)}%)
                    </small>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card">
            <div className="card-header">
              <h5 className="mb-0">Conversion Metrics</h5>
            </div>
            <div className="card-body">
              <div className="mb-3">
                <div className="d-flex justify-content-between align-items-center mb-1">
                  <span>Trial → Active</span>
                  <span className="fw-bold text-success">{formatPercentage(analyticsData.conversionMetrics.trialToActive)}</span>
                </div>
                <div className="progress" style={{ height: '6px' }}>
                  <div
                    className="progress-bar bg-success"
                    style={{ width: `${analyticsData.conversionMetrics.trialToActive}%` }}
                  ></div>
                </div>
              </div>
              <div className="mb-3">
                <div className="d-flex justify-content-between align-items-center mb-1">
                  <span>Starter → Pro</span>
                  <span className="fw-bold text-info">{formatPercentage(analyticsData.conversionMetrics.starterToPro)}</span>
                </div>
                <div className="progress" style={{ height: '6px' }}>
                  <div
                    className="progress-bar bg-info"
                    style={{ width: `${analyticsData.conversionMetrics.starterToPro}%` }}
                  ></div>
                </div>
              </div>
              <div className="mb-3">
                <div className="d-flex justify-content-between align-items-center mb-1">
                  <span>Pro → Enterprise</span>
                  <span className="fw-bold text-warning">{formatPercentage(analyticsData.conversionMetrics.proToEnterprise)}</span>
                </div>
                <div className="progress" style={{ height: '6px' }}>
                  <div
                    className="progress-bar bg-warning"
                    style={{ width: `${analyticsData.conversionMetrics.proToEnterprise}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card">
            <div className="card-header">
              <h5 className="mb-0">Top Revenue Clients</h5>
            </div>
            <div className="card-body">
              {analyticsData.topClients.map((client, index) => (
                <div key={client.id} className="d-flex justify-content-between align-items-center mb-2">
                  <div>
                    <div className="fw-bold">{client.name}</div>
                    <small className="text-muted">
                      <span className={`badge ${getPlanColor(client.plan)} badge-sm me-1`}>
                        {client.plan}
                      </span>
                      <span className={getStatusColor(client.status)}>
                        {client.status}
                      </span>
                    </small>
                  </div>
                  <div className="text-end">
                    <div className="fw-bold">{formatCurrency(client.revenue)}</div>
                    <small className="text-muted">#{index + 1}</small>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Analytics Table */}
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <div className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">Monthly Performance Summary</h5>
                <button className="btn btn-outline-primary btn-sm">
                  📈 View Detailed Report
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="table-responsive">
                <table className="table table-hover">
                  <thead className="table-light">
                    <tr>
                      <th>Month</th>
                      <th>New Clients</th>
                      <th>Revenue</th>
                      <th>Churn</th>
                      <th>Growth Rate</th>
                      <th>ARPU</th>
                      <th>Conversion Rate</th>
                    </tr>
                  </thead>
                  <tbody>
                    {analyticsData.monthlyTrends.map((trend, index) => {
                      const growthRate = index > 0 ?
                        ((trend.revenue - analyticsData.monthlyTrends[index - 1].revenue) / analyticsData.monthlyTrends[index - 1].revenue * 100) : 0;
                      const arpu = trend.revenue / (analyticsData.overview.activeClients - trend.churn);
                      const conversionRate = (trend.newClients / (trend.newClients + 5)) * 100; // Mock conversion rate

                      return (
                        <tr key={index}>
                          <td className="fw-bold">{trend.month} 2024</td>
                          <td>
                            <span className="badge bg-primary">{trend.newClients}</span>
                          </td>
                          <td className="fw-bold">{formatCurrency(trend.revenue)}</td>
                          <td>
                            <span className={`badge ${trend.churn <= 2 ? 'bg-success' : 'bg-warning'}`}>
                              {trend.churn}
                            </span>
                          </td>
                          <td>
                            <span className={`${growthRate >= 0 ? 'text-success' : 'text-danger'}`}>
                              {growthRate >= 0 ? '+' : ''}{growthRate.toFixed(1)}%
                            </span>
                          </td>
                          <td>{formatCurrency(arpu)}</td>
                          <td>
                            <span className="text-info">{conversionRate.toFixed(1)}%</span>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
