import React, { useState } from 'react';
import { toast } from 'react-hot-toast';

interface ReportType {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'sales' | 'inventory' | 'customers' | 'products' | 'financial';
  fields: string[];
}

interface ReportFilters {
  startDate: string;
  endDate: string;
  reportType: string;
  format: 'csv' | 'pdf' | 'excel';
  includeCharts: boolean;
}

const Reports: React.FC = () => {
  const [filters, setFilters] = useState<ReportFilters>({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    endDate: new Date().toISOString().split('T')[0], // today
    reportType: '',
    format: 'csv',
    includeCharts: false
  });

  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const reportTypes: ReportType[] = [
    // Sales Reports
    {
      id: 'sales-summary',
      name: 'Sales Summary Report',
      description: 'Overview of total sales, revenue, and transaction counts',
      icon: '📊',
      category: 'sales',
      fields: ['Order ID', 'Date', 'Customer', 'Total Amount', 'Payment Method', 'Status']
    },
    {
      id: 'sales-by-product',
      name: 'Sales by Product Report',
      description: 'Product-wise sales performance and revenue breakdown',
      icon: '🛍️',
      category: 'sales',
      fields: ['Product Name', 'SKU', 'Quantity Sold', 'Revenue', 'Profit Margin']
    },
    {
      id: 'sales-by-customer',
      name: 'Sales by Customer Report',
      description: 'Customer-wise purchase history and spending analysis',
      icon: '👥',
      category: 'sales',
      fields: ['Customer Name', 'Email', 'Total Orders', 'Total Spent', 'Average Order Value']
    },
    {
      id: 'daily-sales',
      name: 'Daily Sales Report',
      description: 'Day-by-day sales performance and trends',
      icon: '📅',
      category: 'sales',
      fields: ['Date', 'Orders Count', 'Revenue', 'Average Order Value', 'Top Product']
    },

    // Inventory Reports
    {
      id: 'inventory-levels',
      name: 'Current Inventory Levels',
      description: 'Current stock levels for all products',
      icon: '📦',
      category: 'inventory',
      fields: ['Product Name', 'SKU', 'Current Stock', 'Reorder Level', 'Status']
    },
    {
      id: 'low-stock-alert',
      name: 'Low Stock Alert Report',
      description: 'Products that are running low on inventory',
      icon: '⚠️',
      category: 'inventory',
      fields: ['Product Name', 'SKU', 'Current Stock', 'Reorder Level', 'Supplier']
    },
    {
      id: 'inventory-movement',
      name: 'Inventory Movement Report',
      description: 'Track inventory changes over time',
      icon: '🔄',
      category: 'inventory',
      fields: ['Product', 'Date', 'Movement Type', 'Quantity', 'Reason', 'Balance']
    },
    {
      id: 'expiry-report',
      name: 'Product Expiry Report',
      description: 'Products approaching expiration dates',
      icon: '⏰',
      category: 'inventory',
      fields: ['Product Name', 'SKU', 'Batch Number', 'Expiry Date', 'Days Remaining', 'Quantity']
    },

    // Product Performance Reports
    {
      id: 'top-products',
      name: 'Top Performing Products',
      description: 'Best selling products by quantity and revenue',
      icon: '🏆',
      category: 'products',
      fields: ['Product Name', 'SKU', 'Units Sold', 'Revenue', 'Profit', 'Growth %']
    },
    {
      id: 'slow-moving',
      name: 'Slow Moving Products',
      description: 'Products with low sales velocity',
      icon: '🐌',
      category: 'products',
      fields: ['Product Name', 'SKU', 'Last Sale Date', 'Current Stock', 'Days Since Sale']
    },
    {
      id: 'product-performance',
      name: 'Product Performance Analysis',
      description: 'Comprehensive product performance metrics',
      icon: '📈',
      category: 'products',
      fields: ['Product', 'Sales Volume', 'Revenue', 'Profit Margin', 'Return Rate', 'Rating']
    },

    // Customer Reports
    {
      id: 'customer-analysis',
      name: 'Customer Analysis Report',
      description: 'Customer behavior and purchasing patterns',
      icon: '👤',
      category: 'customers',
      fields: ['Customer Name', 'Registration Date', 'Total Orders', 'Total Spent', 'Last Order']
    },
    {
      id: 'customer-segments',
      name: 'Customer Segmentation Report',
      description: 'Customer groups based on spending and behavior',
      icon: '🎯',
      category: 'customers',
      fields: ['Segment', 'Customer Count', 'Average Spend', 'Order Frequency', 'Retention Rate']
    },
    {
      id: 'wishlist-report',
      name: 'Wishlist Analysis Report',
      description: 'Most wishlisted products and customer preferences',
      icon: '❤️',
      category: 'customers',
      fields: ['Product Name', 'Wishlist Count', 'Conversion Rate', 'Average Days to Purchase']
    },

    // Financial Reports
    {
      id: 'profit-loss',
      name: 'Profit & Loss Statement',
      description: 'Comprehensive P&L report with revenue and expenses',
      icon: '💰',
      category: 'financial',
      fields: ['Period', 'Revenue', 'COGS', 'Gross Profit', 'Expenses', 'Net Profit']
    },
    {
      id: 'revenue-analysis',
      name: 'Revenue Analysis Report',
      description: 'Revenue breakdown by products, categories, and time periods',
      icon: '💹',
      category: 'financial',
      fields: ['Category', 'Revenue', 'Growth %', 'Contribution %', 'Trend']
    },
    {
      id: 'offer-performance',
      name: 'Offers & Promotions Report',
      description: 'Performance analysis of discounts and promotional campaigns',
      icon: '🎁',
      category: 'financial',
      fields: ['Offer Name', 'Start Date', 'End Date', 'Usage Count', 'Revenue Impact', 'ROI']
    }
  ];

  const categories = [
    { id: 'all', name: 'All Reports', icon: '📋' },
    { id: 'sales', name: 'Sales Reports', icon: '📊' },
    { id: 'inventory', name: 'Inventory Reports', icon: '📦' },
    { id: 'products', name: 'Product Reports', icon: '🛍️' },
    { id: 'customers', name: 'Customer Reports', icon: '👥' },
    { id: 'financial', name: 'Financial Reports', icon: '💰' }
  ];

  const filteredReports = selectedCategory === 'all'
    ? reportTypes
    : reportTypes.filter(report => report.category === selectedCategory);

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const generateSampleData = (reportType: ReportType, startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

    // Generate sample data based on report type
    switch (reportType.id) {
      case 'sales-summary':
        return Array.from({ length: Math.min(daysDiff, 100) }, (_, i) => ({
          'Order ID': `ORD-${2024}-${String(1000 + i).padStart(6, '0')}`,
          'Date': new Date(start.getTime() + i * 24 * 60 * 60 * 1000).toLocaleDateString(),
          'Customer': `Customer ${i + 1}`,
          'Total Amount': (Math.random() * 500 + 50).toFixed(2),
          'Payment Method': ['Credit Card', 'PayPal', 'Bank Transfer'][Math.floor(Math.random() * 3)],
          'Status': ['Completed', 'Pending', 'Shipped'][Math.floor(Math.random() * 3)]
        }));

      case 'inventory-levels':
        return Array.from({ length: 50 }, (_, i) => ({
          'Product Name': `Product ${i + 1}`,
          'SKU': `SKU-${String(i + 1).padStart(4, '0')}`,
          'Current Stock': Math.floor(Math.random() * 1000),
          'Reorder Level': Math.floor(Math.random() * 100 + 10),
          'Status': Math.random() > 0.7 ? 'Low Stock' : 'In Stock'
        }));

      case 'top-products':
        return Array.from({ length: 20 }, (_, i) => ({
          'Product Name': `Top Product ${i + 1}`,
          'SKU': `TOP-${String(i + 1).padStart(4, '0')}`,
          'Units Sold': Math.floor(Math.random() * 500 + 100),
          'Revenue': (Math.random() * 10000 + 1000).toFixed(2),
          'Profit': (Math.random() * 3000 + 500).toFixed(2),
          'Growth %': (Math.random() * 50 + 10).toFixed(1)
        }));

      case 'customer-analysis':
        return Array.from({ length: 30 }, (_, i) => ({
          'Customer Name': `Customer ${i + 1}`,
          'Registration Date': new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toLocaleDateString(),
          'Total Orders': Math.floor(Math.random() * 20 + 1),
          'Total Spent': (Math.random() * 2000 + 100).toFixed(2),
          'Last Order': new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString()
        }));

      case 'profit-loss':
        return Array.from({ length: 12 }, (_, i) => ({
          'Period': `Month ${i + 1}`,
          'Revenue': (Math.random() * 50000 + 20000).toFixed(2),
          'COGS': (Math.random() * 30000 + 10000).toFixed(2),
          'Gross Profit': (Math.random() * 20000 + 5000).toFixed(2),
          'Expenses': (Math.random() * 10000 + 2000).toFixed(2),
          'Net Profit': (Math.random() * 15000 + 1000).toFixed(2)
        }));

      default:
        return Array.from({ length: 20 }, (_, i) =>
          reportType.fields.reduce((acc, field) => ({
            ...acc,
            [field]: `Sample ${field} ${i + 1}`
          }), {})
        );
    }
  };

  const handleGenerateReport = async () => {
    if (!filters.reportType) {
      toast.error('Please select a report type');
      return;
    }

    if (!filters.startDate || !filters.endDate) {
      toast.error('Please select date range');
      return;
    }

    if (new Date(filters.startDate) > new Date(filters.endDate)) {
      toast.error('Start date cannot be after end date');
      return;
    }

    try {
      setIsGenerating(true);

      // Find the selected report type
      const selectedReport = reportTypes.find(report => report.id === filters.reportType);
      if (!selectedReport) {
        throw new Error('Report type not found');
      }

      // Simulate report generation delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate sample data
      const data = generateSampleData(selectedReport, filters.startDate, filters.endDate);

      // Create file content based on format
      let fileContent = '';
      let fileName = '';
      let mimeType = '';

      if (filters.format === 'csv') {
        // CSV format
        const headers = selectedReport.fields.join(',');
        const rows = data.map(row =>
          selectedReport.fields.map(field => `"${row[field] || ''}"`).join(',')
        ).join('\n');
        fileContent = headers + '\n' + rows;
        fileName = `${selectedReport.id}-${filters.startDate}-to-${filters.endDate}.csv`;
        mimeType = 'text/csv;charset=utf-8;';
      } else if (filters.format === 'excel') {
        // For Excel, we'll create a CSV with .xlsx extension (simplified)
        const headers = selectedReport.fields.join('\t');
        const rows = data.map(row =>
          selectedReport.fields.map(field => row[field] || '').join('\t')
        ).join('\n');
        fileContent = headers + '\n' + rows;
        fileName = `${selectedReport.id}-${filters.startDate}-to-${filters.endDate}.xlsx`;
        mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;';
      } else if (filters.format === 'pdf') {
        // For PDF, we'll create a simple text format (simplified)
        fileContent = `${selectedReport.name}\n`;
        fileContent += `Generated on: ${new Date().toLocaleDateString()}\n`;
        fileContent += `Date Range: ${filters.startDate} to ${filters.endDate}\n\n`;
        fileContent += selectedReport.fields.join('\t') + '\n';
        fileContent += data.map(row =>
          selectedReport.fields.map(field => row[field] || '').join('\t')
        ).join('\n');
        fileName = `${selectedReport.id}-${filters.startDate}-to-${filters.endDate}.pdf`;
        mimeType = 'application/pdf;';
      }

      // Create and download file
      const blob = new Blob([fileContent], { type: mimeType });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', fileName);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success(`${selectedReport.name} generated successfully! (${data.length} records)`);
    } catch (error) {
      toast.error('Failed to generate report');
      console.error('Report generation error:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const getQuickDateRange = (days: number) => {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    setFilters(prev => ({
      ...prev,
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    }));
  };

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Reports & Analytics</h1>
          <p className="text-muted mb-0">Generate comprehensive reports for your business insights</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-info"
            onClick={() => getQuickDateRange(7)}
          >
            Last 7 Days
          </button>
          <button
            className="btn btn-outline-info"
            onClick={() => getQuickDateRange(30)}
          >
            Last 30 Days
          </button>
          <button
            className="btn btn-outline-info"
            onClick={() => getQuickDateRange(90)}
          >
            Last 90 Days
          </button>
        </div>
      </div>

      {/* Report Generation Panel */}
      <div className="card mb-4">
        <div className="card-header">
          <h5 className="mb-0">📊 Generate Report</h5>
        </div>
        <div className="card-body">
          <div className="row g-3">
            <div className="col-md-3">
              <label className="form-label">Start Date</label>
              <input
                type="date"
                className="form-control"
                name="startDate"
                value={filters.startDate}
                onChange={handleFilterChange}
              />
            </div>
            <div className="col-md-3">
              <label className="form-label">End Date</label>
              <input
                type="date"
                className="form-control"
                name="endDate"
                value={filters.endDate}
                onChange={handleFilterChange}
              />
            </div>
            <div className="col-md-3">
              <label className="form-label">Report Format</label>
              <select
                className="form-select"
                name="format"
                value={filters.format}
                onChange={handleFilterChange}
              >
                <option value="csv">CSV (.csv)</option>
                <option value="excel">Excel (.xlsx)</option>
                <option value="pdf">PDF (.pdf)</option>
              </select>
            </div>
            <div className="col-md-3 d-flex align-items-end">
              <button
                className="btn btn-success w-100"
                onClick={handleGenerateReport}
                disabled={isGenerating || !filters.reportType}
              >
                {isGenerating ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    Generating...
                  </>
                ) : (
                  <>📥 Generate Report</>
                )}
              </button>
            </div>
          </div>

          {filters.reportType && (
            <div className="mt-3">
              <div className="alert alert-info">
                <strong>Selected Report:</strong> {reportTypes.find(r => r.id === filters.reportType)?.name}
                <br />
                <small>{reportTypes.find(r => r.id === filters.reportType)?.description}</small>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Category Filter */}
      <div className="mb-4">
        <div className="d-flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category.id}
              className={`btn ${selectedCategory === category.id ? 'btn-primary' : 'btn-outline-primary'}`}
              onClick={() => setSelectedCategory(category.id)}
            >
              {category.icon} {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Reports Grid */}
      <div className="row">
        {filteredReports.map((report) => (
          <div key={report.id} className="col-md-6 col-lg-4 mb-4">
            <div className={`card h-100 ${filters.reportType === report.id ? 'border-primary' : ''}`}>
              <div className="card-body">
                <div className="d-flex align-items-start mb-3">
                  <div className="me-3" style={{ fontSize: '2rem' }}>
                    {report.icon}
                  </div>
                  <div className="flex-grow-1">
                    <h5 className="card-title mb-2">{report.name}</h5>
                    <p className="card-text text-muted small">{report.description}</p>
                  </div>
                </div>

                <div className="mb-3">
                  <small className="text-muted">Includes:</small>
                  <div className="mt-1">
                    {report.fields.slice(0, 3).map((field, index) => (
                      <span key={index} className="badge bg-light text-dark me-1 mb-1">
                        {field}
                      </span>
                    ))}
                    {report.fields.length > 3 && (
                      <span className="badge bg-secondary">
                        +{report.fields.length - 3} more
                      </span>
                    )}
                  </div>
                </div>

                <button
                  className={`btn w-100 ${filters.reportType === report.id ? 'btn-primary' : 'btn-outline-primary'}`}
                  onClick={() => setFilters(prev => ({ ...prev, reportType: report.id }))}
                >
                  {filters.reportType === report.id ? '✓ Selected' : 'Select Report'}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Report Statistics */}
      <div className="row mt-4">
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <h5 className="card-title text-primary">{reportTypes.length}</h5>
              <p className="card-text">Total Reports Available</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <h5 className="card-title text-success">{reportTypes.filter(r => r.category === 'sales').length}</h5>
              <p className="card-text">Sales Reports</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <h5 className="card-title text-info">{reportTypes.filter(r => r.category === 'inventory').length}</h5>
              <p className="card-text">Inventory Reports</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <h5 className="card-title text-warning">{reportTypes.filter(r => r.category === 'financial').length}</h5>
              <p className="card-text">Financial Reports</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reports;
