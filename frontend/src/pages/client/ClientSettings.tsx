import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface CompanySettings {
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  website: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  logo: string;
  taxId: string;
}

interface SystemSettings {
  currency: string;
  timezone: string;
  dateFormat: string;
  language: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    lowStock: boolean;
    newOrders: boolean;
    paymentReceived: boolean;
  };
}

interface InventorySettings {
  defaultUnit: string;
  lowStockThreshold: number;
  autoReorderEnabled: boolean;
  trackSerialNumbers: boolean;
  enableBarcodeScanning: boolean;
  stockValuationMethod: 'FIFO' | 'LIFO' | 'AVERAGE';
  allowNegativeStock: boolean;
}

interface PaymentSettings {
  defaultPaymentMethod: string;
  acceptedPaymentMethods: string[];
  paymentTerms: string;
  lateFeePercentage: number;
  discountSettings: {
    earlyPaymentDiscount: number;
    bulkOrderDiscount: number;
    loyalCustomerDiscount: number;
  };
}

interface SecuritySettings {
  twoFactorAuth: boolean;
  sessionTimeout: number;
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
  };
  ipWhitelist: string[];
  auditLogging: boolean;
}

const ClientSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('company');
  const [companySettings, setCompanySettings] = useState<CompanySettings>({
    companyName: 'Acme Corporation',
    contactName: 'John Smith',
    email: '<EMAIL>',
    phone: '+****************',
    website: 'https://acmecorp.com',
    address: {
      street: '123 Business Ave',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'United States'
    },
    logo: '',
    taxId: '12-3456789'
  });

  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    currency: 'USD',
    timezone: 'America/New_York',
    dateFormat: 'MM/DD/YYYY',
    language: 'en',
    theme: 'light',
    notifications: {
      email: true,
      sms: false,
      push: true,
      lowStock: true,
      newOrders: true,
      paymentReceived: true
    }
  });

  const [inventorySettings, setInventorySettings] = useState<InventorySettings>({
    defaultUnit: 'pcs',
    lowStockThreshold: 10,
    autoReorderEnabled: false,
    trackSerialNumbers: false,
    enableBarcodeScanning: true,
    stockValuationMethod: 'FIFO',
    allowNegativeStock: false
  });

  const [paymentSettings, setPaymentSettings] = useState<PaymentSettings>({
    defaultPaymentMethod: 'Credit Card',
    acceptedPaymentMethods: ['Credit Card', 'PayPal', 'Bank Transfer', 'Cash'],
    paymentTerms: 'Net 30',
    lateFeePercentage: 1.5,
    discountSettings: {
      earlyPaymentDiscount: 2.0,
      bulkOrderDiscount: 5.0,
      loyalCustomerDiscount: 10.0
    }
  });

  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    twoFactorAuth: false,
    sessionTimeout: 30,
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireNumbers: true,
      requireSpecialChars: false
    },
    ipWhitelist: [],
    auditLogging: true
  });

  const [hasChanges, setHasChanges] = useState(false);

  const handleSaveSettings = () => {
    // Simulate API call
    toast.promise(
      new Promise((resolve) => {
        setTimeout(() => {
          resolve('Settings saved successfully');
          setHasChanges(false);
        }, 1500);
      }),
      {
        loading: 'Saving settings...',
        success: 'Settings saved successfully!',
        error: 'Failed to save settings'
      }
    );
  };

  const handleResetSettings = () => {
    if (window.confirm('Are you sure you want to reset all settings to default values?')) {
      toast.success('Settings reset to defaults');
      setHasChanges(true);
    }
  };

  const tabs = [
    { id: 'company', label: '🏢 Company', icon: '🏢' },
    { id: 'system', label: '⚙️ System', icon: '⚙️' },
    { id: 'inventory', label: '📦 Inventory', icon: '📦' },
    { id: 'payment', label: '💳 Payment', icon: '💳' },
    { id: 'security', label: '🔒 Security', icon: '🔒' },
    { id: 'integrations', label: '🔗 Integrations', icon: '🔗' },
    { id: 'backup', label: '💾 Backup', icon: '💾' }
  ];

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">System Settings</h1>
          <p className="text-muted mb-0">Configure your system preferences and business settings</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-secondary"
            onClick={handleResetSettings}
          >
            🔄 Reset to Defaults
          </button>
          <button
            className={`btn ${hasChanges ? 'btn-warning' : 'btn-success'}`}
            onClick={handleSaveSettings}
            disabled={!hasChanges}
          >
            💾 {hasChanges ? 'Save Changes' : 'All Saved'}
          </button>
        </div>
      </div>

      <div className="row">
        {/* Settings Navigation */}
        <div className="col-md-3">
          <div className="card">
            <div className="card-header">
              <h6 className="mb-0">Settings Categories</h6>
            </div>
            <div className="list-group list-group-flush">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  className={`list-group-item list-group-item-action ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  <span className="me-2">{tab.icon}</span>
                  {tab.label.replace(/^[^\s]+ /, '')}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Settings Content */}
        <div className="col-md-9">
          {/* Company Settings */}
          {activeTab === 'company' && (
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">🏢 Company Information</h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Company Name</label>
                    <input
                      type="text"
                      className="form-control"
                      value={companySettings.companyName}
                      onChange={(e) => {
                        setCompanySettings(prev => ({ ...prev, companyName: e.target.value }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Contact Name</label>
                    <input
                      type="text"
                      className="form-control"
                      value={companySettings.contactName}
                      onChange={(e) => {
                        setCompanySettings(prev => ({ ...prev, contactName: e.target.value }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Email</label>
                    <input
                      type="email"
                      className="form-control"
                      value={companySettings.email}
                      onChange={(e) => {
                        setCompanySettings(prev => ({ ...prev, email: e.target.value }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Phone</label>
                    <input
                      type="tel"
                      className="form-control"
                      value={companySettings.phone}
                      onChange={(e) => {
                        setCompanySettings(prev => ({ ...prev, phone: e.target.value }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Website</label>
                    <input
                      type="url"
                      className="form-control"
                      value={companySettings.website}
                      onChange={(e) => {
                        setCompanySettings(prev => ({ ...prev, website: e.target.value }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Tax ID</label>
                    <input
                      type="text"
                      className="form-control"
                      value={companySettings.taxId}
                      onChange={(e) => {
                        setCompanySettings(prev => ({ ...prev, taxId: e.target.value }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                </div>

                <h6 className="mt-4 mb-3">Company Address</h6>
                <div className="row">
                  <div className="col-md-12 mb-3">
                    <label className="form-label">Street Address</label>
                    <input
                      type="text"
                      className="form-control"
                      value={companySettings.address.street}
                      onChange={(e) => {
                        setCompanySettings(prev => ({
                          ...prev,
                          address: { ...prev.address, street: e.target.value }
                        }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">City</label>
                    <input
                      type="text"
                      className="form-control"
                      value={companySettings.address.city}
                      onChange={(e) => {
                        setCompanySettings(prev => ({
                          ...prev,
                          address: { ...prev.address, city: e.target.value }
                        }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-3 mb-3">
                    <label className="form-label">State</label>
                    <input
                      type="text"
                      className="form-control"
                      value={companySettings.address.state}
                      onChange={(e) => {
                        setCompanySettings(prev => ({
                          ...prev,
                          address: { ...prev.address, state: e.target.value }
                        }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-3 mb-3">
                    <label className="form-label">ZIP Code</label>
                    <input
                      type="text"
                      className="form-control"
                      value={companySettings.address.zipCode}
                      onChange={(e) => {
                        setCompanySettings(prev => ({
                          ...prev,
                          address: { ...prev.address, zipCode: e.target.value }
                        }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Country</label>
                    <select
                      className="form-select"
                      value={companySettings.address.country}
                      onChange={(e) => {
                        setCompanySettings(prev => ({
                          ...prev,
                          address: { ...prev.address, country: e.target.value }
                        }));
                        setHasChanges(true);
                      }}
                    >
                      <option value="United States">United States</option>
                      <option value="Canada">Canada</option>
                      <option value="United Kingdom">United Kingdom</option>
                      <option value="Australia">Australia</option>
                    </select>
                  </div>
                </div>

                <h6 className="mt-4 mb-3">Company Logo</h6>
                <div className="mb-3">
                  <input
                    type="file"
                    className="form-control"
                    accept="image/*"
                    onChange={() => {
                      toast.success('Logo upload functionality coming soon!');
                      setHasChanges(true);
                    }}
                  />
                  <small className="text-muted">Upload your company logo (PNG, JPG, max 2MB)</small>
                </div>
              </div>
            </div>
          )}

          {/* System Settings */}
          {activeTab === 'system' && (
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">⚙️ System Configuration</h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Default Currency</label>
                    <select
                      className="form-select"
                      value={systemSettings.currency}
                      onChange={(e) => {
                        setSystemSettings(prev => ({ ...prev, currency: e.target.value }));
                        setHasChanges(true);
                      }}
                    >
                      <option value="USD">USD - US Dollar</option>
                      <option value="EUR">EUR - Euro</option>
                      <option value="GBP">GBP - British Pound</option>
                      <option value="CAD">CAD - Canadian Dollar</option>
                      <option value="AUD">AUD - Australian Dollar</option>
                    </select>
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Timezone</label>
                    <select
                      className="form-select"
                      value={systemSettings.timezone}
                      onChange={(e) => {
                        setSystemSettings(prev => ({ ...prev, timezone: e.target.value }));
                        setHasChanges(true);
                      }}
                    >
                      <option value="America/New_York">Eastern Time (ET)</option>
                      <option value="America/Chicago">Central Time (CT)</option>
                      <option value="America/Denver">Mountain Time (MT)</option>
                      <option value="America/Los_Angeles">Pacific Time (PT)</option>
                      <option value="UTC">UTC</option>
                    </select>
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Date Format</label>
                    <select
                      className="form-select"
                      value={systemSettings.dateFormat}
                      onChange={(e) => {
                        setSystemSettings(prev => ({ ...prev, dateFormat: e.target.value }));
                        setHasChanges(true);
                      }}
                    >
                      <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                      <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                      <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                    </select>
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Language</label>
                    <select
                      className="form-select"
                      value={systemSettings.language}
                      onChange={(e) => {
                        setSystemSettings(prev => ({ ...prev, language: e.target.value }));
                        setHasChanges(true);
                      }}
                    >
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                      <option value="de">German</option>
                    </select>
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Theme</label>
                    <select
                      className="form-select"
                      value={systemSettings.theme}
                      onChange={(e) => {
                        setSystemSettings(prev => ({ ...prev, theme: e.target.value as 'light' | 'dark' | 'auto' }));
                        setHasChanges(true);
                      }}
                    >
                      <option value="light">Light</option>
                      <option value="dark">Dark</option>
                      <option value="auto">Auto (System)</option>
                    </select>
                  </div>
                </div>

                <h6 className="mt-4 mb-3">Notification Preferences</h6>
                <div className="row">
                  <div className="col-md-4">
                    <div className="form-check mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={systemSettings.notifications.email}
                        onChange={(e) => {
                          setSystemSettings(prev => ({
                            ...prev,
                            notifications: { ...prev.notifications, email: e.target.checked }
                          }));
                          setHasChanges(true);
                        }}
                      />
                      <label className="form-check-label">
                        📧 Email Notifications
                      </label>
                    </div>
                    <div className="form-check mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={systemSettings.notifications.sms}
                        onChange={(e) => {
                          setSystemSettings(prev => ({
                            ...prev,
                            notifications: { ...prev.notifications, sms: e.target.checked }
                          }));
                          setHasChanges(true);
                        }}
                      />
                      <label className="form-check-label">
                        📱 SMS Notifications
                      </label>
                    </div>
                    <div className="form-check mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={systemSettings.notifications.push}
                        onChange={(e) => {
                          setSystemSettings(prev => ({
                            ...prev,
                            notifications: { ...prev.notifications, push: e.target.checked }
                          }));
                          setHasChanges(true);
                        }}
                      />
                      <label className="form-check-label">
                        🔔 Push Notifications
                      </label>
                    </div>
                  </div>
                  <div className="col-md-8">
                    <div className="form-check mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={systemSettings.notifications.lowStock}
                        onChange={(e) => {
                          setSystemSettings(prev => ({
                            ...prev,
                            notifications: { ...prev.notifications, lowStock: e.target.checked }
                          }));
                          setHasChanges(true);
                        }}
                      />
                      <label className="form-check-label">
                        ⚠️ Low Stock Alerts
                      </label>
                    </div>
                    <div className="form-check mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={systemSettings.notifications.newOrders}
                        onChange={(e) => {
                          setSystemSettings(prev => ({
                            ...prev,
                            notifications: { ...prev.notifications, newOrders: e.target.checked }
                          }));
                          setHasChanges(true);
                        }}
                      />
                      <label className="form-check-label">
                        📋 New Order Notifications
                      </label>
                    </div>
                    <div className="form-check mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={systemSettings.notifications.paymentReceived}
                        onChange={(e) => {
                          setSystemSettings(prev => ({
                            ...prev,
                            notifications: { ...prev.notifications, paymentReceived: e.target.checked }
                          }));
                          setHasChanges(true);
                        }}
                      />
                      <label className="form-check-label">
                        💰 Payment Received Notifications
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Other Settings Tabs */}
          {(activeTab === 'inventory' || activeTab === 'payment' || activeTab === 'security' || activeTab === 'integrations' || activeTab === 'backup') && (
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">
                  {activeTab === 'inventory' && '📦 Inventory Settings'}
                  {activeTab === 'payment' && '💳 Payment Settings'}
                  {activeTab === 'security' && '🔒 Security Settings'}
                  {activeTab === 'integrations' && '🔗 Integrations'}
                  {activeTab === 'backup' && '💾 Backup & Recovery'}
                </h5>
              </div>
              <div className="card-body">
                <div className="text-center py-5">
                  <div className="mb-3">
                    <span className="display-1">
                      {activeTab === 'inventory' && '📦'}
                      {activeTab === 'payment' && '💳'}
                      {activeTab === 'security' && '🔒'}
                      {activeTab === 'integrations' && '🔗'}
                      {activeTab === 'backup' && '💾'}
                    </span>
                  </div>
                  <h5>
                    {activeTab === 'inventory' && 'Inventory Configuration'}
                    {activeTab === 'payment' && 'Payment Gateway Setup'}
                    {activeTab === 'security' && 'Security & Access Control'}
                    {activeTab === 'integrations' && 'Third-party Integrations'}
                    {activeTab === 'backup' && 'Data Backup & Recovery'}
                  </h5>
                  <p className="text-muted">
                    {activeTab === 'inventory' && 'Configure inventory management settings, stock thresholds, and valuation methods'}
                    {activeTab === 'payment' && 'Set up payment gateways, configure payment methods, and manage transaction settings'}
                    {activeTab === 'security' && 'Manage user access, authentication settings, and security policies'}
                    {activeTab === 'integrations' && 'Connect with WooCommerce, Shopify, Magento, and other platforms'}
                    {activeTab === 'backup' && 'Configure automated backups, data retention, and recovery options'}
                  </p>
                  <button
                    className="btn btn-primary"
                    onClick={() => toast.success(`${activeTab} settings will be implemented in the next phase`)}
                  >
                    Configure {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Settings
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClientSettings;
