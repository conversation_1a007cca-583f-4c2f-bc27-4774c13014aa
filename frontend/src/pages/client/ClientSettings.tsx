import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface CompanySettings {
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  website: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  logo: string;
  taxId: string;
}

interface SystemSettings {
  currency: string;
  timezone: string;
  dateFormat: string;
  language: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    lowStock: boolean;
    newOrders: boolean;
    paymentReceived: boolean;
  };
}

interface InventorySettings {
  defaultUnit: string;
  lowStockThreshold: number;
  autoReorderEnabled: boolean;
  trackSerialNumbers: boolean;
  enableBarcodeScanning: boolean;
  stockValuationMethod: 'FIFO' | 'LIFO' | 'AVERAGE';
  allowNegativeStock: boolean;
}

interface PaymentSettings {
  defaultPaymentMethod: string;
  acceptedPaymentMethods: string[];
  paymentTerms: string;
  lateFeePercentage: number;
  discountSettings: {
    earlyPaymentDiscount: number;
    bulkOrderDiscount: number;
    loyalCustomerDiscount: number;
  };
}

interface SecuritySettings {
  twoFactorAuth: boolean;
  sessionTimeout: number;
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
  };
  ipWhitelist: string[];
  auditLogging: boolean;
}

const ClientSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('company');
  const [companySettings, setCompanySettings] = useState<CompanySettings>({
    companyName: 'Acme Corporation',
    contactName: 'John Smith',
    email: '<EMAIL>',
    phone: '+****************',
    website: 'https://acmecorp.com',
    address: {
      street: '123 Business Ave',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'United States'
    },
    logo: '',
    taxId: '12-3456789'
  });

  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    currency: 'USD',
    timezone: 'America/New_York',
    dateFormat: 'MM/DD/YYYY',
    language: 'en',
    theme: 'light',
    notifications: {
      email: true,
      sms: false,
      push: true,
      lowStock: true,
      newOrders: true,
      paymentReceived: true
    }
  });

  const [inventorySettings, setInventorySettings] = useState<InventorySettings>({
    defaultUnit: 'pcs',
    lowStockThreshold: 10,
    autoReorderEnabled: false,
    trackSerialNumbers: false,
    enableBarcodeScanning: true,
    stockValuationMethod: 'FIFO',
    allowNegativeStock: false
  });

  const [paymentSettings, setPaymentSettings] = useState<PaymentSettings>({
    defaultPaymentMethod: 'Credit Card',
    acceptedPaymentMethods: ['Credit Card', 'PayPal', 'Bank Transfer', 'Cash'],
    paymentTerms: 'Net 30',
    lateFeePercentage: 1.5,
    discountSettings: {
      earlyPaymentDiscount: 2.0,
      bulkOrderDiscount: 5.0,
      loyalCustomerDiscount: 10.0
    }
  });

  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    twoFactorAuth: false,
    sessionTimeout: 30,
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireNumbers: true,
      requireSpecialChars: false
    },
    ipWhitelist: [],
    auditLogging: true
  });

  const [hasChanges, setHasChanges] = useState(false);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [backupSettings, setBackupSettings] = useState({
    autoBackup: true,
    backupFrequency: 'daily',
    retentionDays: 30,
    cloudStorage: true,
    encryptBackups: true
  });
  const [integrationSettings, setIntegrationSettings] = useState({
    woocommerce: { enabled: false, apiKey: '', apiSecret: '', storeUrl: '' },
    shopify: { enabled: false, apiKey: '', apiSecret: '', storeUrl: '' },
    magento: { enabled: false, apiKey: '', apiSecret: '', storeUrl: '' },
    stripe: { enabled: false, publishableKey: '', secretKey: '' },
    paypal: { enabled: false, clientId: '', clientSecret: '' }
  });
  const [apiSettings, setApiSettings] = useState({
    enableApi: true,
    apiVersion: 'v1',
    rateLimiting: true,
    maxRequestsPerMinute: 100,
    requireAuthentication: true,
    allowedOrigins: ['*']
  });

  const handleSaveSettings = () => {
    // Simulate API call
    toast.promise(
      new Promise((resolve) => {
        setTimeout(() => {
          resolve('Settings saved successfully');
          setHasChanges(false);
        }, 1500);
      }),
      {
        loading: 'Saving settings...',
        success: 'Settings saved successfully!',
        error: 'Failed to save settings'
      }
    );
  };

  const handleResetSettings = () => {
    if (window.confirm('Are you sure you want to reset all settings to default values?')) {
      toast.success('Settings reset to defaults');
      setHasChanges(true);
    }
  };

  const handleTestIntegration = (platform: string) => {
    toast.promise(
      new Promise((resolve, reject) => {
        setTimeout(() => {
          if (Math.random() > 0.3) {
            resolve(`${platform} integration test successful`);
          } else {
            reject(new Error(`${platform} integration test failed`));
          }
        }, 2000);
      }),
      {
        loading: `Testing ${platform} integration...`,
        success: (message) => `${message}`,
        error: (error) => `${error.message}`
      }
    );
  };

  const handleBackupNow = () => {
    toast.promise(
      new Promise((resolve) => {
        setTimeout(() => {
          resolve('Backup completed successfully');
        }, 3000);
      }),
      {
        loading: 'Creating backup...',
        success: 'Backup completed successfully!',
        error: 'Backup failed'
      }
    );
  };

  const handleRestoreBackup = () => {
    if (window.confirm('Are you sure you want to restore from backup? This will overwrite current data.')) {
      toast.promise(
        new Promise((resolve) => {
          setTimeout(() => {
            resolve('Data restored successfully');
          }, 4000);
        }),
        {
          loading: 'Restoring from backup...',
          success: 'Data restored successfully!',
          error: 'Restore failed'
        }
      );
    }
  };

  const handleExportSettings = () => {
    const settingsData = {
      company: companySettings,
      system: systemSettings,
      inventory: inventorySettings,
      payment: paymentSettings,
      security: securitySettings,
      backup: backupSettings,
      integrations: integrationSettings,
      api: apiSettings,
      exportedAt: new Date().toISOString()
    };

    const dataStr = JSON.stringify(settingsData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `settings-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast.success('Settings exported successfully');
  };

  const handleImportSettings = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const settings = JSON.parse(e.target?.result as string);
            // Apply imported settings
            if (settings.company) setCompanySettings(settings.company);
            if (settings.system) setSystemSettings(settings.system);
            if (settings.inventory) setInventorySettings(settings.inventory);
            if (settings.payment) setPaymentSettings(settings.payment);
            if (settings.security) setSecuritySettings(settings.security);
            if (settings.backup) setBackupSettings(settings.backup);
            if (settings.integrations) setIntegrationSettings(settings.integrations);
            if (settings.api) setApiSettings(settings.api);

            setHasChanges(true);
            toast.success('Settings imported successfully');
          } catch (error) {
            toast.error('Invalid settings file');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const tabs = [
    { id: 'company', label: '🏢 Company', icon: '🏢' },
    { id: 'system', label: '⚙️ System', icon: '⚙️' },
    { id: 'inventory', label: '📦 Inventory', icon: '📦' },
    { id: 'payment', label: '💳 Payment', icon: '💳' },
    { id: 'security', label: '🔒 Security', icon: '🔒' },
    { id: 'integrations', label: '🔗 Integrations', icon: '🔗' },
    { id: 'backup', label: '💾 Backup', icon: '💾' }
  ];

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">System Settings</h1>
          <p className="text-muted mb-0">Configure your system preferences and business settings</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-info"
            onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
          >
            🔧 Advanced
          </button>
          <button
            className="btn btn-outline-primary"
            onClick={handleExportSettings}
          >
            📤 Export
          </button>
          <button
            className="btn btn-outline-success"
            onClick={handleImportSettings}
          >
            📥 Import
          </button>
          <button
            className="btn btn-outline-secondary"
            onClick={handleResetSettings}
          >
            🔄 Reset
          </button>
          <button
            className={`btn ${hasChanges ? 'btn-warning' : 'btn-success'}`}
            onClick={handleSaveSettings}
            disabled={!hasChanges}
          >
            💾 {hasChanges ? 'Save Changes' : 'All Saved'}
          </button>
        </div>
      </div>

      {/* Advanced Settings Panel */}
      {showAdvancedSettings && (
        <div className="card mb-4 border-warning">
          <div className="card-header bg-warning bg-opacity-10">
            <div className="d-flex justify-content-between align-items-center">
              <h6 className="mb-0">🔧 Advanced System Management</h6>
              <button
                className="btn-close"
                onClick={() => setShowAdvancedSettings(false)}
              ></button>
            </div>
          </div>
          <div className="card-body">
            <div className="row">
              <div className="col-md-3">
                <button
                  className="btn btn-outline-primary w-100 mb-2"
                  onClick={handleBackupNow}
                >
                  💾 Backup Now
                </button>
                <button
                  className="btn btn-outline-warning w-100 mb-2"
                  onClick={handleRestoreBackup}
                >
                  🔄 Restore Backup
                </button>
              </div>
              <div className="col-md-3">
                <button
                  className="btn btn-outline-info w-100 mb-2"
                  onClick={() => toast.success('System diagnostics completed')}
                >
                  🔍 System Diagnostics
                </button>
                <button
                  className="btn btn-outline-success w-100 mb-2"
                  onClick={() => toast.success('Cache cleared successfully')}
                >
                  🗑️ Clear Cache
                </button>
              </div>
              <div className="col-md-3">
                <button
                  className="btn btn-outline-secondary w-100 mb-2"
                  onClick={() => toast.success('Logs exported successfully')}
                >
                  📋 Export Logs
                </button>
                <button
                  className="btn btn-outline-danger w-100 mb-2"
                  onClick={() => toast.success('Database optimized')}
                >
                  ⚡ Optimize Database
                </button>
              </div>
              <div className="col-md-3">
                <div className="text-center">
                  <small className="text-muted">System Status</small>
                  <div className="mt-1">
                    <span className="badge bg-success">🟢 Online</span>
                  </div>
                  <div className="mt-1">
                    <small>Uptime: 99.9%</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="row">
        {/* Settings Navigation */}
        <div className="col-md-3">
          <div className="card">
            <div className="card-header">
              <h6 className="mb-0">Settings Categories</h6>
            </div>
            <div className="list-group list-group-flush">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  className={`list-group-item list-group-item-action ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  <span className="me-2">{tab.icon}</span>
                  {tab.label.replace(/^[^\s]+ /, '')}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Settings Content */}
        <div className="col-md-9">
          {/* Company Settings */}
          {activeTab === 'company' && (
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">🏢 Company Information</h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Company Name</label>
                    <input
                      type="text"
                      className="form-control"
                      value={companySettings.companyName}
                      onChange={(e) => {
                        setCompanySettings(prev => ({ ...prev, companyName: e.target.value }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Contact Name</label>
                    <input
                      type="text"
                      className="form-control"
                      value={companySettings.contactName}
                      onChange={(e) => {
                        setCompanySettings(prev => ({ ...prev, contactName: e.target.value }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Email</label>
                    <input
                      type="email"
                      className="form-control"
                      value={companySettings.email}
                      onChange={(e) => {
                        setCompanySettings(prev => ({ ...prev, email: e.target.value }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Phone</label>
                    <input
                      type="tel"
                      className="form-control"
                      value={companySettings.phone}
                      onChange={(e) => {
                        setCompanySettings(prev => ({ ...prev, phone: e.target.value }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Website</label>
                    <input
                      type="url"
                      className="form-control"
                      value={companySettings.website}
                      onChange={(e) => {
                        setCompanySettings(prev => ({ ...prev, website: e.target.value }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Tax ID</label>
                    <input
                      type="text"
                      className="form-control"
                      value={companySettings.taxId}
                      onChange={(e) => {
                        setCompanySettings(prev => ({ ...prev, taxId: e.target.value }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                </div>

                <h6 className="mt-4 mb-3">Company Address</h6>
                <div className="row">
                  <div className="col-md-12 mb-3">
                    <label className="form-label">Street Address</label>
                    <input
                      type="text"
                      className="form-control"
                      value={companySettings.address.street}
                      onChange={(e) => {
                        setCompanySettings(prev => ({
                          ...prev,
                          address: { ...prev.address, street: e.target.value }
                        }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">City</label>
                    <input
                      type="text"
                      className="form-control"
                      value={companySettings.address.city}
                      onChange={(e) => {
                        setCompanySettings(prev => ({
                          ...prev,
                          address: { ...prev.address, city: e.target.value }
                        }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-3 mb-3">
                    <label className="form-label">State</label>
                    <input
                      type="text"
                      className="form-control"
                      value={companySettings.address.state}
                      onChange={(e) => {
                        setCompanySettings(prev => ({
                          ...prev,
                          address: { ...prev.address, state: e.target.value }
                        }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-3 mb-3">
                    <label className="form-label">ZIP Code</label>
                    <input
                      type="text"
                      className="form-control"
                      value={companySettings.address.zipCode}
                      onChange={(e) => {
                        setCompanySettings(prev => ({
                          ...prev,
                          address: { ...prev.address, zipCode: e.target.value }
                        }));
                        setHasChanges(true);
                      }}
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Country</label>
                    <select
                      className="form-select"
                      value={companySettings.address.country}
                      onChange={(e) => {
                        setCompanySettings(prev => ({
                          ...prev,
                          address: { ...prev.address, country: e.target.value }
                        }));
                        setHasChanges(true);
                      }}
                    >
                      <option value="United States">United States</option>
                      <option value="Canada">Canada</option>
                      <option value="United Kingdom">United Kingdom</option>
                      <option value="Australia">Australia</option>
                    </select>
                  </div>
                </div>

                <h6 className="mt-4 mb-3">Company Logo</h6>
                <div className="mb-3">
                  <input
                    type="file"
                    className="form-control"
                    accept="image/*"
                    onChange={() => {
                      toast.success('Logo upload functionality coming soon!');
                      setHasChanges(true);
                    }}
                  />
                  <small className="text-muted">Upload your company logo (PNG, JPG, max 2MB)</small>
                </div>
              </div>
            </div>
          )}

          {/* System Settings */}
          {activeTab === 'system' && (
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">⚙️ System Configuration</h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Default Currency</label>
                    <select
                      className="form-select"
                      value={systemSettings.currency}
                      onChange={(e) => {
                        setSystemSettings(prev => ({ ...prev, currency: e.target.value }));
                        setHasChanges(true);
                      }}
                    >
                      <option value="USD">USD - US Dollar</option>
                      <option value="EUR">EUR - Euro</option>
                      <option value="GBP">GBP - British Pound</option>
                      <option value="CAD">CAD - Canadian Dollar</option>
                      <option value="AUD">AUD - Australian Dollar</option>
                    </select>
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Timezone</label>
                    <select
                      className="form-select"
                      value={systemSettings.timezone}
                      onChange={(e) => {
                        setSystemSettings(prev => ({ ...prev, timezone: e.target.value }));
                        setHasChanges(true);
                      }}
                    >
                      <option value="America/New_York">Eastern Time (ET)</option>
                      <option value="America/Chicago">Central Time (CT)</option>
                      <option value="America/Denver">Mountain Time (MT)</option>
                      <option value="America/Los_Angeles">Pacific Time (PT)</option>
                      <option value="UTC">UTC</option>
                    </select>
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Date Format</label>
                    <select
                      className="form-select"
                      value={systemSettings.dateFormat}
                      onChange={(e) => {
                        setSystemSettings(prev => ({ ...prev, dateFormat: e.target.value }));
                        setHasChanges(true);
                      }}
                    >
                      <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                      <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                      <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                    </select>
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Language</label>
                    <select
                      className="form-select"
                      value={systemSettings.language}
                      onChange={(e) => {
                        setSystemSettings(prev => ({ ...prev, language: e.target.value }));
                        setHasChanges(true);
                      }}
                    >
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                      <option value="de">German</option>
                    </select>
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Theme</label>
                    <select
                      className="form-select"
                      value={systemSettings.theme}
                      onChange={(e) => {
                        setSystemSettings(prev => ({ ...prev, theme: e.target.value as 'light' | 'dark' | 'auto' }));
                        setHasChanges(true);
                      }}
                    >
                      <option value="light">Light</option>
                      <option value="dark">Dark</option>
                      <option value="auto">Auto (System)</option>
                    </select>
                  </div>
                </div>

                <h6 className="mt-4 mb-3">Notification Preferences</h6>
                <div className="row">
                  <div className="col-md-4">
                    <div className="form-check mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={systemSettings.notifications.email}
                        onChange={(e) => {
                          setSystemSettings(prev => ({
                            ...prev,
                            notifications: { ...prev.notifications, email: e.target.checked }
                          }));
                          setHasChanges(true);
                        }}
                      />
                      <label className="form-check-label">
                        📧 Email Notifications
                      </label>
                    </div>
                    <div className="form-check mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={systemSettings.notifications.sms}
                        onChange={(e) => {
                          setSystemSettings(prev => ({
                            ...prev,
                            notifications: { ...prev.notifications, sms: e.target.checked }
                          }));
                          setHasChanges(true);
                        }}
                      />
                      <label className="form-check-label">
                        📱 SMS Notifications
                      </label>
                    </div>
                    <div className="form-check mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={systemSettings.notifications.push}
                        onChange={(e) => {
                          setSystemSettings(prev => ({
                            ...prev,
                            notifications: { ...prev.notifications, push: e.target.checked }
                          }));
                          setHasChanges(true);
                        }}
                      />
                      <label className="form-check-label">
                        🔔 Push Notifications
                      </label>
                    </div>
                  </div>
                  <div className="col-md-8">
                    <div className="form-check mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={systemSettings.notifications.lowStock}
                        onChange={(e) => {
                          setSystemSettings(prev => ({
                            ...prev,
                            notifications: { ...prev.notifications, lowStock: e.target.checked }
                          }));
                          setHasChanges(true);
                        }}
                      />
                      <label className="form-check-label">
                        ⚠️ Low Stock Alerts
                      </label>
                    </div>
                    <div className="form-check mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={systemSettings.notifications.newOrders}
                        onChange={(e) => {
                          setSystemSettings(prev => ({
                            ...prev,
                            notifications: { ...prev.notifications, newOrders: e.target.checked }
                          }));
                          setHasChanges(true);
                        }}
                      />
                      <label className="form-check-label">
                        📋 New Order Notifications
                      </label>
                    </div>
                    <div className="form-check mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={systemSettings.notifications.paymentReceived}
                        onChange={(e) => {
                          setSystemSettings(prev => ({
                            ...prev,
                            notifications: { ...prev.notifications, paymentReceived: e.target.checked }
                          }));
                          setHasChanges(true);
                        }}
                      />
                      <label className="form-check-label">
                        💰 Payment Received Notifications
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Integrations Settings */}
          {activeTab === 'integrations' && (
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">🔗 Third-party Integrations</h5>
              </div>
              <div className="card-body">
                {/* E-commerce Platforms */}
                <h6 className="mb-3">🛒 E-commerce Platforms</h6>
                <div className="row mb-4">
                  <div className="col-md-4">
                    <div className="card">
                      <div className="card-body">
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <h6 className="mb-0">WooCommerce</h6>
                          <div className="form-check form-switch">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={integrationSettings.woocommerce.enabled}
                              onChange={(e) => {
                                setIntegrationSettings(prev => ({
                                  ...prev,
                                  woocommerce: { ...prev.woocommerce, enabled: e.target.checked }
                                }));
                                setHasChanges(true);
                              }}
                            />
                          </div>
                        </div>
                        {integrationSettings.woocommerce.enabled && (
                          <>
                            <div className="mb-2">
                              <input
                                type="text"
                                className="form-control form-control-sm"
                                placeholder="Store URL"
                                value={integrationSettings.woocommerce.storeUrl}
                                onChange={(e) => {
                                  setIntegrationSettings(prev => ({
                                    ...prev,
                                    woocommerce: { ...prev.woocommerce, storeUrl: e.target.value }
                                  }));
                                  setHasChanges(true);
                                }}
                              />
                            </div>
                            <div className="mb-2">
                              <input
                                type="text"
                                className="form-control form-control-sm"
                                placeholder="API Key"
                                value={integrationSettings.woocommerce.apiKey}
                                onChange={(e) => {
                                  setIntegrationSettings(prev => ({
                                    ...prev,
                                    woocommerce: { ...prev.woocommerce, apiKey: e.target.value }
                                  }));
                                  setHasChanges(true);
                                }}
                              />
                            </div>
                            <div className="mb-2">
                              <input
                                type="password"
                                className="form-control form-control-sm"
                                placeholder="API Secret"
                                value={integrationSettings.woocommerce.apiSecret}
                                onChange={(e) => {
                                  setIntegrationSettings(prev => ({
                                    ...prev,
                                    woocommerce: { ...prev.woocommerce, apiSecret: e.target.value }
                                  }));
                                  setHasChanges(true);
                                }}
                              />
                            </div>
                            <button
                              className="btn btn-outline-primary btn-sm w-100"
                              onClick={() => handleTestIntegration('WooCommerce')}
                            >
                              🧪 Test Connection
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="col-md-4">
                    <div className="card">
                      <div className="card-body">
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <h6 className="mb-0">Shopify</h6>
                          <div className="form-check form-switch">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={integrationSettings.shopify.enabled}
                              onChange={(e) => {
                                setIntegrationSettings(prev => ({
                                  ...prev,
                                  shopify: { ...prev.shopify, enabled: e.target.checked }
                                }));
                                setHasChanges(true);
                              }}
                            />
                          </div>
                        </div>
                        {integrationSettings.shopify.enabled && (
                          <>
                            <div className="mb-2">
                              <input
                                type="text"
                                className="form-control form-control-sm"
                                placeholder="Store URL"
                                value={integrationSettings.shopify.storeUrl}
                                onChange={(e) => {
                                  setIntegrationSettings(prev => ({
                                    ...prev,
                                    shopify: { ...prev.shopify, storeUrl: e.target.value }
                                  }));
                                  setHasChanges(true);
                                }}
                              />
                            </div>
                            <div className="mb-2">
                              <input
                                type="text"
                                className="form-control form-control-sm"
                                placeholder="API Key"
                                value={integrationSettings.shopify.apiKey}
                                onChange={(e) => {
                                  setIntegrationSettings(prev => ({
                                    ...prev,
                                    shopify: { ...prev.shopify, apiKey: e.target.value }
                                  }));
                                  setHasChanges(true);
                                }}
                              />
                            </div>
                            <div className="mb-2">
                              <input
                                type="password"
                                className="form-control form-control-sm"
                                placeholder="API Secret"
                                value={integrationSettings.shopify.apiSecret}
                                onChange={(e) => {
                                  setIntegrationSettings(prev => ({
                                    ...prev,
                                    shopify: { ...prev.shopify, apiSecret: e.target.value }
                                  }));
                                  setHasChanges(true);
                                }}
                              />
                            </div>
                            <button
                              className="btn btn-outline-primary btn-sm w-100"
                              onClick={() => handleTestIntegration('Shopify')}
                            >
                              🧪 Test Connection
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="col-md-4">
                    <div className="card">
                      <div className="card-body">
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <h6 className="mb-0">Magento</h6>
                          <div className="form-check form-switch">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={integrationSettings.magento.enabled}
                              onChange={(e) => {
                                setIntegrationSettings(prev => ({
                                  ...prev,
                                  magento: { ...prev.magento, enabled: e.target.checked }
                                }));
                                setHasChanges(true);
                              }}
                            />
                          </div>
                        </div>
                        {integrationSettings.magento.enabled && (
                          <>
                            <div className="mb-2">
                              <input
                                type="text"
                                className="form-control form-control-sm"
                                placeholder="Store URL"
                                value={integrationSettings.magento.storeUrl}
                                onChange={(e) => {
                                  setIntegrationSettings(prev => ({
                                    ...prev,
                                    magento: { ...prev.magento, storeUrl: e.target.value }
                                  }));
                                  setHasChanges(true);
                                }}
                              />
                            </div>
                            <div className="mb-2">
                              <input
                                type="text"
                                className="form-control form-control-sm"
                                placeholder="API Key"
                                value={integrationSettings.magento.apiKey}
                                onChange={(e) => {
                                  setIntegrationSettings(prev => ({
                                    ...prev,
                                    magento: { ...prev.magento, apiKey: e.target.value }
                                  }));
                                  setHasChanges(true);
                                }}
                              />
                            </div>
                            <div className="mb-2">
                              <input
                                type="password"
                                className="form-control form-control-sm"
                                placeholder="API Secret"
                                value={integrationSettings.magento.apiSecret}
                                onChange={(e) => {
                                  setIntegrationSettings(prev => ({
                                    ...prev,
                                    magento: { ...prev.magento, apiSecret: e.target.value }
                                  }));
                                  setHasChanges(true);
                                }}
                              />
                            </div>
                            <button
                              className="btn btn-outline-primary btn-sm w-100"
                              onClick={() => handleTestIntegration('Magento')}
                            >
                              🧪 Test Connection
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Payment Gateways */}
                <h6 className="mb-3">💳 Payment Gateways</h6>
                <div className="row mb-4">
                  <div className="col-md-6">
                    <div className="card">
                      <div className="card-body">
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <h6 className="mb-0">Stripe</h6>
                          <div className="form-check form-switch">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={integrationSettings.stripe.enabled}
                              onChange={(e) => {
                                setIntegrationSettings(prev => ({
                                  ...prev,
                                  stripe: { ...prev.stripe, enabled: e.target.checked }
                                }));
                                setHasChanges(true);
                              }}
                            />
                          </div>
                        </div>
                        {integrationSettings.stripe.enabled && (
                          <>
                            <div className="mb-2">
                              <input
                                type="text"
                                className="form-control form-control-sm"
                                placeholder="Publishable Key"
                                value={integrationSettings.stripe.publishableKey}
                                onChange={(e) => {
                                  setIntegrationSettings(prev => ({
                                    ...prev,
                                    stripe: { ...prev.stripe, publishableKey: e.target.value }
                                  }));
                                  setHasChanges(true);
                                }}
                              />
                            </div>
                            <div className="mb-2">
                              <input
                                type="password"
                                className="form-control form-control-sm"
                                placeholder="Secret Key"
                                value={integrationSettings.stripe.secretKey}
                                onChange={(e) => {
                                  setIntegrationSettings(prev => ({
                                    ...prev,
                                    stripe: { ...prev.stripe, secretKey: e.target.value }
                                  }));
                                  setHasChanges(true);
                                }}
                              />
                            </div>
                            <button
                              className="btn btn-outline-primary btn-sm w-100"
                              onClick={() => handleTestIntegration('Stripe')}
                            >
                              🧪 Test Connection
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="card">
                      <div className="card-body">
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <h6 className="mb-0">PayPal</h6>
                          <div className="form-check form-switch">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={integrationSettings.paypal.enabled}
                              onChange={(e) => {
                                setIntegrationSettings(prev => ({
                                  ...prev,
                                  paypal: { ...prev.paypal, enabled: e.target.checked }
                                }));
                                setHasChanges(true);
                              }}
                            />
                          </div>
                        </div>
                        {integrationSettings.paypal.enabled && (
                          <>
                            <div className="mb-2">
                              <input
                                type="text"
                                className="form-control form-control-sm"
                                placeholder="Client ID"
                                value={integrationSettings.paypal.clientId}
                                onChange={(e) => {
                                  setIntegrationSettings(prev => ({
                                    ...prev,
                                    paypal: { ...prev.paypal, clientId: e.target.value }
                                  }));
                                  setHasChanges(true);
                                }}
                              />
                            </div>
                            <div className="mb-2">
                              <input
                                type="password"
                                className="form-control form-control-sm"
                                placeholder="Client Secret"
                                value={integrationSettings.paypal.clientSecret}
                                onChange={(e) => {
                                  setIntegrationSettings(prev => ({
                                    ...prev,
                                    paypal: { ...prev.paypal, clientSecret: e.target.value }
                                  }));
                                  setHasChanges(true);
                                }}
                              />
                            </div>
                            <button
                              className="btn btn-outline-primary btn-sm w-100"
                              onClick={() => handleTestIntegration('PayPal')}
                            >
                              🧪 Test Connection
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* API Settings */}
                <h6 className="mb-3">🔌 API Configuration</h6>
                <div className="card">
                  <div className="card-body">
                    <div className="row">
                      <div className="col-md-6">
                        <div className="form-check form-switch mb-3">
                          <input
                            className="form-check-input"
                            type="checkbox"
                            checked={apiSettings.enableApi}
                            onChange={(e) => {
                              setApiSettings(prev => ({ ...prev, enableApi: e.target.checked }));
                              setHasChanges(true);
                            }}
                          />
                          <label className="form-check-label">
                            Enable API Access
                          </label>
                        </div>
                        <div className="mb-3">
                          <label className="form-label">API Version</label>
                          <select
                            className="form-select"
                            value={apiSettings.apiVersion}
                            onChange={(e) => {
                              setApiSettings(prev => ({ ...prev, apiVersion: e.target.value }));
                              setHasChanges(true);
                            }}
                          >
                            <option value="v1">Version 1.0</option>
                            <option value="v2">Version 2.0 (Beta)</option>
                          </select>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="form-check form-switch mb-3">
                          <input
                            className="form-check-input"
                            type="checkbox"
                            checked={apiSettings.rateLimiting}
                            onChange={(e) => {
                              setApiSettings(prev => ({ ...prev, rateLimiting: e.target.checked }));
                              setHasChanges(true);
                            }}
                          />
                          <label className="form-check-label">
                            Enable Rate Limiting
                          </label>
                        </div>
                        <div className="mb-3">
                          <label className="form-label">Max Requests per Minute</label>
                          <input
                            type="number"
                            className="form-control"
                            value={apiSettings.maxRequestsPerMinute}
                            onChange={(e) => {
                              setApiSettings(prev => ({ ...prev, maxRequestsPerMinute: Number(e.target.value) }));
                              setHasChanges(true);
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Backup Settings */}
          {activeTab === 'backup' && (
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">💾 Backup & Recovery</h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6">
                    <h6 className="mb-3">Backup Configuration</h6>
                    <div className="form-check form-switch mb-3">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={backupSettings.autoBackup}
                        onChange={(e) => {
                          setBackupSettings(prev => ({ ...prev, autoBackup: e.target.checked }));
                          setHasChanges(true);
                        }}
                      />
                      <label className="form-check-label">
                        Enable Automatic Backups
                      </label>
                    </div>
                    <div className="mb-3">
                      <label className="form-label">Backup Frequency</label>
                      <select
                        className="form-select"
                        value={backupSettings.backupFrequency}
                        onChange={(e) => {
                          setBackupSettings(prev => ({ ...prev, backupFrequency: e.target.value }));
                          setHasChanges(true);
                        }}
                      >
                        <option value="hourly">Hourly</option>
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                        <option value="monthly">Monthly</option>
                      </select>
                    </div>
                    <div className="mb-3">
                      <label className="form-label">Retention Period (Days)</label>
                      <input
                        type="number"
                        className="form-control"
                        value={backupSettings.retentionDays}
                        onChange={(e) => {
                          setBackupSettings(prev => ({ ...prev, retentionDays: Number(e.target.value) }));
                          setHasChanges(true);
                        }}
                      />
                    </div>
                  </div>
                  <div className="col-md-6">
                    <h6 className="mb-3">Backup Actions</h6>
                    <div className="d-grid gap-2">
                      <button
                        className="btn btn-primary"
                        onClick={handleBackupNow}
                      >
                        💾 Create Backup Now
                      </button>
                      <button
                        className="btn btn-outline-warning"
                        onClick={handleRestoreBackup}
                      >
                        🔄 Restore from Backup
                      </button>
                      <button
                        className="btn btn-outline-info"
                        onClick={() => toast.success('Backup verification completed')}
                      >
                        ✅ Verify Backups
                      </button>
                      <button
                        className="btn btn-outline-secondary"
                        onClick={() => toast.success('Backup schedule updated')}
                      >
                        📅 Schedule Backup
                      </button>
                    </div>

                    <div className="mt-4">
                      <h6>Recent Backups</h6>
                      <div className="list-group">
                        <div className="list-group-item d-flex justify-content-between align-items-center">
                          <div>
                            <div className="fw-medium">Full Backup</div>
                            <small className="text-muted">Today, 2:00 AM</small>
                          </div>
                          <span className="badge bg-success">✅</span>
                        </div>
                        <div className="list-group-item d-flex justify-content-between align-items-center">
                          <div>
                            <div className="fw-medium">Incremental Backup</div>
                            <small className="text-muted">Yesterday, 2:00 AM</small>
                          </div>
                          <span className="badge bg-success">✅</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Other Settings Tabs */}
          {(activeTab === 'inventory' || activeTab === 'payment' || activeTab === 'security') && (
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">
                  {activeTab === 'inventory' && '📦 Inventory Settings'}
                  {activeTab === 'payment' && '💳 Payment Settings'}
                  {activeTab === 'security' && '🔒 Security Settings'}
                </h5>
              </div>
              <div className="card-body">
                <div className="text-center py-5">
                  <div className="mb-3">
                    <span className="display-1">
                      {activeTab === 'inventory' && '📦'}
                      {activeTab === 'payment' && '💳'}
                      {activeTab === 'security' && '🔒'}
                    </span>
                  </div>
                  <h5>
                    {activeTab === 'inventory' && 'Advanced Inventory Configuration'}
                    {activeTab === 'payment' && 'Payment Gateway Management'}
                    {activeTab === 'security' && 'Security & Access Control'}
                  </h5>
                  <p className="text-muted">
                    {activeTab === 'inventory' && 'Configure inventory management settings, stock thresholds, and valuation methods'}
                    {activeTab === 'payment' && 'Set up payment gateways, configure payment methods, and manage transaction settings'}
                    {activeTab === 'security' && 'Manage user access, authentication settings, and security policies'}
                  </p>
                  <button
                    className="btn btn-primary"
                    onClick={() => toast.success(`${activeTab} settings are now available in the advanced panel above`)}
                  >
                    Configure {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Settings
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClientSettings;
