import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface Order {
  id: string;
  orderNumber: string;
  customerId?: string;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  items: OrderItem[];
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod?: string;
  shippingAddress?: string;
  billingAddress?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  sku: string;
  price: number;
  quantity: number;
  total: number;
}

const ClientOrders: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [paymentFilter, setPaymentFilter] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  // Load sample data
  useEffect(() => {
    const sampleOrders: Order[] = [
      {
        id: '1',
        orderNumber: 'ORD-2024-001',
        customerName: 'John Smith',
        customerEmail: '<EMAIL>',
        customerPhone: '+****************',
        items: [
          {
            id: '1',
            productId: '1',
            productName: 'Wireless Headphones',
            sku: 'WH001',
            price: 99.99,
            quantity: 2,
            total: 199.98
          },
          {
            id: '2',
            productId: '2',
            productName: 'Coffee Mug',
            sku: 'CM001',
            price: 12.99,
            quantity: 1,
            total: 12.99
          }
        ],
        subtotal: 212.97,
        discount: 10.00,
        tax: 20.30,
        total: 223.27,
        status: 'confirmed',
        paymentStatus: 'paid',
        paymentMethod: 'Credit Card',
        shippingAddress: '123 Main St, City, State 12345',
        billingAddress: '123 Main St, City, State 12345',
        notes: 'Please deliver after 5 PM',
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T11:00:00Z'
      },
      {
        id: '2',
        orderNumber: 'ORD-2024-002',
        customerName: 'Jane Doe',
        customerEmail: '<EMAIL>',
        customerPhone: '+****************',
        items: [
          {
            id: '3',
            productId: '3',
            productName: 'Office Chair',
            sku: 'OC001',
            price: 299.99,
            quantity: 1,
            total: 299.99
          }
        ],
        subtotal: 299.99,
        discount: 0,
        tax: 30.00,
        total: 329.99,
        status: 'processing',
        paymentStatus: 'paid',
        paymentMethod: 'PayPal',
        shippingAddress: '456 Oak Ave, City, State 67890',
        createdAt: '2024-01-14T14:20:00Z',
        updatedAt: '2024-01-15T09:15:00Z'
      },
      {
        id: '3',
        orderNumber: 'ORD-2024-003',
        customerName: 'Mike Johnson',
        customerEmail: '<EMAIL>',
        items: [
          {
            id: '4',
            productId: '2',
            productName: 'Coffee Mug',
            sku: 'CM001',
            price: 12.99,
            quantity: 5,
            total: 64.95
          }
        ],
        subtotal: 64.95,
        discount: 5.00,
        tax: 6.00,
        total: 65.95,
        status: 'pending',
        paymentStatus: 'pending',
        paymentMethod: 'Cash on Delivery',
        shippingAddress: '789 Pine St, City, State 54321',
        createdAt: '2024-01-16T08:45:00Z',
        updatedAt: '2024-01-16T08:45:00Z'
      }
    ];
    setOrders(sampleOrders);
    setFilteredOrders(sampleOrders);
  }, []);

  // Filter orders
  useEffect(() => {
    let filtered = orders;

    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerEmail?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(order => order.status === statusFilter);
    }

    if (paymentFilter) {
      filtered = filtered.filter(order => order.paymentStatus === paymentFilter);
    }

    setFilteredOrders(filtered);
  }, [orders, searchTerm, statusFilter, paymentFilter]);

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowOrderDetails(true);
  };

  const handleUpdateOrderStatus = (orderId: string, newStatus: Order['status']) => {
    setOrders(prev => prev.map(order => 
      order.id === orderId 
        ? { ...order, status: newStatus, updatedAt: new Date().toISOString() }
        : order
    ));
    toast.success('Order status updated successfully');
  };

  const handleUpdatePaymentStatus = (orderId: string, newStatus: Order['paymentStatus']) => {
    setOrders(prev => prev.map(order => 
      order.id === orderId 
        ? { ...order, paymentStatus: newStatus, updatedAt: new Date().toISOString() }
        : order
    ));
    toast.success('Payment status updated successfully');
  };

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'warning',
      confirmed: 'info',
      processing: 'primary',
      shipped: 'success',
      delivered: 'success',
      cancelled: 'danger',
      refunded: 'secondary'
    };
    return colors[status as keyof typeof colors] || 'secondary';
  };

  const getPaymentStatusColor = (status: string) => {
    const colors = {
      pending: 'warning',
      paid: 'success',
      failed: 'danger',
      refunded: 'secondary'
    };
    return colors[status as keyof typeof colors] || 'secondary';
  };

  const getTotalRevenue = () => {
    return orders.filter(o => o.paymentStatus === 'paid').reduce((sum, order) => sum + order.total, 0);
  };

  const getPendingOrdersCount = () => {
    return orders.filter(o => o.status === 'pending').length;
  };

  const getProcessingOrdersCount = () => {
    return orders.filter(o => o.status === 'processing').length;
  };

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Order Management</h1>
          <p className="text-muted mb-0">Manage customer orders and track fulfillment</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-primary"
            onClick={() => toast.success('Order reports coming soon!')}
          >
            📊 Reports
          </button>
          <button
            className="btn btn-outline-success"
            onClick={() => toast.success('Export orders coming soon!')}
          >
            📤 Export
          </button>
          <button
            className="btn btn-primary"
            onClick={() => toast.success('Create order coming soon!')}
          >
            ➕ Create Order
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary bg-opacity-10 p-3 rounded">
                    <span className="text-primary fs-4">📋</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Orders</h6>
                  <h4 className="mb-0">{orders.length}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-warning bg-opacity-10 p-3 rounded">
                    <span className="text-warning fs-4">⏳</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Pending Orders</h6>
                  <h4 className="mb-0">{getPendingOrdersCount()}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-info bg-opacity-10 p-3 rounded">
                    <span className="text-info fs-4">⚙️</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Processing</h6>
                  <h4 className="mb-0">{getProcessingOrdersCount()}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-success bg-opacity-10 p-3 rounded">
                    <span className="text-success fs-4">💰</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Revenue</h6>
                  <h4 className="mb-0">${getTotalRevenue().toFixed(2)}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="card mb-4">
        <div className="card-body">
          <div className="row align-items-center">
            <div className="col-md-4">
              <input
                type="text"
                className="form-control"
                placeholder="Search orders, customer name, or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="col-md-2">
              <select
                className="form-select"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="confirmed">Confirmed</option>
                <option value="processing">Processing</option>
                <option value="shipped">Shipped</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
                <option value="refunded">Refunded</option>
              </select>
            </div>
            <div className="col-md-2">
              <select
                className="form-select"
                value={paymentFilter}
                onChange={(e) => setPaymentFilter(e.target.value)}
              >
                <option value="">All Payments</option>
                <option value="pending">Pending</option>
                <option value="paid">Paid</option>
                <option value="failed">Failed</option>
                <option value="refunded">Refunded</option>
              </select>
            </div>
            <div className="col-md-2">
              <button
                className="btn btn-outline-secondary w-100"
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                  setPaymentFilter('');
                }}
              >
                🔄 Clear Filters
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Orders List */}
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">Orders ({filteredOrders.length})</h5>
        </div>
        <div className="card-body p-0">
          {filteredOrders.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <span className="display-1">📋</span>
              </div>
              <h5>No Orders Found</h5>
              <p className="text-muted">
                {searchTerm || statusFilter || paymentFilter 
                  ? 'Try adjusting your filters' 
                  : 'No orders have been placed yet'
                }
              </p>
            </div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead className="table-light">
                  <tr>
                    <th>Order</th>
                    <th>Customer</th>
                    <th>Items</th>
                    <th>Total</th>
                    <th>Status</th>
                    <th>Payment</th>
                    <th>Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredOrders.map((order) => (
                    <tr key={order.id}>
                      <td>
                        <div>
                          <div className="fw-medium">{order.orderNumber}</div>
                          <small className="text-muted">ID: {order.id}</small>
                        </div>
                      </td>
                      <td>
                        <div>
                          <div className="fw-medium">{order.customerName}</div>
                          <small className="text-muted">{order.customerEmail}</small>
                        </div>
                      </td>
                      <td>
                        <span className="badge bg-light text-dark">
                          {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                        </span>
                      </td>
                      <td>
                        <div>
                          <div className="fw-medium">${order.total.toFixed(2)}</div>
                          {order.discount > 0 && (
                            <small className="text-success">-${order.discount.toFixed(2)} discount</small>
                          )}
                        </div>
                      </td>
                      <td>
                        <span className={`badge bg-${getStatusColor(order.status)}`}>
                          {order.status}
                        </span>
                      </td>
                      <td>
                        <span className={`badge bg-${getPaymentStatusColor(order.paymentStatus)}`}>
                          {order.paymentStatus}
                        </span>
                      </td>
                      <td>
                        <small className="text-muted">
                          {new Date(order.createdAt).toLocaleDateString()}
                        </small>
                      </td>
                      <td>
                        <div className="btn-group btn-group-sm">
                          <button
                            className="btn btn-outline-primary"
                            onClick={() => handleViewOrder(order)}
                          >
                            👁️ View
                          </button>
                          <div className="dropdown">
                            <button
                              className="btn btn-outline-secondary dropdown-toggle"
                              type="button"
                              data-bs-toggle="dropdown"
                            >
                              Actions
                            </button>
                            <ul className="dropdown-menu">
                              <li><h6 className="dropdown-header">Update Status</h6></li>
                              <li>
                                <button
                                  className="dropdown-item"
                                  onClick={() => handleUpdateOrderStatus(order.id, 'confirmed')}
                                >
                                  ✅ Confirm Order
                                </button>
                              </li>
                              <li>
                                <button
                                  className="dropdown-item"
                                  onClick={() => handleUpdateOrderStatus(order.id, 'processing')}
                                >
                                  ⚙️ Start Processing
                                </button>
                              </li>
                              <li>
                                <button
                                  className="dropdown-item"
                                  onClick={() => handleUpdateOrderStatus(order.id, 'shipped')}
                                >
                                  🚚 Mark as Shipped
                                </button>
                              </li>
                              <li>
                                <button
                                  className="dropdown-item"
                                  onClick={() => handleUpdateOrderStatus(order.id, 'delivered')}
                                >
                                  📦 Mark as Delivered
                                </button>
                              </li>
                              <li><hr className="dropdown-divider" /></li>
                              <li>
                                <button
                                  className="dropdown-item text-danger"
                                  onClick={() => handleUpdateOrderStatus(order.id, 'cancelled')}
                                >
                                  ❌ Cancel Order
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Order Details Modal */}
      {showOrderDetails && selectedOrder && (
        <OrderDetailsModal
          order={selectedOrder}
          onClose={() => setShowOrderDetails(false)}
          onUpdateStatus={handleUpdateOrderStatus}
          onUpdatePaymentStatus={handleUpdatePaymentStatus}
        />
      )}
    </div>
  );
};

// Order Details Modal Component (placeholder)
const OrderDetailsModal: React.FC<{
  order: Order;
  onClose: () => void;
  onUpdateStatus: (orderId: string, status: Order['status']) => void;
  onUpdatePaymentStatus: (orderId: string, status: Order['paymentStatus']) => void;
}> = ({ order, onClose, onUpdateStatus, onUpdatePaymentStatus }) => {
  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-xl">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">Order Details - {order.orderNumber}</h5>
            <button type="button" className="btn-close" onClick={onClose}></button>
          </div>
          <div className="modal-body">
            <div className="text-center py-4">
              <p>Order details view will be implemented next</p>
              <p className="text-muted">This will show complete order information, items, customer details, and tracking</p>
            </div>
          </div>
          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientOrders;
