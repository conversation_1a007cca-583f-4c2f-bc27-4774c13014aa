import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

// Custom dropdown styles
const dropdownStyles = `
  .custom-dropdown {
    position: relative;
    display: inline-block;
  }

  .custom-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    min-width: 200px;
    background-color: #fff;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.175);
    max-height: 300px;
    overflow-y: auto;
  }

  .custom-dropdown-item {
    display: block;
    width: 100%;
    padding: 0.375rem 1rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    cursor: pointer;
  }

  .custom-dropdown-item:hover {
    background-color: #f8f9fa;
  }

  .custom-dropdown-header {
    display: block;
    padding: 0.5rem 1rem;
    margin-bottom: 0;
    font-size: 0.875rem;
    color: #6c757d;
    white-space: nowrap;
  }

  .custom-dropdown-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid rgba(0,0,0,.15);
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = dropdownStyles;
  document.head.appendChild(styleElement);
}

interface Order {
  id: string;
  orderNumber: string;
  customerId?: string;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  items: OrderItem[];
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod?: string;
  shippingAddress?: string;
  billingAddress?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  sku: string;
  price: number;
  quantity: number;
  total: number;
}

const ClientOrders: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [paymentFilter, setPaymentFilter] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  // Load sample data
  useEffect(() => {
    const sampleOrders: Order[] = [
      {
        id: '1',
        orderNumber: 'ORD-2024-001',
        customerName: 'John Smith',
        customerEmail: '<EMAIL>',
        customerPhone: '+****************',
        items: [
          {
            id: '1',
            productId: '1',
            productName: 'Wireless Headphones',
            sku: 'WH001',
            price: 99.99,
            quantity: 2,
            total: 199.98
          },
          {
            id: '2',
            productId: '2',
            productName: 'Coffee Mug',
            sku: 'CM001',
            price: 12.99,
            quantity: 1,
            total: 12.99
          }
        ],
        subtotal: 212.97,
        discount: 10.00,
        tax: 20.30,
        total: 223.27,
        status: 'confirmed',
        paymentStatus: 'paid',
        paymentMethod: 'Credit Card',
        shippingAddress: '123 Main St, City, State 12345',
        billingAddress: '123 Main St, City, State 12345',
        notes: 'Please deliver after 5 PM',
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T11:00:00Z'
      },
      {
        id: '2',
        orderNumber: 'ORD-2024-002',
        customerName: 'Jane Doe',
        customerEmail: '<EMAIL>',
        customerPhone: '+****************',
        items: [
          {
            id: '3',
            productId: '3',
            productName: 'Office Chair',
            sku: 'OC001',
            price: 299.99,
            quantity: 1,
            total: 299.99
          }
        ],
        subtotal: 299.99,
        discount: 0,
        tax: 30.00,
        total: 329.99,
        status: 'processing',
        paymentStatus: 'paid',
        paymentMethod: 'PayPal',
        shippingAddress: '456 Oak Ave, City, State 67890',
        createdAt: '2024-01-14T14:20:00Z',
        updatedAt: '2024-01-15T09:15:00Z'
      },
      {
        id: '3',
        orderNumber: 'ORD-2024-003',
        customerName: 'Mike Johnson',
        customerEmail: '<EMAIL>',
        items: [
          {
            id: '4',
            productId: '2',
            productName: 'Coffee Mug',
            sku: 'CM001',
            price: 12.99,
            quantity: 5,
            total: 64.95
          }
        ],
        subtotal: 64.95,
        discount: 5.00,
        tax: 6.00,
        total: 65.95,
        status: 'pending',
        paymentStatus: 'pending',
        paymentMethod: 'Cash on Delivery',
        shippingAddress: '789 Pine St, City, State 54321',
        createdAt: '2024-01-16T08:45:00Z',
        updatedAt: '2024-01-16T08:45:00Z'
      }
    ];
    setOrders(sampleOrders);
    setFilteredOrders(sampleOrders);
  }, []);

  // Filter orders
  useEffect(() => {
    let filtered = orders;

    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerEmail?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(order => order.status === statusFilter);
    }

    if (paymentFilter) {
      filtered = filtered.filter(order => order.paymentStatus === paymentFilter);
    }

    setFilteredOrders(filtered);
  }, [orders, searchTerm, statusFilter, paymentFilter]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdown && !(event.target as Element).closest('.position-relative')) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openDropdown]);

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowOrderDetails(true);
  };

  const handleUpdateOrderStatus = (orderId: string, newStatus: Order['status']) => {
    setOrders(prev => prev.map(order => 
      order.id === orderId 
        ? { ...order, status: newStatus, updatedAt: new Date().toISOString() }
        : order
    ));
    toast.success('Order status updated successfully');
  };

  const handleUpdatePaymentStatus = (orderId: string, newStatus: Order['paymentStatus']) => {
    setOrders(prev => prev.map(order =>
      order.id === orderId
        ? { ...order, paymentStatus: newStatus, updatedAt: new Date().toISOString() }
        : order
    ));
    toast.success('Payment status updated successfully');
  };

  const toggleDropdown = (orderId: string) => {
    setOpenDropdown(openDropdown === orderId ? null : orderId);
  };

  const handleActionClick = (orderId: string, action: () => void) => {
    action();
    setOpenDropdown(null); // Close dropdown after action
  };

  const handlePrintInvoice = (order: Order) => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Invoice - ${order.orderNumber}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              .invoice-details { margin-bottom: 20px; }
              .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
              .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              .table th { background-color: #f2f2f2; }
              .total { text-align: right; font-weight: bold; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>INVOICE</h1>
              <h2>${order.orderNumber}</h2>
            </div>
            <div class="invoice-details">
              <p><strong>Customer:</strong> ${order.customerName}</p>
              <p><strong>Email:</strong> ${order.customerEmail || 'N/A'}</p>
              <p><strong>Phone:</strong> ${order.customerPhone || 'N/A'}</p>
              <p><strong>Date:</strong> ${new Date(order.createdAt).toLocaleDateString()}</p>
              <p><strong>Status:</strong> ${order.status}</p>
              <p><strong>Payment Status:</strong> ${order.paymentStatus}</p>
            </div>
            <table class="table">
              <thead>
                <tr>
                  <th>Item</th>
                  <th>SKU</th>
                  <th>Price</th>
                  <th>Quantity</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody>
                ${order.items.map(item => `
                  <tr>
                    <td>${item.productName}</td>
                    <td>${item.sku}</td>
                    <td>$${item.price.toFixed(2)}</td>
                    <td>${item.quantity}</td>
                    <td>$${item.total.toFixed(2)}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
            <div class="total">
              <p>Subtotal: $${order.subtotal.toFixed(2)}</p>
              <p>Discount: -$${order.discount.toFixed(2)}</p>
              <p>Tax: $${order.tax.toFixed(2)}</p>
              <p><strong>Total: $${order.total.toFixed(2)}</strong></p>
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
    toast.success('Invoice printed successfully');
  };

  const handleEmailCustomer = (order: Order) => {
    // Simulate sending email
    toast.promise(
      new Promise((resolve) => {
        setTimeout(() => {
          resolve(`Email sent to ${order.customerEmail || order.customerName}`);
        }, 2000);
      }),
      {
        loading: 'Sending email...',
        success: (message) => `${message}`,
        error: 'Failed to send email'
      }
    );
  };

  const handleDuplicateOrder = (order: Order) => {
    const newOrder: Order = {
      ...order,
      id: (orders.length + 1).toString(),
      orderNumber: `ORD-2024-${String(orders.length + 1).padStart(3, '0')}`,
      status: 'pending',
      paymentStatus: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      notes: `Duplicated from ${order.orderNumber}${order.notes ? ` - ${order.notes}` : ''}`
    };

    setOrders(prev => [...prev, newOrder]);
    toast.success(`Order duplicated as ${newOrder.orderNumber}`);
  };

  const handleExportOrder = (order: Order) => {
    const orderData = {
      orderNumber: order.orderNumber,
      customer: {
        name: order.customerName,
        email: order.customerEmail,
        phone: order.customerPhone
      },
      items: order.items,
      totals: {
        subtotal: order.subtotal,
        discount: order.discount,
        tax: order.tax,
        total: order.total
      },
      status: order.status,
      paymentStatus: order.paymentStatus,
      paymentMethod: order.paymentMethod,
      addresses: {
        billing: order.billingAddress,
        shipping: order.shippingAddress
      },
      dates: {
        created: order.createdAt,
        updated: order.updatedAt
      },
      notes: order.notes
    };

    const dataStr = JSON.stringify(orderData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `order-${order.orderNumber}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast.success('Order exported successfully');
  };

  const handleTrackOrder = (order: Order) => {
    // Simulate tracking information
    const trackingInfo = {
      'pending': 'Order is being prepared',
      'confirmed': 'Order confirmed and being processed',
      'processing': 'Order is being packed',
      'shipped': 'Order has been shipped - Tracking: TRK123456789',
      'delivered': 'Order has been delivered',
      'cancelled': 'Order was cancelled'
    };

    toast.success(trackingInfo[order.status] || 'No tracking information available');
  };

  const handleAddNote = (order: Order) => {
    const note = prompt('Add a note to this order:', order.notes || '');
    if (note !== null) {
      setOrders(prev => prev.map(o =>
        o.id === order.id
          ? { ...o, notes: note, updatedAt: new Date().toISOString() }
          : o
      ));
      toast.success('Note added to order');
    }
  };

  const handleGenerateReports = () => {
    toast.promise(
      new Promise((resolve) => {
        setTimeout(() => {
          resolve('Order reports generated successfully');
        }, 2000);
      }),
      {
        loading: 'Generating reports...',
        success: 'Order reports generated successfully!',
        error: 'Failed to generate reports'
      }
    );
  };

  const handleExportAllOrders = () => {
    const exportData = {
      exportDate: new Date().toISOString(),
      totalOrders: orders.length,
      orders: orders.map(order => ({
        orderNumber: order.orderNumber,
        customer: order.customerName,
        email: order.customerEmail,
        total: order.total,
        status: order.status,
        paymentStatus: order.paymentStatus,
        createdAt: order.createdAt,
        items: order.items.length
      }))
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `orders-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast.success('Orders exported successfully');
  };

  const handleCreateOrder = () => {
    const customerName = prompt('Enter customer name:');
    if (customerName) {
      const customerEmail = prompt('Enter customer email:');
      const newOrder: Order = {
        id: (orders.length + 1).toString(),
        orderNumber: `ORD-2024-${String(orders.length + 1).padStart(3, '0')}`,
        customerName,
        customerEmail: customerEmail || '',
        customerPhone: '',
        items: [],
        subtotal: 0,
        discount: 0,
        tax: 0,
        total: 0,
        status: 'pending',
        paymentStatus: 'pending',
        paymentMethod: '',
        shippingAddress: '',
        billingAddress: '',
        notes: 'Created manually',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      setOrders(prev => [...prev, newOrder]);
      toast.success(`Order ${newOrder.orderNumber} created successfully`);
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'warning',
      confirmed: 'info',
      processing: 'primary',
      shipped: 'success',
      delivered: 'success',
      cancelled: 'danger',
      refunded: 'secondary'
    };
    return colors[status as keyof typeof colors] || 'secondary';
  };

  const getPaymentStatusColor = (status: string) => {
    const colors = {
      pending: 'warning',
      paid: 'success',
      failed: 'danger',
      refunded: 'secondary'
    };
    return colors[status as keyof typeof colors] || 'secondary';
  };

  const getTotalRevenue = () => {
    return orders.filter(o => o.paymentStatus === 'paid').reduce((sum, order) => sum + order.total, 0);
  };

  const getPendingOrdersCount = () => {
    return orders.filter(o => o.status === 'pending').length;
  };

  const getProcessingOrdersCount = () => {
    return orders.filter(o => o.status === 'processing').length;
  };

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Order Management</h1>
          <p className="text-muted mb-0">Manage customer orders and track fulfillment</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-primary"
            onClick={handleGenerateReports}
          >
            📊 Reports
          </button>
          <button
            className="btn btn-outline-success"
            onClick={handleExportAllOrders}
          >
            📤 Export
          </button>
          <button
            className="btn btn-primary"
            onClick={handleCreateOrder}
          >
            ➕ Create Order
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary bg-opacity-10 p-3 rounded">
                    <span className="text-primary fs-4">📋</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Orders</h6>
                  <h4 className="mb-0">{orders.length}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-warning bg-opacity-10 p-3 rounded">
                    <span className="text-warning fs-4">⏳</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Pending Orders</h6>
                  <h4 className="mb-0">{getPendingOrdersCount()}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-info bg-opacity-10 p-3 rounded">
                    <span className="text-info fs-4">⚙️</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Processing</h6>
                  <h4 className="mb-0">{getProcessingOrdersCount()}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-success bg-opacity-10 p-3 rounded">
                    <span className="text-success fs-4">💰</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Revenue</h6>
                  <h4 className="mb-0">${getTotalRevenue().toFixed(2)}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="card mb-4">
        <div className="card-body">
          <div className="row align-items-center">
            <div className="col-md-4">
              <input
                type="text"
                className="form-control"
                placeholder="Search orders, customer name, or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="col-md-2">
              <select
                className="form-select"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="confirmed">Confirmed</option>
                <option value="processing">Processing</option>
                <option value="shipped">Shipped</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
                <option value="refunded">Refunded</option>
              </select>
            </div>
            <div className="col-md-2">
              <select
                className="form-select"
                value={paymentFilter}
                onChange={(e) => setPaymentFilter(e.target.value)}
              >
                <option value="">All Payments</option>
                <option value="pending">Pending</option>
                <option value="paid">Paid</option>
                <option value="failed">Failed</option>
                <option value="refunded">Refunded</option>
              </select>
            </div>
            <div className="col-md-2">
              <button
                className="btn btn-outline-secondary w-100"
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                  setPaymentFilter('');
                }}
              >
                🔄 Clear Filters
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Orders List */}
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">Orders ({filteredOrders.length})</h5>
        </div>
        <div className="card-body p-0">
          {filteredOrders.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <span className="display-1">📋</span>
              </div>
              <h5>No Orders Found</h5>
              <p className="text-muted">
                {searchTerm || statusFilter || paymentFilter 
                  ? 'Try adjusting your filters' 
                  : 'No orders have been placed yet'
                }
              </p>
            </div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead className="table-light">
                  <tr>
                    <th>Order</th>
                    <th>Customer</th>
                    <th>Items</th>
                    <th>Total</th>
                    <th>Status</th>
                    <th>Payment</th>
                    <th>Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredOrders.map((order) => (
                    <tr key={order.id}>
                      <td>
                        <div>
                          <div className="fw-medium">{order.orderNumber}</div>
                          <small className="text-muted">ID: {order.id}</small>
                        </div>
                      </td>
                      <td>
                        <div>
                          <div className="fw-medium">{order.customerName}</div>
                          <small className="text-muted">{order.customerEmail}</small>
                        </div>
                      </td>
                      <td>
                        <span className="badge bg-light text-dark">
                          {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                        </span>
                      </td>
                      <td>
                        <div>
                          <div className="fw-medium">${order.total.toFixed(2)}</div>
                          {order.discount > 0 && (
                            <small className="text-success">-${order.discount.toFixed(2)} discount</small>
                          )}
                        </div>
                      </td>
                      <td>
                        <span className={`badge bg-${getStatusColor(order.status)}`}>
                          {order.status}
                        </span>
                      </td>
                      <td>
                        <span className={`badge bg-${getPaymentStatusColor(order.paymentStatus)}`}>
                          {order.paymentStatus}
                        </span>
                      </td>
                      <td>
                        <small className="text-muted">
                          {new Date(order.createdAt).toLocaleDateString()}
                        </small>
                      </td>
                      <td>
                        <div className="btn-group btn-group-sm">
                          <button
                            className="btn btn-outline-primary"
                            onClick={() => handleViewOrder(order)}
                          >
                            👁️ View
                          </button>
                          <div className="custom-dropdown">
                            <button
                              className="btn btn-outline-secondary dropdown-toggle"
                              type="button"
                              onClick={() => toggleDropdown(order.id)}
                            >
                              Actions
                            </button>
                            {openDropdown === order.id && (
                              <div className="custom-dropdown-menu">
                                <h6 className="custom-dropdown-header">Quick Actions</h6>
                                <button
                                  className="custom-dropdown-item"
                                  onClick={() => handleActionClick(order.id, () => handleViewOrder(order))}
                                >
                                  👁️ View Details
                                </button>
                                <button
                                  className="custom-dropdown-item"
                                  onClick={() => handleActionClick(order.id, () => handlePrintInvoice(order))}
                                >
                                  🖨️ Print Invoice
                                </button>
                                <button
                                  className="custom-dropdown-item"
                                  onClick={() => handleActionClick(order.id, () => handleEmailCustomer(order))}
                                >
                                  ✉️ Email Customer
                                </button>
                                <button
                                  className="custom-dropdown-item"
                                  onClick={() => handleActionClick(order.id, () => handleTrackOrder(order))}
                                >
                                  📦 Track Order
                                </button>
                                <hr className="custom-dropdown-divider" />
                                <h6 className="custom-dropdown-header">Update Status</h6>
                                {order.status === 'pending' && (
                                  <button
                                    className="custom-dropdown-item"
                                    onClick={() => handleActionClick(order.id, () => handleUpdateOrderStatus(order.id, 'confirmed'))}
                                  >
                                    ✅ Confirm Order
                                  </button>
                                )}
                                {(order.status === 'confirmed' || order.status === 'pending') && (
                                  <button
                                    className="custom-dropdown-item"
                                    onClick={() => handleActionClick(order.id, () => handleUpdateOrderStatus(order.id, 'processing'))}
                                  >
                                    ⚙️ Start Processing
                                  </button>
                                )}
                                {order.status === 'processing' && (
                                  <button
                                    className="custom-dropdown-item"
                                    onClick={() => handleActionClick(order.id, () => handleUpdateOrderStatus(order.id, 'shipped'))}
                                  >
                                    🚚 Mark as Shipped
                                  </button>
                                )}
                                {order.status === 'shipped' && (
                                  <button
                                    className="custom-dropdown-item"
                                    onClick={() => handleActionClick(order.id, () => handleUpdateOrderStatus(order.id, 'delivered'))}
                                  >
                                    📦 Mark as Delivered
                                  </button>
                                )}
                                {order.status !== 'cancelled' && order.status !== 'delivered' && (
                                  <>
                                    <hr className="custom-dropdown-divider" />
                                    <button
                                      className="custom-dropdown-item text-danger"
                                      onClick={() => handleActionClick(order.id, () => {
                                        if (window.confirm('Are you sure you want to cancel this order?')) {
                                          handleUpdateOrderStatus(order.id, 'cancelled');
                                        }
                                      })}
                                    >
                                      ❌ Cancel Order
                                    </button>
                                  </>
                                )}
                                <hr className="custom-dropdown-divider" />
                                <h6 className="custom-dropdown-header">Payment Status</h6>
                                {order.paymentStatus === 'pending' && (
                                  <button
                                    className="custom-dropdown-item"
                                    onClick={() => handleActionClick(order.id, () => handleUpdatePaymentStatus(order.id, 'paid'))}
                                  >
                                    💳 Mark as Paid
                                  </button>
                                )}
                                {order.paymentStatus === 'paid' && (
                                  <button
                                    className="custom-dropdown-item"
                                    onClick={() => handleActionClick(order.id, () => {
                                      if (window.confirm('Are you sure you want to refund this order?')) {
                                        handleUpdatePaymentStatus(order.id, 'refunded');
                                      }
                                    })}
                                  >
                                    💸 Process Refund
                                  </button>
                                )}
                                <hr className="custom-dropdown-divider" />
                                <h6 className="custom-dropdown-header">More Actions</h6>
                                <button
                                  className="custom-dropdown-item"
                                  onClick={() => handleActionClick(order.id, () => handleDuplicateOrder(order))}
                                >
                                  📋 Duplicate Order
                                </button>
                                <button
                                  className="custom-dropdown-item"
                                  onClick={() => handleActionClick(order.id, () => handleExportOrder(order))}
                                >
                                  📤 Export Order
                                </button>
                                <button
                                  className="custom-dropdown-item"
                                  onClick={() => handleActionClick(order.id, () => handleAddNote(order))}
                                >
                                  📝 Add Note
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Order Details Modal */}
      {showOrderDetails && selectedOrder && (
        <OrderDetailsModal
          order={selectedOrder}
          onClose={() => setShowOrderDetails(false)}
          onUpdateStatus={handleUpdateOrderStatus}
          onUpdatePaymentStatus={handleUpdatePaymentStatus}
        />
      )}
    </div>
  );
};

// Order Details Modal Component
const OrderDetailsModal: React.FC<{
  order: Order;
  onClose: () => void;
  onUpdateStatus: (orderId: string, status: Order['status']) => void;
  onUpdatePaymentStatus: (orderId: string, status: Order['paymentStatus']) => void;
}> = ({ order, onClose, onUpdateStatus, onUpdatePaymentStatus }) => {
  const [activeTab, setActiveTab] = useState('details');

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'warning',
      confirmed: 'info',
      processing: 'primary',
      shipped: 'success',
      delivered: 'success',
      cancelled: 'danger',
      refunded: 'secondary'
    };
    return colors[status as keyof typeof colors] || 'secondary';
  };

  const getPaymentStatusColor = (status: string) => {
    const colors = {
      pending: 'warning',
      paid: 'success',
      failed: 'danger',
      refunded: 'secondary'
    };
    return colors[status as keyof typeof colors] || 'secondary';
  };

  const handlePrintInvoice = () => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Invoice - ${order.orderNumber}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              .invoice-details { margin-bottom: 20px; }
              .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
              .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              .table th { background-color: #f2f2f2; }
              .total { text-align: right; font-weight: bold; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>INVOICE</h1>
              <h2>${order.orderNumber}</h2>
            </div>
            <div class="invoice-details">
              <p><strong>Customer:</strong> ${order.customerName}</p>
              <p><strong>Email:</strong> ${order.customerEmail || 'N/A'}</p>
              <p><strong>Phone:</strong> ${order.customerPhone || 'N/A'}</p>
              <p><strong>Date:</strong> ${new Date(order.createdAt).toLocaleDateString()}</p>
              <p><strong>Status:</strong> ${order.status}</p>
              <p><strong>Payment Status:</strong> ${order.paymentStatus}</p>
            </div>
            <table class="table">
              <thead>
                <tr>
                  <th>Item</th>
                  <th>SKU</th>
                  <th>Price</th>
                  <th>Quantity</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody>
                ${order.items.map(item => `
                  <tr>
                    <td>${item.productName}</td>
                    <td>${item.sku}</td>
                    <td>$${item.price.toFixed(2)}</td>
                    <td>${item.quantity}</td>
                    <td>$${item.total.toFixed(2)}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
            <div class="total">
              <p>Subtotal: $${order.subtotal.toFixed(2)}</p>
              <p>Discount: -$${order.discount.toFixed(2)}</p>
              <p>Tax: $${order.tax.toFixed(2)}</p>
              <p><strong>Total: $${order.total.toFixed(2)}</strong></p>
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
    toast.success('Invoice printed successfully');
  };

  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-xl">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">Order Details - {order.orderNumber}</h5>
            <button type="button" className="btn-close" onClick={onClose}></button>
          </div>
          <div className="modal-body">
            {/* Order Header */}
            <div className="row mb-4">
              <div className="col-md-6">
                <h6 className="text-primary">Order Information</h6>
                <p className="mb-1"><strong>Order Number:</strong> {order.orderNumber}</p>
                <p className="mb-1"><strong>Date:</strong> {new Date(order.createdAt).toLocaleDateString()}</p>
                <p className="mb-1"><strong>Last Updated:</strong> {new Date(order.updatedAt).toLocaleDateString()}</p>
                <p className="mb-1">
                  <strong>Status:</strong>
                  <span className={`badge bg-${getStatusColor(order.status)} ms-2`}>
                    {order.status}
                  </span>
                </p>
                <p className="mb-1">
                  <strong>Payment:</strong>
                  <span className={`badge bg-${getPaymentStatusColor(order.paymentStatus)} ms-2`}>
                    {order.paymentStatus}
                  </span>
                </p>
              </div>
              <div className="col-md-6">
                <h6 className="text-success">Customer Information</h6>
                <p className="mb-1"><strong>Name:</strong> {order.customerName}</p>
                <p className="mb-1"><strong>Email:</strong> {order.customerEmail || 'N/A'}</p>
                <p className="mb-1"><strong>Phone:</strong> {order.customerPhone || 'N/A'}</p>
                <p className="mb-1"><strong>Payment Method:</strong> {order.paymentMethod || 'N/A'}</p>
              </div>
            </div>

            {/* Tabs */}
            <ul className="nav nav-tabs mb-3">
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'details' ? 'active' : ''}`}
                  onClick={() => setActiveTab('details')}
                >
                  📋 Order Items
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'addresses' ? 'active' : ''}`}
                  onClick={() => setActiveTab('addresses')}
                >
                  📍 Addresses
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'history' ? 'active' : ''}`}
                  onClick={() => setActiveTab('history')}
                >
                  📊 Order History
                </button>
              </li>
            </ul>

            {/* Tab Content */}
            {activeTab === 'details' && (
              <div>
                <div className="table-responsive">
                  <table className="table table-bordered">
                    <thead className="table-light">
                      <tr>
                        <th>Product</th>
                        <th>SKU</th>
                        <th>Price</th>
                        <th>Quantity</th>
                        <th>Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {order.items.map((item) => (
                        <tr key={item.id}>
                          <td>
                            <div className="fw-medium">{item.productName}</div>
                            <small className="text-muted">ID: {item.productId}</small>
                          </td>
                          <td>{item.sku}</td>
                          <td>${item.price.toFixed(2)}</td>
                          <td>{item.quantity}</td>
                          <td>${item.total.toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Order Summary */}
                <div className="row">
                  <div className="col-md-6">
                    {order.notes && (
                      <div>
                        <h6>Order Notes</h6>
                        <p className="text-muted">{order.notes}</p>
                      </div>
                    )}
                  </div>
                  <div className="col-md-6">
                    <div className="card">
                      <div className="card-body">
                        <h6 className="card-title">Order Summary</h6>
                        <div className="d-flex justify-content-between mb-2">
                          <span>Subtotal:</span>
                          <span>${order.subtotal.toFixed(2)}</span>
                        </div>
                        <div className="d-flex justify-content-between mb-2">
                          <span>Discount:</span>
                          <span className="text-success">-${order.discount.toFixed(2)}</span>
                        </div>
                        <div className="d-flex justify-content-between mb-2">
                          <span>Tax:</span>
                          <span>${order.tax.toFixed(2)}</span>
                        </div>
                        <hr />
                        <div className="d-flex justify-content-between">
                          <strong>Total:</strong>
                          <strong>${order.total.toFixed(2)}</strong>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'addresses' && (
              <div className="row">
                <div className="col-md-6">
                  <div className="card">
                    <div className="card-header">
                      <h6 className="mb-0">🏠 Billing Address</h6>
                    </div>
                    <div className="card-body">
                      {order.billingAddress ? (
                        <address className="mb-0">
                          {order.billingAddress}
                        </address>
                      ) : (
                        <p className="text-muted">No billing address provided</p>
                      )}
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="card">
                    <div className="card-header">
                      <h6 className="mb-0">🚚 Shipping Address</h6>
                    </div>
                    <div className="card-body">
                      {order.shippingAddress ? (
                        <address className="mb-0">
                          {order.shippingAddress}
                        </address>
                      ) : (
                        <p className="text-muted">No shipping address provided</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'history' && (
              <div>
                <div className="timeline">
                  <div className="timeline-item">
                    <div className="timeline-marker bg-success"></div>
                    <div className="timeline-content">
                      <h6 className="timeline-title">Order Created</h6>
                      <p className="timeline-text">Order {order.orderNumber} was created</p>
                      <small className="text-muted">{new Date(order.createdAt).toLocaleString()}</small>
                    </div>
                  </div>
                  {order.status !== 'pending' && (
                    <div className="timeline-item">
                      <div className="timeline-marker bg-info"></div>
                      <div className="timeline-content">
                        <h6 className="timeline-title">Status Updated</h6>
                        <p className="timeline-text">Order status changed to {order.status}</p>
                        <small className="text-muted">{new Date(order.updatedAt).toLocaleString()}</small>
                      </div>
                    </div>
                  )}
                  {order.paymentStatus === 'paid' && (
                    <div className="timeline-item">
                      <div className="timeline-marker bg-success"></div>
                      <div className="timeline-content">
                        <h6 className="timeline-title">Payment Received</h6>
                        <p className="timeline-text">Payment of ${order.total.toFixed(2)} received via {order.paymentMethod}</p>
                        <small className="text-muted">{new Date(order.updatedAt).toLocaleString()}</small>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          <div className="modal-footer">
            <button
              type="button"
              className="btn btn-outline-primary"
              onClick={handlePrintInvoice}
            >
              🖨️ Print Invoice
            </button>
            <button
              type="button"
              className="btn btn-outline-success"
              onClick={() => toast.success('Email sent to customer!')}
            >
              ✉️ Email Customer
            </button>
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientOrders;
