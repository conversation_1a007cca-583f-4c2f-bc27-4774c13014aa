import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface Product {
  id: string;
  name: string;
  sku: string;
  category: string;
  price: number;
  cost: number;
  stock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  barcode?: string;
  description?: string;
  supplier?: string;
  location?: string;
  status: 'active' | 'inactive' | 'discontinued';
  createdAt: string;
  updatedAt: string;
}

interface ProductFormData {
  name: string;
  sku: string;
  category: string;
  price: number;
  cost: number;
  stock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  barcode?: string;
  description?: string;
  supplier?: string;
  location?: string;
  status: 'active' | 'inactive' | 'discontinued';
}

const ClientInventory: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [showAddProduct, setShowAddProduct] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [view, setView] = useState<'grid' | 'list'>('list');

  // Load sample data
  useEffect(() => {
    const sampleProducts: Product[] = [
      {
        id: '1',
        name: 'Wireless Headphones',
        sku: 'WH001',
        category: 'Electronics',
        price: 99.99,
        cost: 60.00,
        stock: 45,
        minStock: 10,
        maxStock: 100,
        unit: 'pcs',
        barcode: '123456789012',
        description: 'High-quality wireless headphones with noise cancellation',
        supplier: 'TechCorp',
        location: 'A1-B2',
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-15T00:00:00Z'
      },
      {
        id: '2',
        name: 'Coffee Mug',
        sku: 'CM001',
        category: 'Home & Garden',
        price: 12.99,
        cost: 5.00,
        stock: 8,
        minStock: 15,
        maxStock: 50,
        unit: 'pcs',
        barcode: '987654321098',
        description: 'Ceramic coffee mug with company logo',
        supplier: 'HomeGoods Inc',
        location: 'B3-C1',
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-10T00:00:00Z'
      },
      {
        id: '3',
        name: 'Office Chair',
        sku: 'OC001',
        category: 'Furniture',
        price: 299.99,
        cost: 180.00,
        stock: 12,
        minStock: 5,
        maxStock: 25,
        unit: 'pcs',
        description: 'Ergonomic office chair with lumbar support',
        supplier: 'Office Solutions',
        location: 'C1-D2',
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-12T00:00:00Z'
      }
    ];
    setProducts(sampleProducts);
    setFilteredProducts(sampleProducts);
  }, []);

  // Filter products
  useEffect(() => {
    let filtered = products;

    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.barcode?.includes(searchTerm)
      );
    }

    if (categoryFilter) {
      filtered = filtered.filter(product => product.category === categoryFilter);
    }

    if (statusFilter) {
      filtered = filtered.filter(product => product.status === statusFilter);
    }

    setFilteredProducts(filtered);
  }, [products, searchTerm, categoryFilter, statusFilter]);

  const handleAddProduct = () => {
    setEditingProduct(null);
    setShowAddProduct(true);
  };

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setShowAddProduct(true);
  };

  const handleDeleteProduct = (productId: string) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      setProducts(prev => prev.filter(p => p.id !== productId));
      toast.success('Product deleted successfully');
    }
  };

  const handleSaveProduct = (productData: ProductFormData) => {
    if (editingProduct) {
      setProducts(prev => prev.map(p => 
        p.id === editingProduct.id 
          ? { ...p, ...productData, updatedAt: new Date().toISOString() }
          : p
      ));
      toast.success('Product updated successfully');
    } else {
      const newProduct: Product = {
        id: (products.length + 1).toString(),
        ...productData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      setProducts(prev => [...prev, newProduct]);
      toast.success('Product added successfully');
    }
    setShowAddProduct(false);
  };

  const getStockStatus = (product: Product) => {
    if (product.stock <= 0) return { status: 'out-of-stock', color: 'danger', text: 'Out of Stock' };
    if (product.stock <= product.minStock) return { status: 'low-stock', color: 'warning', text: 'Low Stock' };
    if (product.stock >= product.maxStock) return { status: 'overstock', color: 'info', text: 'Overstock' };
    return { status: 'in-stock', color: 'success', text: 'In Stock' };
  };

  const getCategories = () => {
    return [...new Set(products.map(p => p.category))];
  };

  const getLowStockCount = () => {
    return products.filter(p => p.stock <= p.minStock).length;
  };

  const getOutOfStockCount = () => {
    return products.filter(p => p.stock <= 0).length;
  };

  const getTotalValue = () => {
    return products.reduce((sum, p) => sum + (p.stock * p.cost), 0);
  };

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Inventory Management</h1>
          <p className="text-muted mb-0">Manage your product inventory and stock levels</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-primary"
            onClick={() => toast.success('Inventory report coming soon!')}
          >
            📊 Reports
          </button>
          <button
            className="btn btn-outline-success"
            onClick={() => toast.success('Stock adjustment coming soon!')}
          >
            📦 Stock Adjustment
          </button>
          <button
            className="btn btn-primary"
            onClick={handleAddProduct}
          >
            ➕ Add Product
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary bg-opacity-10 p-3 rounded">
                    <span className="text-primary fs-4">📦</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Products</h6>
                  <h4 className="mb-0">{products.length}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-warning bg-opacity-10 p-3 rounded">
                    <span className="text-warning fs-4">⚠️</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Low Stock Items</h6>
                  <h4 className="mb-0">{getLowStockCount()}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-danger bg-opacity-10 p-3 rounded">
                    <span className="text-danger fs-4">❌</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Out of Stock</h6>
                  <h4 className="mb-0">{getOutOfStockCount()}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-success bg-opacity-10 p-3 rounded">
                    <span className="text-success fs-4">💰</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Value</h6>
                  <h4 className="mb-0">${getTotalValue().toFixed(2)}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="card mb-4">
        <div className="card-body">
          <div className="row align-items-center">
            <div className="col-md-4">
              <input
                type="text"
                className="form-control"
                placeholder="Search products, SKU, or barcode..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="col-md-2">
              <select
                className="form-select"
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
              >
                <option value="">All Categories</option>
                {getCategories().map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
            <div className="col-md-2">
              <select
                className="form-select"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="discontinued">Discontinued</option>
              </select>
            </div>
            <div className="col-md-2">
              <div className="btn-group w-100">
                <button
                  className={`btn ${view === 'list' ? 'btn-primary' : 'btn-outline-primary'}`}
                  onClick={() => setView('list')}
                >
                  📋 List
                </button>
                <button
                  className={`btn ${view === 'grid' ? 'btn-primary' : 'btn-outline-primary'}`}
                  onClick={() => setView('grid')}
                >
                  ⊞ Grid
                </button>
              </div>
            </div>
            <div className="col-md-2">
              <button
                className="btn btn-outline-secondary w-100"
                onClick={() => {
                  setSearchTerm('');
                  setCategoryFilter('');
                  setStatusFilter('');
                }}
              >
                🔄 Clear Filters
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Products List/Grid */}
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">Products ({filteredProducts.length})</h5>
        </div>
        <div className="card-body p-0">
          {filteredProducts.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <span className="display-1">📦</span>
              </div>
              <h5>No Products Found</h5>
              <p className="text-muted">
                {searchTerm || categoryFilter || statusFilter 
                  ? 'Try adjusting your filters' 
                  : 'Add your first product to get started'
                }
              </p>
              {!searchTerm && !categoryFilter && !statusFilter && (
                <button className="btn btn-primary" onClick={handleAddProduct}>
                  Add Product
                </button>
              )}
            </div>
          ) : view === 'list' ? (
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead className="table-light">
                  <tr>
                    <th>Product</th>
                    <th>Category</th>
                    <th>Price</th>
                    <th>Stock</th>
                    <th>Status</th>
                    <th>Location</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredProducts.map((product) => {
                    const stockStatus = getStockStatus(product);
                    return (
                      <tr key={product.id}>
                        <td>
                          <div>
                            <div className="fw-medium">{product.name}</div>
                            <small className="text-muted">SKU: {product.sku}</small>
                          </div>
                        </td>
                        <td>
                          <span className="badge bg-light text-dark">{product.category}</span>
                        </td>
                        <td>
                          <div>
                            <div className="fw-medium">${product.price}</div>
                            <small className="text-muted">Cost: ${product.cost}</small>
                          </div>
                        </td>
                        <td>
                          <div>
                            <span className={`badge bg-${stockStatus.color}`}>
                              {product.stock} {product.unit}
                            </span>
                            <br />
                            <small className="text-muted">Min: {product.minStock}</small>
                          </div>
                        </td>
                        <td>
                          <span className={`badge bg-${product.status === 'active' ? 'success' : 'secondary'}`}>
                            {product.status}
                          </span>
                        </td>
                        <td>
                          <span className="text-muted">{product.location || 'N/A'}</span>
                        </td>
                        <td>
                          <div className="btn-group btn-group-sm">
                            <button
                              className="btn btn-outline-primary"
                              onClick={() => handleEditProduct(product)}
                            >
                              ✏️ Edit
                            </button>
                            <button
                              className="btn btn-outline-success"
                              onClick={() => toast.success('Stock adjustment coming soon!')}
                            >
                              📦 Adjust
                            </button>
                            <button
                              className="btn btn-outline-danger"
                              onClick={() => handleDeleteProduct(product.id)}
                            >
                              🗑️ Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="row g-3 p-3">
              {filteredProducts.map((product) => {
                const stockStatus = getStockStatus(product);
                return (
                  <div key={product.id} className="col-md-4 col-lg-3">
                    <div className="card h-100">
                      <div className="card-body">
                        <h6 className="card-title">{product.name}</h6>
                        <p className="card-text">
                          <small className="text-muted">SKU: {product.sku}</small><br />
                          <span className="fw-bold">${product.price}</span><br />
                          <span className={`badge bg-${stockStatus.color} mt-1`}>
                            {product.stock} {product.unit}
                          </span>
                        </p>
                        <div className="btn-group w-100">
                          <button
                            className="btn btn-outline-primary btn-sm"
                            onClick={() => handleEditProduct(product)}
                          >
                            ✏️ Edit
                          </button>
                          <button
                            className="btn btn-outline-danger btn-sm"
                            onClick={() => handleDeleteProduct(product.id)}
                          >
                            🗑️
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Add/Edit Product Modal */}
      {showAddProduct && (
        <ProductModal
          product={editingProduct}
          onSave={handleSaveProduct}
          onCancel={() => setShowAddProduct(false)}
        />
      )}
    </div>
  );
};

// Product Modal Component
const ProductModal: React.FC<{
  product: Product | null;
  onSave: (data: ProductFormData) => void;
  onCancel: () => void;
}> = ({ product, onSave, onCancel }) => {
  const [formData, setFormData] = useState<ProductFormData>({
    name: product?.name || '',
    sku: product?.sku || '',
    category: product?.category || '',
    price: product?.price || 0,
    cost: product?.cost || 0,
    stock: product?.stock || 0,
    minStock: product?.minStock || 0,
    maxStock: product?.maxStock || 0,
    unit: product?.unit || 'pcs',
    barcode: product?.barcode || '',
    description: product?.description || '',
    supplier: product?.supplier || '',
    location: product?.location || '',
    status: product?.status || 'active'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.sku.trim()) {
      newErrors.sku = 'SKU is required';
    }

    if (!formData.category.trim()) {
      newErrors.category = 'Category is required';
    }

    if (formData.price <= 0) {
      newErrors.price = 'Price must be greater than 0';
    }

    if (formData.cost < 0) {
      newErrors.cost = 'Cost cannot be negative';
    }

    if (formData.stock < 0) {
      newErrors.stock = 'Stock cannot be negative';
    }

    if (formData.minStock < 0) {
      newErrors.minStock = 'Minimum stock cannot be negative';
    }

    if (formData.maxStock <= formData.minStock) {
      newErrors.maxStock = 'Maximum stock must be greater than minimum stock';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSave(formData);
    }
  };

  const handleInputChange = (field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const generateSKU = () => {
    const prefix = formData.category.substring(0, 2).toUpperCase();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const sku = `${prefix}${random}`;
    handleInputChange('sku', sku);
  };

  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-xl">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">
              {product ? 'Edit Product' : 'Add Product'}
            </h5>
            <button type="button" className="btn-close" onClick={onCancel}></button>
          </div>
          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              <div className="row">
                {/* Basic Information */}
                <div className="col-md-6">
                  <div className="card h-100">
                    <div className="card-header">
                      <h6 className="mb-0">Basic Information</h6>
                    </div>
                    <div className="card-body">
                      <div className="mb-3">
                        <label className="form-label">Product Name *</label>
                        <input
                          type="text"
                          className={`form-control ${errors.name ? 'is-invalid' : ''}`}
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          placeholder="Enter product name"
                        />
                        {errors.name && <div className="invalid-feedback">{errors.name}</div>}
                      </div>

                      <div className="mb-3">
                        <label className="form-label">SKU *</label>
                        <div className="input-group">
                          <input
                            type="text"
                            className={`form-control ${errors.sku ? 'is-invalid' : ''}`}
                            value={formData.sku}
                            onChange={(e) => handleInputChange('sku', e.target.value.toUpperCase())}
                            placeholder="Product SKU"
                          />
                          <button
                            type="button"
                            className="btn btn-outline-secondary"
                            onClick={generateSKU}
                            disabled={!formData.category}
                          >
                            Generate
                          </button>
                        </div>
                        {errors.sku && <div className="invalid-feedback">{errors.sku}</div>}
                      </div>

                      <div className="mb-3">
                        <label className="form-label">Category *</label>
                        <input
                          type="text"
                          className={`form-control ${errors.category ? 'is-invalid' : ''}`}
                          value={formData.category}
                          onChange={(e) => handleInputChange('category', e.target.value)}
                          placeholder="Product category"
                          list="categories"
                        />
                        <datalist id="categories">
                          <option value="Electronics" />
                          <option value="Home & Garden" />
                          <option value="Furniture" />
                          <option value="Clothing" />
                          <option value="Books" />
                          <option value="Sports" />
                        </datalist>
                        {errors.category && <div className="invalid-feedback">{errors.category}</div>}
                      </div>

                      <div className="mb-3">
                        <label className="form-label">Description</label>
                        <textarea
                          className="form-control"
                          rows={3}
                          value={formData.description}
                          onChange={(e) => handleInputChange('description', e.target.value)}
                          placeholder="Product description"
                        />
                      </div>

                      <div className="mb-3">
                        <label className="form-label">Status</label>
                        <select
                          className="form-select"
                          value={formData.status}
                          onChange={(e) => handleInputChange('status', e.target.value)}
                        >
                          <option value="active">Active</option>
                          <option value="inactive">Inactive</option>
                          <option value="discontinued">Discontinued</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Pricing & Inventory */}
                <div className="col-md-6">
                  <div className="card h-100">
                    <div className="card-header">
                      <h6 className="mb-0">Pricing & Inventory</h6>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6 mb-3">
                          <label className="form-label">Selling Price *</label>
                          <div className="input-group">
                            <span className="input-group-text">$</span>
                            <input
                              type="number"
                              className={`form-control ${errors.price ? 'is-invalid' : ''}`}
                              value={formData.price}
                              onChange={(e) => handleInputChange('price', Number(e.target.value))}
                              step="0.01"
                              min="0"
                            />
                          </div>
                          {errors.price && <div className="invalid-feedback">{errors.price}</div>}
                        </div>
                        <div className="col-md-6 mb-3">
                          <label className="form-label">Cost Price</label>
                          <div className="input-group">
                            <span className="input-group-text">$</span>
                            <input
                              type="number"
                              className={`form-control ${errors.cost ? 'is-invalid' : ''}`}
                              value={formData.cost}
                              onChange={(e) => handleInputChange('cost', Number(e.target.value))}
                              step="0.01"
                              min="0"
                            />
                          </div>
                          {errors.cost && <div className="invalid-feedback">{errors.cost}</div>}
                        </div>
                      </div>

                      <div className="row">
                        <div className="col-md-6 mb-3">
                          <label className="form-label">Current Stock</label>
                          <div className="input-group">
                            <input
                              type="number"
                              className={`form-control ${errors.stock ? 'is-invalid' : ''}`}
                              value={formData.stock}
                              onChange={(e) => handleInputChange('stock', Number(e.target.value))}
                              min="0"
                            />
                            <select
                              className="form-select"
                              value={formData.unit}
                              onChange={(e) => handleInputChange('unit', e.target.value)}
                              style={{ maxWidth: '100px' }}
                            >
                              <option value="pcs">pcs</option>
                              <option value="kg">kg</option>
                              <option value="lbs">lbs</option>
                              <option value="liters">liters</option>
                              <option value="boxes">boxes</option>
                            </select>
                          </div>
                          {errors.stock && <div className="invalid-feedback">{errors.stock}</div>}
                        </div>
                        <div className="col-md-6 mb-3">
                          <label className="form-label">Barcode</label>
                          <input
                            type="text"
                            className="form-control"
                            value={formData.barcode}
                            onChange={(e) => handleInputChange('barcode', e.target.value)}
                            placeholder="Product barcode"
                          />
                        </div>
                      </div>

                      <div className="row">
                        <div className="col-md-6 mb-3">
                          <label className="form-label">Minimum Stock</label>
                          <input
                            type="number"
                            className={`form-control ${errors.minStock ? 'is-invalid' : ''}`}
                            value={formData.minStock}
                            onChange={(e) => handleInputChange('minStock', Number(e.target.value))}
                            min="0"
                          />
                          {errors.minStock && <div className="invalid-feedback">{errors.minStock}</div>}
                        </div>
                        <div className="col-md-6 mb-3">
                          <label className="form-label">Maximum Stock</label>
                          <input
                            type="number"
                            className={`form-control ${errors.maxStock ? 'is-invalid' : ''}`}
                            value={formData.maxStock}
                            onChange={(e) => handleInputChange('maxStock', Number(e.target.value))}
                            min="0"
                          />
                          {errors.maxStock && <div className="invalid-feedback">{errors.maxStock}</div>}
                        </div>
                      </div>

                      <div className="row">
                        <div className="col-md-6 mb-3">
                          <label className="form-label">Supplier</label>
                          <input
                            type="text"
                            className="form-control"
                            value={formData.supplier}
                            onChange={(e) => handleInputChange('supplier', e.target.value)}
                            placeholder="Supplier name"
                          />
                        </div>
                        <div className="col-md-6 mb-3">
                          <label className="form-label">Location</label>
                          <input
                            type="text"
                            className="form-control"
                            value={formData.location}
                            onChange={(e) => handleInputChange('location', e.target.value)}
                            placeholder="A1-B2"
                          />
                        </div>
                      </div>

                      {/* Profit Margin Display */}
                      {formData.price > 0 && formData.cost > 0 && (
                        <div className="alert alert-info">
                          <strong>Profit Margin:</strong> {(((formData.price - formData.cost) / formData.price) * 100).toFixed(2)}%
                          <br />
                          <strong>Profit per Unit:</strong> ${(formData.price - formData.cost).toFixed(2)}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onCancel}>
                Cancel
              </button>
              <button type="submit" className="btn btn-primary">
                {product ? 'Update Product' : 'Add Product'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ClientInventory;
