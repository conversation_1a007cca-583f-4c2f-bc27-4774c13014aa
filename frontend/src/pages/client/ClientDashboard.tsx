import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { ClientDashboardStats, ClientModule, CLIENT_MODULES } from '../../types/client';

const ClientDashboard: React.FC = () => {
  const [stats, setStats] = useState<ClientDashboardStats | null>(null);
  const [availableModules, setAvailableModules] = useState<ClientModule[]>([]);
  const [recentActivities, setRecentActivities] = useState<any[]>([]);
  const [notifications, setNotifications] = useState<any[]>([]);

  // Load dashboard data
  useEffect(() => {
    // Sample stats data
    const sampleStats: ClientDashboardStats = {
      clientId: 'client_1',
      totalProducts: 1247,
      totalOrders: 89,
      totalRevenue: 45678.90,
      lowStockItems: 12,
      activeUsers: 8,
      warehouseLocations: 3,
      posTerminals: 5,
      lastSyncTime: new Date().toISOString(),
      period: 'today',
      updatedAt: new Date().toISOString()
    };

    // Filter modules based on user role (for demo, show all)
    const userRole = 'client_admin'; // This would come from auth context
    const filteredModules = CLIENT_MODULES.filter(module => 
      module.enabled && module.requiredRole.includes(userRole as any)
    );

    // Sample activities
    const sampleActivities = [
      {
        id: '1',
        user: 'John Doe',
        action: 'Added new product',
        module: 'Inventory',
        time: '2 minutes ago',
        icon: '📦'
      },
      {
        id: '2',
        user: 'Jane Smith',
        action: 'Processed order #1234',
        module: 'POS',
        time: '15 minutes ago',
        icon: '💰'
      },
      {
        id: '3',
        user: 'Mike Johnson',
        action: 'Updated warehouse location',
        module: 'Warehouse',
        time: '1 hour ago',
        icon: '🏭'
      }
    ];

    // Sample notifications
    const sampleNotifications = [
      {
        id: '1',
        type: 'warning',
        title: 'Low Stock Alert',
        message: '12 products are running low on stock',
        time: '5 minutes ago'
      },
      {
        id: '2',
        type: 'info',
        title: 'System Update',
        message: 'New features available in inventory module',
        time: '2 hours ago'
      },
      {
        id: '3',
        type: 'success',
        title: 'Backup Completed',
        message: 'Daily backup completed successfully',
        time: '1 day ago'
      }
    ];

    setStats(sampleStats);
    setAvailableModules(filteredModules);
    setRecentActivities(sampleActivities);
    setNotifications(sampleNotifications);
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getNotificationIcon = (type: string) => {
    const icons = {
      info: '💡',
      warning: '⚠️',
      error: '❌',
      success: '✅'
    };
    return icons[type as keyof typeof icons] || '📢';
  };

  const getNotificationColor = (type: string) => {
    const colors = {
      info: 'text-info',
      warning: 'text-warning',
      error: 'text-danger',
      success: 'text-success'
    };
    return colors[type as keyof typeof colors] || 'text-primary';
  };

  if (!stats) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Client Dashboard</h1>
          <p className="text-muted mb-0">Welcome to your inventory management system</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-primary"
            onClick={() => toast.success('Sync completed!')}
          >
            🔄 Sync Data
          </button>
          <button
            className="btn btn-primary"
            onClick={() => toast.success('Quick action menu coming soon!')}
          >
            ⚡ Quick Actions
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-lg-3 col-md-6 mb-3">
          <div className="card h-100">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary bg-opacity-10 p-3 rounded">
                    <span className="text-primary fs-4">📦</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0 text-muted">Total Products</h6>
                  <h4 className="mb-0">{stats.totalProducts.toLocaleString()}</h4>
                  <small className="text-success">+12% from last month</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-lg-3 col-md-6 mb-3">
          <div className="card h-100">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-success bg-opacity-10 p-3 rounded">
                    <span className="text-success fs-4">📋</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0 text-muted">Today's Orders</h6>
                  <h4 className="mb-0">{stats.totalOrders}</h4>
                  <small className="text-success">+8% from yesterday</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-lg-3 col-md-6 mb-3">
          <div className="card h-100">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-warning bg-opacity-10 p-3 rounded">
                    <span className="text-warning fs-4">💰</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0 text-muted">Today's Revenue</h6>
                  <h4 className="mb-0">{formatCurrency(stats.totalRevenue)}</h4>
                  <small className="text-success">+15% from yesterday</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-lg-3 col-md-6 mb-3">
          <div className="card h-100">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-danger bg-opacity-10 p-3 rounded">
                    <span className="text-danger fs-4">⚠️</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0 text-muted">Low Stock Items</h6>
                  <h4 className="mb-0">{stats.lowStockItems}</h4>
                  <small className="text-danger">Requires attention</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="row">
        {/* Quick Access Modules */}
        <div className="col-lg-8 mb-4">
          <div className="card">
            <div className="card-header">
              <h5 className="mb-0">Quick Access</h5>
            </div>
            <div className="card-body">
              <div className="row">
                {availableModules.slice(0, 6).map((module) => (
                  <div key={module.id} className="col-md-4 mb-3">
                    <Link
                      to={module.path}
                      className="text-decoration-none"
                    >
                      <div className="card h-100 border-0 bg-light">
                        <div className="card-body text-center">
                          <div className="mb-2">
                            <span className="fs-1">{module.icon}</span>
                          </div>
                          <h6 className="mb-1">{module.name}</h6>
                          <small className="text-muted">{module.description}</small>
                        </div>
                      </div>
                    </Link>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div className="col-lg-4 mb-4">
          <div className="card">
            <div className="card-header">
              <h5 className="mb-0">System Status</h5>
            </div>
            <div className="card-body">
              <div className="d-flex justify-content-between align-items-center mb-3">
                <span>Active Users</span>
                <span className="badge bg-success">{stats.activeUsers}</span>
              </div>
              <div className="d-flex justify-content-between align-items-center mb-3">
                <span>Warehouses</span>
                <span className="badge bg-primary">{stats.warehouseLocations}</span>
              </div>
              <div className="d-flex justify-content-between align-items-center mb-3">
                <span>POS Terminals</span>
                <span className="badge bg-info">{stats.posTerminals}</span>
              </div>
              <div className="d-flex justify-content-between align-items-center">
                <span>Last Sync</span>
                <small className="text-muted">
                  {stats.lastSyncTime ? new Date(stats.lastSyncTime).toLocaleTimeString() : 'Never'}
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="row">
        {/* Recent Activities */}
        <div className="col-lg-6 mb-4">
          <div className="card">
            <div className="card-header d-flex justify-content-between align-items-center">
              <h5 className="mb-0">Recent Activities</h5>
              <Link to="/client/activities" className="btn btn-sm btn-outline-primary">
                View All
              </Link>
            </div>
            <div className="card-body">
              {recentActivities.length === 0 ? (
                <div className="text-center py-3">
                  <span className="text-muted">No recent activities</span>
                </div>
              ) : (
                <div className="list-group list-group-flush">
                  {recentActivities.map((activity) => (
                    <div key={activity.id} className="list-group-item border-0 px-0">
                      <div className="d-flex align-items-center">
                        <span className="me-3 fs-5">{activity.icon}</span>
                        <div className="flex-grow-1">
                          <div className="fw-medium">{activity.action}</div>
                          <small className="text-muted">
                            by {activity.user} in {activity.module} • {activity.time}
                          </small>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Notifications */}
        <div className="col-lg-6 mb-4">
          <div className="card">
            <div className="card-header d-flex justify-content-between align-items-center">
              <h5 className="mb-0">Notifications</h5>
              <Link to="/client/notifications" className="btn btn-sm btn-outline-primary">
                View All
              </Link>
            </div>
            <div className="card-body">
              {notifications.length === 0 ? (
                <div className="text-center py-3">
                  <span className="text-muted">No notifications</span>
                </div>
              ) : (
                <div className="list-group list-group-flush">
                  {notifications.map((notification) => (
                    <div key={notification.id} className="list-group-item border-0 px-0">
                      <div className="d-flex align-items-start">
                        <span className={`me-3 fs-5 ${getNotificationColor(notification.type)}`}>
                          {getNotificationIcon(notification.type)}
                        </span>
                        <div className="flex-grow-1">
                          <div className="fw-medium">{notification.title}</div>
                          <div className="text-muted small">{notification.message}</div>
                          <small className="text-muted">{notification.time}</small>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientDashboard;
