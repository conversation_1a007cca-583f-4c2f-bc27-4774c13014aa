import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { POSTerminal, POSTerminalFormData } from '../../types/client';

const POSPanel: React.FC = () => {
  const [terminals, setTerminals] = useState<POSTerminal[]>([]);
  const [selectedTerminal, setSelectedTerminal] = useState<POSTerminal | null>(null);
  const [showAddTerminal, setShowAddTerminal] = useState(false);
  const [editingTerminal, setEditingTerminal] = useState<POSTerminal | null>(null);
  const [view, setView] = useState<'list' | 'terminal'>('list');

  // Load sample data
  useEffect(() => {
    const sampleTerminals: POSTerminal[] = [
      {
        id: '1',
        clientId: 'client_1',
        warehouseId: '1',
        name: 'Main Store Terminal 1',
        code: 'POS001',
        location: 'Main Store - Counter 1',
        isActive: true,
        status: 'online',
        cashierId: 'user_3',
        lastTransaction: '2024-01-15T14:30:00Z',
        dailySales: {
          amount: 2450.75,
          transactions: 28,
          date: '2024-01-15'
        },
        hardware: {
          printer: true,
          scanner: true,
          cashDrawer: true,
          cardReader: true,
          display: true
        },
        settings: {
          allowDiscounts: true,
          allowReturns: true,
          requireManagerApproval: false,
          autoOpenDrawer: true,
          printReceipts: true
        },
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-15T14:30:00Z'
      },
      {
        id: '2',
        clientId: 'client_1',
        warehouseId: '1',
        name: 'Main Store Terminal 2',
        code: 'POS002',
        location: 'Main Store - Counter 2',
        isActive: true,
        status: 'online',
        cashierId: 'user_4',
        lastTransaction: '2024-01-15T13:45:00Z',
        dailySales: {
          amount: 1890.25,
          transactions: 22,
          date: '2024-01-15'
        },
        hardware: {
          printer: true,
          scanner: true,
          cashDrawer: true,
          cardReader: true,
          display: false
        },
        settings: {
          allowDiscounts: false,
          allowReturns: true,
          requireManagerApproval: true,
          autoOpenDrawer: true,
          printReceipts: true
        },
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-15T13:45:00Z'
      },
      {
        id: '3',
        clientId: 'client_1',
        warehouseId: '2',
        name: 'Branch Store Terminal',
        code: 'POS003',
        location: 'Branch Store - Main Counter',
        isActive: true,
        status: 'offline',
        lastTransaction: '2024-01-14T18:20:00Z',
        dailySales: {
          amount: 0,
          transactions: 0,
          date: '2024-01-15'
        },
        hardware: {
          printer: true,
          scanner: false,
          cashDrawer: true,
          cardReader: false,
          display: false
        },
        settings: {
          allowDiscounts: true,
          allowReturns: false,
          requireManagerApproval: true,
          autoOpenDrawer: false,
          printReceipts: true
        },
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-14T18:20:00Z'
      }
    ];
    setTerminals(sampleTerminals);
  }, []);

  const handleAddTerminal = () => {
    setEditingTerminal(null);
    setShowAddTerminal(true);
  };

  const handleEditTerminal = (terminal: POSTerminal) => {
    setEditingTerminal(terminal);
    setShowAddTerminal(true);
  };

  const handleDeleteTerminal = (terminalId: string) => {
    if (window.confirm('Are you sure you want to delete this POS terminal?')) {
      setTerminals(prev => prev.filter(t => t.id !== terminalId));
      toast.success('POS terminal deleted successfully');
    }
  };

  const handleToggleTerminal = (terminalId: string) => {
    setTerminals(prev => prev.map(terminal => 
      terminal.id === terminalId 
        ? { ...terminal, isActive: !terminal.isActive }
        : terminal
    ));
    toast.success('Terminal status updated');
  };

  const handleOpenTerminal = (terminal: POSTerminal) => {
    setSelectedTerminal(terminal);
    setView('terminal');
  };

  const getStatusColor = (status: string) => {
    const colors = {
      online: 'success',
      offline: 'danger',
      maintenance: 'warning'
    };
    return colors[status as keyof typeof colors] || 'secondary';
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      online: '🟢',
      offline: '🔴',
      maintenance: '🟡'
    };
    return icons[status as keyof typeof icons] || '⚪';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getTotalDailySales = () => {
    return terminals.reduce((sum, terminal) => sum + (terminal.dailySales?.amount || 0), 0);
  };

  const getTotalTransactions = () => {
    return terminals.reduce((sum, terminal) => sum + (terminal.dailySales?.transactions || 0), 0);
  };

  if (view === 'terminal' && selectedTerminal) {
    return (
      <POSTerminalInterface
        terminal={selectedTerminal}
        onBack={() => setView('list')}
      />
    );
  }

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">POS Management</h1>
          <p className="text-muted mb-0">Manage point of sale terminals and transactions</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-primary"
            onClick={() => toast.success('POS analytics coming soon!')}
          >
            📊 Analytics
          </button>
          <button
            className="btn btn-outline-success"
            onClick={() => toast.success('Sales report coming soon!')}
          >
            📈 Sales Report
          </button>
          <button
            className="btn btn-primary"
            onClick={handleAddTerminal}
          >
            ➕ Add Terminal
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary bg-opacity-10 p-3 rounded">
                    <span className="text-primary fs-4">💰</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Terminals</h6>
                  <h4 className="mb-0">{terminals.length}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-success bg-opacity-10 p-3 rounded">
                    <span className="text-success fs-4">🟢</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Online Terminals</h6>
                  <h4 className="mb-0">{terminals.filter(t => t.status === 'online').length}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-warning bg-opacity-10 p-3 rounded">
                    <span className="text-warning fs-4">💵</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Today's Sales</h6>
                  <h4 className="mb-0">{formatCurrency(getTotalDailySales())}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-info bg-opacity-10 p-3 rounded">
                    <span className="text-info fs-4">🧾</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Transactions</h6>
                  <h4 className="mb-0">{getTotalTransactions()}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Terminals List */}
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">POS Terminals</h5>
        </div>
        <div className="card-body p-0">
          {terminals.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <span className="display-1">💰</span>
              </div>
              <h5>No POS Terminals Configured</h5>
              <p className="text-muted">Add your first POS terminal to start processing sales</p>
              <button className="btn btn-primary" onClick={handleAddTerminal}>
                Add POS Terminal
              </button>
            </div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead className="table-light">
                  <tr>
                    <th>Terminal</th>
                    <th>Location</th>
                    <th>Status</th>
                    <th>Cashier</th>
                    <th>Today's Sales</th>
                    <th>Transactions</th>
                    <th>Last Activity</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {terminals.map((terminal) => (
                    <tr key={terminal.id}>
                      <td>
                        <div>
                          <div className="fw-medium">{terminal.name}</div>
                          <small className="text-muted">{terminal.code}</small>
                        </div>
                      </td>
                      <td>
                        <span className="text-muted">{terminal.location}</span>
                      </td>
                      <td>
                        <div className="d-flex align-items-center">
                          <span className="me-1">{getStatusIcon(terminal.status)}</span>
                          <span className={`badge bg-${getStatusColor(terminal.status)}`}>
                            {terminal.status}
                          </span>
                        </div>
                      </td>
                      <td>
                        {terminal.cashierId ? (
                          <span className="badge bg-light text-dark">
                            User {terminal.cashierId}
                          </span>
                        ) : (
                          <span className="text-muted">Unassigned</span>
                        )}
                      </td>
                      <td>
                        <div className="fw-medium">
                          {formatCurrency(terminal.dailySales?.amount || 0)}
                        </div>
                      </td>
                      <td>
                        <span className="badge bg-primary">
                          {terminal.dailySales?.transactions || 0}
                        </span>
                      </td>
                      <td>
                        {terminal.lastTransaction ? (
                          <small className="text-muted">
                            {new Date(terminal.lastTransaction).toLocaleString()}
                          </small>
                        ) : (
                          <small className="text-muted">Never</small>
                        )}
                      </td>
                      <td>
                        <div className="btn-group btn-group-sm">
                          <button
                            className="btn btn-outline-primary"
                            onClick={() => handleOpenTerminal(terminal)}
                            disabled={terminal.status === 'offline'}
                          >
                            🖥️ Open
                          </button>
                          <button
                            className="btn btn-outline-secondary"
                            onClick={() => handleEditTerminal(terminal)}
                          >
                            ✏️ Edit
                          </button>
                          <button
                            className={`btn ${terminal.isActive ? 'btn-outline-warning' : 'btn-outline-success'}`}
                            onClick={() => handleToggleTerminal(terminal.id)}
                          >
                            {terminal.isActive ? '⏸️ Disable' : '▶️ Enable'}
                          </button>
                          <button
                            className="btn btn-outline-danger"
                            onClick={() => handleDeleteTerminal(terminal.id)}
                          >
                            🗑️ Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Add/Edit Terminal Modal */}
      {showAddTerminal && (
        <POSTerminalModal
          terminal={editingTerminal}
          onSave={(terminalData) => {
            if (editingTerminal) {
              // Update existing terminal
              setTerminals(prev => prev.map(t => 
                t.id === editingTerminal.id 
                  ? { ...t, ...terminalData, updatedAt: new Date().toISOString() }
                  : t
              ));
              toast.success('POS terminal updated successfully');
            } else {
              // Add new terminal
              const newTerminal: POSTerminal = {
                id: (terminals.length + 1).toString(),
                clientId: 'client_1',
                ...terminalData,
                status: 'offline',
                dailySales: {
                  amount: 0,
                  transactions: 0,
                  date: new Date().toISOString().split('T')[0]
                },
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
              };
              setTerminals(prev => [...prev, newTerminal]);
              toast.success('POS terminal added successfully');
            }
            setShowAddTerminal(false);
          }}
          onCancel={() => setShowAddTerminal(false)}
        />
      )}
    </div>
  );
};

// POS Terminal Interface Component
const POSTerminalInterface: React.FC<{
  terminal: POSTerminal;
  onBack: () => void;
}> = ({ terminal, onBack }) => {
  const [cart, setCart] = useState<any[]>([]);
  const [searchProduct, setSearchProduct] = useState('');
  const [customerInfo, setCustomerInfo] = useState({ name: '', phone: '', email: '' });
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [discount, setDiscount] = useState(0);
  const [showPayment, setShowPayment] = useState(false);

  // Sample products
  const sampleProducts = [
    { id: '1', name: 'Product A', price: 29.99, barcode: '123456789', stock: 50 },
    { id: '2', name: 'Product B', price: 19.99, barcode: '987654321', stock: 30 },
    { id: '3', name: 'Product C', price: 39.99, barcode: '456789123', stock: 25 },
  ];

  const addToCart = (product: any) => {
    const existingItem = cart.find(item => item.id === product.id);
    if (existingItem) {
      setCart(prev => prev.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart(prev => [...prev, { ...product, quantity: 1 }]);
    }
  };

  const removeFromCart = (productId: string) => {
    setCart(prev => prev.filter(item => item.id !== productId));
  };

  const updateQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId);
    } else {
      setCart(prev => prev.map(item =>
        item.id === productId
          ? { ...item, quantity }
          : item
      ));
    }
  };

  const getSubtotal = () => {
    return cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  };

  const getDiscountAmount = () => {
    return (getSubtotal() * discount) / 100;
  };

  const getTotal = () => {
    return getSubtotal() - getDiscountAmount();
  };

  const handleCheckout = () => {
    if (cart.length === 0) {
      toast.error('Cart is empty');
      return;
    }
    setShowPayment(true);
  };

  const processPayment = () => {
    // Simulate payment processing
    toast.success('Payment processed successfully!');
    setCart([]);
    setCustomerInfo({ name: '', phone: '', email: '' });
    setDiscount(0);
    setShowPayment(false);
  };

  const filteredProducts = sampleProducts.filter(product =>
    product.name.toLowerCase().includes(searchProduct.toLowerCase()) ||
    product.barcode.includes(searchProduct)
  );

  return (
    <div className="container-fluid p-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div className="d-flex align-items-center">
          <button className="btn btn-outline-secondary me-3" onClick={onBack}>
            ← Back to Terminals
          </button>
          <div>
            <h1 className="h3 mb-1">{terminal.name}</h1>
            <p className="text-muted mb-0">{terminal.code} • {terminal.location}</p>
          </div>
        </div>
        <div className="d-flex align-items-center gap-2">
          <span className={`badge bg-${terminal.status === 'online' ? 'success' : 'danger'}`}>
            {terminal.status}
          </span>
          <button className="btn btn-outline-primary btn-sm">
            🔄 Sync
          </button>
        </div>
      </div>

      <div className="row">
        {/* Product Search & Selection */}
        <div className="col-lg-8">
          <div className="card mb-4">
            <div className="card-header">
              <div className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">Product Search</h5>
                <div className="d-flex gap-2">
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Search by name or scan barcode..."
                    value={searchProduct}
                    onChange={(e) => setSearchProduct(e.target.value)}
                    style={{ width: '300px' }}
                  />
                  <button className="btn btn-outline-primary">
                    📷 Scan
                  </button>
                </div>
              </div>
            </div>
            <div className="card-body">
              <div className="row">
                {filteredProducts.map((product) => (
                  <div key={product.id} className="col-md-4 mb-3">
                    <div className="card h-100">
                      <div className="card-body text-center">
                        <h6 className="card-title">{product.name}</h6>
                        <p className="card-text">
                          <strong>${product.price}</strong><br />
                          <small className="text-muted">Stock: {product.stock}</small>
                        </p>
                        <button
                          className="btn btn-primary btn-sm"
                          onClick={() => addToCart(product)}
                          disabled={product.stock === 0}
                        >
                          Add to Cart
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Customer Information */}
          <div className="card">
            <div className="card-header">
              <h5 className="mb-0">Customer Information (Optional)</h5>
            </div>
            <div className="card-body">
              <div className="row">
                <div className="col-md-4">
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Customer Name"
                    value={customerInfo.name}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div className="col-md-4">
                  <input
                    type="tel"
                    className="form-control"
                    placeholder="Phone Number"
                    value={customerInfo.phone}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
                  />
                </div>
                <div className="col-md-4">
                  <input
                    type="email"
                    className="form-control"
                    placeholder="Email Address"
                    value={customerInfo.email}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, email: e.target.value }))}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Shopping Cart */}
        <div className="col-lg-4">
          <div className="card">
            <div className="card-header d-flex justify-content-between align-items-center">
              <h5 className="mb-0">Shopping Cart</h5>
              <button
                className="btn btn-outline-danger btn-sm"
                onClick={() => setCart([])}
                disabled={cart.length === 0}
              >
                Clear All
              </button>
            </div>
            <div className="card-body">
              {cart.length === 0 ? (
                <div className="text-center py-4">
                  <span className="text-muted">Cart is empty</span>
                </div>
              ) : (
                <>
                  <div className="cart-items mb-3" style={{ maxHeight: '300px', overflowY: 'auto' }}>
                    {cart.map((item) => (
                      <div key={item.id} className="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div className="flex-grow-1">
                          <div className="fw-medium">{item.name}</div>
                          <small className="text-muted">${item.price} each</small>
                        </div>
                        <div className="d-flex align-items-center gap-2">
                          <button
                            className="btn btn-outline-secondary btn-sm"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          >
                            -
                          </button>
                          <span className="mx-2">{item.quantity}</span>
                          <button
                            className="btn btn-outline-secondary btn-sm"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          >
                            +
                          </button>
                          <button
                            className="btn btn-outline-danger btn-sm ms-2"
                            onClick={() => removeFromCart(item.id)}
                          >
                            🗑️
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Discount */}
                  {terminal.settings.allowDiscounts && (
                    <div className="mb-3">
                      <label className="form-label">Discount (%)</label>
                      <input
                        type="number"
                        className="form-control"
                        value={discount}
                        onChange={(e) => setDiscount(Number(e.target.value))}
                        min="0"
                        max="100"
                      />
                    </div>
                  )}

                  {/* Totals */}
                  <div className="border-top pt-3">
                    <div className="d-flex justify-content-between mb-2">
                      <span>Subtotal:</span>
                      <span>${getSubtotal().toFixed(2)}</span>
                    </div>
                    {discount > 0 && (
                      <div className="d-flex justify-content-between mb-2 text-success">
                        <span>Discount ({discount}%):</span>
                        <span>-${getDiscountAmount().toFixed(2)}</span>
                      </div>
                    )}
                    <div className="d-flex justify-content-between mb-3 fw-bold">
                      <span>Total:</span>
                      <span>${getTotal().toFixed(2)}</span>
                    </div>

                    <button
                      className="btn btn-success w-100"
                      onClick={handleCheckout}
                      disabled={cart.length === 0}
                    >
                      💳 Checkout
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      {showPayment && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Process Payment</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowPayment(false)}
                ></button>
              </div>
              <div className="modal-body">
                <div className="mb-3">
                  <label className="form-label">Payment Method</label>
                  <select
                    className="form-select"
                    value={paymentMethod}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                  >
                    <option value="cash">💵 Cash</option>
                    <option value="card">💳 Credit/Debit Card</option>
                    <option value="digital">📱 Digital Wallet</option>
                  </select>
                </div>

                <div className="mb-3">
                  <div className="d-flex justify-content-between">
                    <span>Total Amount:</span>
                    <strong>${getTotal().toFixed(2)}</strong>
                  </div>
                </div>

                {paymentMethod === 'cash' && (
                  <div className="mb-3">
                    <label className="form-label">Cash Received</label>
                    <input
                      type="number"
                      className="form-control"
                      placeholder="Enter amount received"
                      step="0.01"
                    />
                  </div>
                )}
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setShowPayment(false)}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-success"
                  onClick={processPayment}
                >
                  Process Payment
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// POS Terminal Modal Component
const POSTerminalModal: React.FC<{
  terminal: POSTerminal | null;
  onSave: (data: POSTerminalFormData) => void;
  onCancel: () => void;
}> = ({ terminal, onSave, onCancel }) => {
  const [formData, setFormData] = useState<POSTerminalFormData>({
    name: terminal?.name || '',
    code: terminal?.code || '',
    location: terminal?.location || '',
    warehouseId: terminal?.warehouseId || '',
    cashierId: terminal?.cashierId || '',
    hardware: terminal?.hardware || {
      printer: false,
      scanner: false,
      cashDrawer: false,
      cardReader: false,
      display: false
    },
    settings: terminal?.settings || {
      allowDiscounts: false,
      allowReturns: false,
      requireManagerApproval: false,
      autoOpenDrawer: false,
      printReceipts: true
    },
    isActive: terminal?.isActive ?? true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Terminal name is required';
    }

    if (!formData.code.trim()) {
      newErrors.code = 'Terminal code is required';
    }

    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSave(formData);
    }
  };

  const handleInputChange = (field: keyof POSTerminalFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleHardwareChange = (hardware: string, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      hardware: { ...prev.hardware, [hardware]: value }
    }));
  };

  const handleSettingsChange = (setting: string, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      settings: { ...prev.settings, [setting]: value }
    }));
  };

  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-xl">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">
              {terminal ? 'Edit POS Terminal' : 'Add POS Terminal'}
            </h5>
            <button type="button" className="btn-close" onClick={onCancel}></button>
          </div>
          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              <div className="row">
                {/* Basic Information */}
                <div className="col-md-6">
                  <div className="card h-100">
                    <div className="card-header">
                      <h6 className="mb-0">Basic Information</h6>
                    </div>
                    <div className="card-body">
                      <div className="mb-3">
                        <label className="form-label">Terminal Name *</label>
                        <input
                          type="text"
                          className={`form-control ${errors.name ? 'is-invalid' : ''}`}
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          placeholder="Main Store Terminal 1"
                        />
                        {errors.name && <div className="invalid-feedback">{errors.name}</div>}
                      </div>

                      <div className="mb-3">
                        <label className="form-label">Terminal Code *</label>
                        <input
                          type="text"
                          className={`form-control ${errors.code ? 'is-invalid' : ''}`}
                          value={formData.code}
                          onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
                          placeholder="POS001"
                        />
                        {errors.code && <div className="invalid-feedback">{errors.code}</div>}
                      </div>

                      <div className="mb-3">
                        <label className="form-label">Location *</label>
                        <input
                          type="text"
                          className={`form-control ${errors.location ? 'is-invalid' : ''}`}
                          value={formData.location}
                          onChange={(e) => handleInputChange('location', e.target.value)}
                          placeholder="Main Store - Counter 1"
                        />
                        {errors.location && <div className="invalid-feedback">{errors.location}</div>}
                      </div>

                      <div className="mb-3">
                        <label className="form-label">Warehouse</label>
                        <select
                          className="form-select"
                          value={formData.warehouseId}
                          onChange={(e) => handleInputChange('warehouseId', e.target.value)}
                        >
                          <option value="">Select Warehouse</option>
                          <option value="1">Main Warehouse</option>
                          <option value="2">Branch Warehouse</option>
                        </select>
                      </div>

                      <div className="mb-3">
                        <label className="form-label">Assigned Cashier</label>
                        <select
                          className="form-select"
                          value={formData.cashierId}
                          onChange={(e) => handleInputChange('cashierId', e.target.value)}
                        >
                          <option value="">Select Cashier</option>
                          <option value="user_3">John Smith</option>
                          <option value="user_4">Jane Doe</option>
                          <option value="user_5">Mike Johnson</option>
                        </select>
                      </div>

                      <div className="mb-3">
                        <div className="form-check">
                          <input
                            className="form-check-input"
                            type="checkbox"
                            checked={formData.isActive}
                            onChange={(e) => handleInputChange('isActive', e.target.checked)}
                          />
                          <label className="form-check-label">
                            Active Terminal
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Hardware & Settings */}
                <div className="col-md-6">
                  {/* Hardware Configuration */}
                  <div className="card mb-3">
                    <div className="card-header">
                      <h6 className="mb-0">Hardware Configuration</h6>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <div className="form-check mb-2">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={formData.hardware.printer}
                              onChange={(e) => handleHardwareChange('printer', e.target.checked)}
                            />
                            <label className="form-check-label">🖨️ Receipt Printer</label>
                          </div>
                          <div className="form-check mb-2">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={formData.hardware.scanner}
                              onChange={(e) => handleHardwareChange('scanner', e.target.checked)}
                            />
                            <label className="form-check-label">📷 Barcode Scanner</label>
                          </div>
                          <div className="form-check mb-2">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={formData.hardware.cashDrawer}
                              onChange={(e) => handleHardwareChange('cashDrawer', e.target.checked)}
                            />
                            <label className="form-check-label">💰 Cash Drawer</label>
                          </div>
                        </div>
                        <div className="col-md-6">
                          <div className="form-check mb-2">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={formData.hardware.cardReader}
                              onChange={(e) => handleHardwareChange('cardReader', e.target.checked)}
                            />
                            <label className="form-check-label">💳 Card Reader</label>
                          </div>
                          <div className="form-check mb-2">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={formData.hardware.display}
                              onChange={(e) => handleHardwareChange('display', e.target.checked)}
                            />
                            <label className="form-check-label">🖥️ Customer Display</label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Terminal Settings */}
                  <div className="card">
                    <div className="card-header">
                      <h6 className="mb-0">Terminal Settings</h6>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <div className="form-check mb-2">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={formData.settings.allowDiscounts}
                              onChange={(e) => handleSettingsChange('allowDiscounts', e.target.checked)}
                            />
                            <label className="form-check-label">Allow Discounts</label>
                          </div>
                          <div className="form-check mb-2">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={formData.settings.allowReturns}
                              onChange={(e) => handleSettingsChange('allowReturns', e.target.checked)}
                            />
                            <label className="form-check-label">Allow Returns</label>
                          </div>
                          <div className="form-check mb-2">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={formData.settings.requireManagerApproval}
                              onChange={(e) => handleSettingsChange('requireManagerApproval', e.target.checked)}
                            />
                            <label className="form-check-label">Require Manager Approval</label>
                          </div>
                        </div>
                        <div className="col-md-6">
                          <div className="form-check mb-2">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={formData.settings.autoOpenDrawer}
                              onChange={(e) => handleSettingsChange('autoOpenDrawer', e.target.checked)}
                            />
                            <label className="form-check-label">Auto Open Drawer</label>
                          </div>
                          <div className="form-check mb-2">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={formData.settings.printReceipts}
                              onChange={(e) => handleSettingsChange('printReceipts', e.target.checked)}
                            />
                            <label className="form-check-label">Print Receipts</label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onCancel}>
                Cancel
              </button>
              <button type="submit" className="btn btn-primary">
                {terminal ? 'Update Terminal' : 'Add Terminal'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default POSPanel;
