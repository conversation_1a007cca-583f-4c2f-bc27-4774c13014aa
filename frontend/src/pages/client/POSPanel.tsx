import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { POSTerminal, POSTerminalFormData } from '../../types/client';

const POSPanel: React.FC = () => {
  const [terminals, setTerminals] = useState<POSTerminal[]>([]);
  const [selectedTerminal, setSelectedTerminal] = useState<POSTerminal | null>(null);
  const [showAddTerminal, setShowAddTerminal] = useState(false);
  const [editingTerminal, setEditingTerminal] = useState<POSTerminal | null>(null);
  const [view, setView] = useState<'list' | 'terminal'>('list');

  // Load sample data
  useEffect(() => {
    const sampleTerminals: POSTerminal[] = [
      {
        id: '1',
        clientId: 'client_1',
        warehouseId: '1',
        name: 'Main Store Terminal 1',
        code: 'POS001',
        location: 'Main Store - Counter 1',
        isActive: true,
        status: 'online',
        cashierId: 'user_3',
        lastTransaction: '2024-01-15T14:30:00Z',
        dailySales: {
          amount: 2450.75,
          transactions: 28,
          date: '2024-01-15'
        },
        hardware: {
          printer: true,
          scanner: true,
          cashDrawer: true,
          cardReader: true,
          display: true
        },
        settings: {
          allowDiscounts: true,
          allowReturns: true,
          requireManagerApproval: false,
          autoOpenDrawer: true,
          printReceipts: true
        },
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-15T14:30:00Z'
      },
      {
        id: '2',
        clientId: 'client_1',
        warehouseId: '1',
        name: 'Main Store Terminal 2',
        code: 'POS002',
        location: 'Main Store - Counter 2',
        isActive: true,
        status: 'online',
        cashierId: 'user_4',
        lastTransaction: '2024-01-15T13:45:00Z',
        dailySales: {
          amount: 1890.25,
          transactions: 22,
          date: '2024-01-15'
        },
        hardware: {
          printer: true,
          scanner: true,
          cashDrawer: true,
          cardReader: true,
          display: false
        },
        settings: {
          allowDiscounts: false,
          allowReturns: true,
          requireManagerApproval: true,
          autoOpenDrawer: true,
          printReceipts: true
        },
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-15T13:45:00Z'
      },
      {
        id: '3',
        clientId: 'client_1',
        warehouseId: '2',
        name: 'Branch Store Terminal',
        code: 'POS003',
        location: 'Branch Store - Main Counter',
        isActive: true,
        status: 'offline',
        lastTransaction: '2024-01-14T18:20:00Z',
        dailySales: {
          amount: 0,
          transactions: 0,
          date: '2024-01-15'
        },
        hardware: {
          printer: true,
          scanner: false,
          cashDrawer: true,
          cardReader: false,
          display: false
        },
        settings: {
          allowDiscounts: true,
          allowReturns: false,
          requireManagerApproval: true,
          autoOpenDrawer: false,
          printReceipts: true
        },
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-14T18:20:00Z'
      }
    ];
    setTerminals(sampleTerminals);
  }, []);

  const handleAddTerminal = () => {
    setEditingTerminal(null);
    setShowAddTerminal(true);
  };

  const handleEditTerminal = (terminal: POSTerminal) => {
    setEditingTerminal(terminal);
    setShowAddTerminal(true);
  };

  const handleDeleteTerminal = (terminalId: string) => {
    if (window.confirm('Are you sure you want to delete this POS terminal?')) {
      setTerminals(prev => prev.filter(t => t.id !== terminalId));
      toast.success('POS terminal deleted successfully');
    }
  };

  const handleToggleTerminal = (terminalId: string) => {
    setTerminals(prev => prev.map(terminal => 
      terminal.id === terminalId 
        ? { ...terminal, isActive: !terminal.isActive }
        : terminal
    ));
    toast.success('Terminal status updated');
  };

  const handleOpenTerminal = (terminal: POSTerminal) => {
    setSelectedTerminal(terminal);
    setView('terminal');
  };

  const getStatusColor = (status: string) => {
    const colors = {
      online: 'success',
      offline: 'danger',
      maintenance: 'warning'
    };
    return colors[status as keyof typeof colors] || 'secondary';
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      online: '🟢',
      offline: '🔴',
      maintenance: '🟡'
    };
    return icons[status as keyof typeof icons] || '⚪';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getTotalDailySales = () => {
    return terminals.reduce((sum, terminal) => sum + (terminal.dailySales?.amount || 0), 0);
  };

  const getTotalTransactions = () => {
    return terminals.reduce((sum, terminal) => sum + (terminal.dailySales?.transactions || 0), 0);
  };

  if (view === 'terminal' && selectedTerminal) {
    return (
      <POSTerminalInterface
        terminal={selectedTerminal}
        onBack={() => setView('list')}
      />
    );
  }

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">POS Management</h1>
          <p className="text-muted mb-0">Manage point of sale terminals and transactions</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-primary"
            onClick={() => toast.success('POS analytics coming soon!')}
          >
            📊 Analytics
          </button>
          <button
            className="btn btn-outline-success"
            onClick={() => toast.success('Sales report coming soon!')}
          >
            📈 Sales Report
          </button>
          <button
            className="btn btn-primary"
            onClick={handleAddTerminal}
          >
            ➕ Add Terminal
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary bg-opacity-10 p-3 rounded">
                    <span className="text-primary fs-4">💰</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Terminals</h6>
                  <h4 className="mb-0">{terminals.length}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-success bg-opacity-10 p-3 rounded">
                    <span className="text-success fs-4">🟢</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Online Terminals</h6>
                  <h4 className="mb-0">{terminals.filter(t => t.status === 'online').length}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-warning bg-opacity-10 p-3 rounded">
                    <span className="text-warning fs-4">💵</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Today's Sales</h6>
                  <h4 className="mb-0">{formatCurrency(getTotalDailySales())}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-info bg-opacity-10 p-3 rounded">
                    <span className="text-info fs-4">🧾</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Transactions</h6>
                  <h4 className="mb-0">{getTotalTransactions()}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Terminals List */}
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">POS Terminals</h5>
        </div>
        <div className="card-body p-0">
          {terminals.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <span className="display-1">💰</span>
              </div>
              <h5>No POS Terminals Configured</h5>
              <p className="text-muted">Add your first POS terminal to start processing sales</p>
              <button className="btn btn-primary" onClick={handleAddTerminal}>
                Add POS Terminal
              </button>
            </div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead className="table-light">
                  <tr>
                    <th>Terminal</th>
                    <th>Location</th>
                    <th>Status</th>
                    <th>Cashier</th>
                    <th>Today's Sales</th>
                    <th>Transactions</th>
                    <th>Last Activity</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {terminals.map((terminal) => (
                    <tr key={terminal.id}>
                      <td>
                        <div>
                          <div className="fw-medium">{terminal.name}</div>
                          <small className="text-muted">{terminal.code}</small>
                        </div>
                      </td>
                      <td>
                        <span className="text-muted">{terminal.location}</span>
                      </td>
                      <td>
                        <div className="d-flex align-items-center">
                          <span className="me-1">{getStatusIcon(terminal.status)}</span>
                          <span className={`badge bg-${getStatusColor(terminal.status)}`}>
                            {terminal.status}
                          </span>
                        </div>
                      </td>
                      <td>
                        {terminal.cashierId ? (
                          <span className="badge bg-light text-dark">
                            User {terminal.cashierId}
                          </span>
                        ) : (
                          <span className="text-muted">Unassigned</span>
                        )}
                      </td>
                      <td>
                        <div className="fw-medium">
                          {formatCurrency(terminal.dailySales?.amount || 0)}
                        </div>
                      </td>
                      <td>
                        <span className="badge bg-primary">
                          {terminal.dailySales?.transactions || 0}
                        </span>
                      </td>
                      <td>
                        {terminal.lastTransaction ? (
                          <small className="text-muted">
                            {new Date(terminal.lastTransaction).toLocaleString()}
                          </small>
                        ) : (
                          <small className="text-muted">Never</small>
                        )}
                      </td>
                      <td>
                        <div className="btn-group btn-group-sm">
                          <button
                            className="btn btn-outline-primary"
                            onClick={() => handleOpenTerminal(terminal)}
                            disabled={terminal.status === 'offline'}
                          >
                            🖥️ Open
                          </button>
                          <button
                            className="btn btn-outline-secondary"
                            onClick={() => handleEditTerminal(terminal)}
                          >
                            ✏️ Edit
                          </button>
                          <button
                            className={`btn ${terminal.isActive ? 'btn-outline-warning' : 'btn-outline-success'}`}
                            onClick={() => handleToggleTerminal(terminal.id)}
                          >
                            {terminal.isActive ? '⏸️ Disable' : '▶️ Enable'}
                          </button>
                          <button
                            className="btn btn-outline-danger"
                            onClick={() => handleDeleteTerminal(terminal.id)}
                          >
                            🗑️ Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Add/Edit Terminal Modal */}
      {showAddTerminal && (
        <POSTerminalModal
          terminal={editingTerminal}
          onSave={(terminalData) => {
            if (editingTerminal) {
              // Update existing terminal
              setTerminals(prev => prev.map(t => 
                t.id === editingTerminal.id 
                  ? { ...t, ...terminalData, updatedAt: new Date().toISOString() }
                  : t
              ));
              toast.success('POS terminal updated successfully');
            } else {
              // Add new terminal
              const newTerminal: POSTerminal = {
                id: (terminals.length + 1).toString(),
                clientId: 'client_1',
                ...terminalData,
                status: 'offline',
                dailySales: {
                  amount: 0,
                  transactions: 0,
                  date: new Date().toISOString().split('T')[0]
                },
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
              };
              setTerminals(prev => [...prev, newTerminal]);
              toast.success('POS terminal added successfully');
            }
            setShowAddTerminal(false);
          }}
          onCancel={() => setShowAddTerminal(false)}
        />
      )}
    </div>
  );
};

// POS Terminal Interface Component (placeholder)
const POSTerminalInterface: React.FC<{
  terminal: POSTerminal;
  onBack: () => void;
}> = ({ terminal, onBack }) => {
  return (
    <div className="container-fluid p-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div className="d-flex align-items-center">
          <button className="btn btn-outline-secondary me-3" onClick={onBack}>
            ← Back to Terminals
          </button>
          <div>
            <h1 className="h3 mb-1">{terminal.name}</h1>
            <p className="text-muted mb-0">{terminal.code} • {terminal.location}</p>
          </div>
        </div>
        <div className="d-flex align-items-center gap-2">
          <span className={`badge bg-${terminal.status === 'online' ? 'success' : 'danger'}`}>
            {terminal.status}
          </span>
        </div>
      </div>

      <div className="text-center py-5">
        <div className="mb-3">
          <span className="display-1">💰</span>
        </div>
        <h5>POS Terminal Interface</h5>
        <p className="text-muted">This will be a full POS interface for processing sales transactions</p>
        <p className="text-muted">Features will include product scanning, payment processing, receipt printing, and more</p>
      </div>
    </div>
  );
};

// POS Terminal Modal Component (placeholder)
const POSTerminalModal: React.FC<{
  terminal: POSTerminal | null;
  onSave: (data: POSTerminalFormData) => void;
  onCancel: () => void;
}> = ({ terminal, onSave, onCancel }) => {
  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-lg">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">
              {terminal ? 'Edit POS Terminal' : 'Add POS Terminal'}
            </h5>
            <button type="button" className="btn-close" onClick={onCancel}></button>
          </div>
          <div className="modal-body">
            <div className="text-center py-4">
              <p>POS Terminal Configuration Form</p>
              <p className="text-muted">This will be implemented in the next step</p>
            </div>
          </div>
          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onCancel}>
              Cancel
            </button>
            <button type="button" className="btn btn-primary" onClick={() => onSave({} as any)}>
              Save Terminal
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default POSPanel;
