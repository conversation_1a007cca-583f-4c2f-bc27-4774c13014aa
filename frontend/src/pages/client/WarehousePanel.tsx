import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { Warehouse, WarehouseZone, WarehouseFormData, WAREHOUSE_TYPES, WAREHOUSE_ZONE_TYPES } from '../../types/client';

const WarehousePanel: React.FC = () => {
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState<Warehouse | null>(null);
  const [showAddWarehouse, setShowAddWarehouse] = useState(false);
  const [showAddZone, setShowAddZone] = useState(false);
  const [editingWarehouse, setEditingWarehouse] = useState<Warehouse | null>(null);
  const [view, setView] = useState<'list' | 'details'>('list');

  // Load sample data
  useEffect(() => {
    const sampleWarehouses: Warehouse[] = [
      {
        id: '1',
        clientId: 'client_1',
        name: 'Main Warehouse',
        code: 'WH001',
        address: '123 Industrial Blvd, City, State 12345',
        phone: '+****************',
        email: '<EMAIL>',
        managerId: 'user_1',
        isActive: true,
        type: 'main',
        capacity: {
          totalSpace: 10000,
          usedSpace: 7500,
          unit: 'sqft'
        },
        zones: [
          {
            id: 'zone_1',
            warehouseId: '1',
            name: 'Receiving Area',
            code: 'RCV001',
            type: 'receiving',
            capacity: 500,
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z'
          },
          {
            id: 'zone_2',
            warehouseId: '1',
            name: 'Storage Area A',
            code: 'STG001',
            type: 'storage',
            capacity: 3000,
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z'
          },
          {
            id: 'zone_3',
            warehouseId: '1',
            name: 'Picking Area',
            code: 'PCK001',
            type: 'picking',
            capacity: 800,
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z'
          }
        ],
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-15T00:00:00Z'
      },
      {
        id: '2',
        clientId: 'client_1',
        name: 'Branch Warehouse',
        code: 'WH002',
        address: '456 Commerce St, City, State 12345',
        phone: '+****************',
        managerId: 'user_2',
        isActive: true,
        type: 'branch',
        capacity: {
          totalSpace: 5000,
          usedSpace: 3200,
          unit: 'sqft'
        },
        zones: [
          {
            id: 'zone_4',
            warehouseId: '2',
            name: 'Storage Area B',
            code: 'STG002',
            type: 'storage',
            capacity: 2000,
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z'
          }
        ],
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-10T00:00:00Z'
      }
    ];
    setWarehouses(sampleWarehouses);
  }, []);

  const handleAddWarehouse = () => {
    setEditingWarehouse(null);
    setShowAddWarehouse(true);
  };

  const handleEditWarehouse = (warehouse: Warehouse) => {
    setEditingWarehouse(warehouse);
    setShowAddWarehouse(true);
  };

  const handleDeleteWarehouse = (warehouseId: string) => {
    if (window.confirm('Are you sure you want to delete this warehouse?')) {
      setWarehouses(prev => prev.filter(w => w.id !== warehouseId));
      toast.success('Warehouse deleted successfully');
    }
  };

  const handleToggleWarehouse = (warehouseId: string) => {
    setWarehouses(prev => prev.map(warehouse => 
      warehouse.id === warehouseId 
        ? { ...warehouse, isActive: !warehouse.isActive }
        : warehouse
    ));
    toast.success('Warehouse status updated');
  };

  const handleViewWarehouse = (warehouse: Warehouse) => {
    setSelectedWarehouse(warehouse);
    setView('details');
  };

  const getTypeIcon = (type: string) => {
    const icons = {
      main: '🏭',
      branch: '🏢',
      storage: '📦',
      distribution: '🚚'
    };
    return icons[type as keyof typeof icons] || '🏭';
  };

  const getZoneTypeIcon = (type: string) => {
    const icons = {
      receiving: '📥',
      storage: '📦',
      picking: '🎯',
      shipping: '📤',
      returns: '↩️'
    };
    return icons[type as keyof typeof icons] || '📦';
  };

  const calculateUtilization = (warehouse: Warehouse) => {
    if (!warehouse.capacity) return 0;
    return (warehouse.capacity.usedSpace / warehouse.capacity.totalSpace) * 100;
  };

  if (view === 'details' && selectedWarehouse) {
    return (
      <WarehouseDetails
        warehouse={selectedWarehouse}
        onBack={() => setView('list')}
        onEdit={() => handleEditWarehouse(selectedWarehouse)}
        onAddZone={() => setShowAddZone(true)}
      />
    );
  }

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Warehouse Management</h1>
          <p className="text-muted mb-0">Manage warehouse locations and operations</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-primary"
            onClick={() => toast.success('Warehouse analytics coming soon!')}
          >
            📊 Analytics
          </button>
          <button
            className="btn btn-primary"
            onClick={handleAddWarehouse}
          >
            ➕ Add Warehouse
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary bg-opacity-10 p-3 rounded">
                    <span className="text-primary fs-4">🏭</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Warehouses</h6>
                  <h4 className="mb-0">{warehouses.length}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-success bg-opacity-10 p-3 rounded">
                    <span className="text-success fs-4">✅</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Active Warehouses</h6>
                  <h4 className="mb-0">{warehouses.filter(w => w.isActive).length}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-warning bg-opacity-10 p-3 rounded">
                    <span className="text-warning fs-4">📦</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Zones</h6>
                  <h4 className="mb-0">{warehouses.reduce((sum, w) => sum + w.zones.length, 0)}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-info bg-opacity-10 p-3 rounded">
                    <span className="text-info fs-4">📏</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Space</h6>
                  <h4 className="mb-0">
                    {warehouses.reduce((sum, w) => sum + (w.capacity?.totalSpace || 0), 0).toLocaleString()} sqft
                  </h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Warehouses List */}
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">Warehouse Locations</h5>
        </div>
        <div className="card-body p-0">
          {warehouses.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <span className="display-1">🏭</span>
              </div>
              <h5>No Warehouses Configured</h5>
              <p className="text-muted">Add your first warehouse to start managing inventory locations</p>
              <button className="btn btn-primary" onClick={handleAddWarehouse}>
                Add Warehouse
              </button>
            </div>
          ) : (
            <div className="row g-0">
              {warehouses.map((warehouse) => (
                <div key={warehouse.id} className="col-lg-6 col-xl-4">
                  <div className="card border-0 border-end border-bottom">
                    <div className="card-body">
                      <div className="d-flex align-items-start justify-content-between mb-3">
                        <div className="d-flex align-items-center">
                          <span className="me-2 fs-4">{getTypeIcon(warehouse.type)}</span>
                          <div>
                            <h6 className="mb-0">{warehouse.name}</h6>
                            <small className="text-muted">{warehouse.code}</small>
                          </div>
                        </div>
                        <div className="dropdown">
                          <button
                            className="btn btn-sm btn-outline-secondary dropdown-toggle"
                            type="button"
                            data-bs-toggle="dropdown"
                          >
                            Actions
                          </button>
                          <ul className="dropdown-menu">
                            <li>
                              <button
                                className="dropdown-item"
                                onClick={() => handleViewWarehouse(warehouse)}
                              >
                                👁️ View Details
                              </button>
                            </li>
                            <li>
                              <button
                                className="dropdown-item"
                                onClick={() => handleEditWarehouse(warehouse)}
                              >
                                ✏️ Edit
                              </button>
                            </li>
                            <li>
                              <button
                                className="dropdown-item"
                                onClick={() => handleToggleWarehouse(warehouse.id)}
                              >
                                {warehouse.isActive ? '⏸️ Deactivate' : '▶️ Activate'}
                              </button>
                            </li>
                            <li><hr className="dropdown-divider" /></li>
                            <li>
                              <button
                                className="dropdown-item text-danger"
                                onClick={() => handleDeleteWarehouse(warehouse.id)}
                              >
                                🗑️ Delete
                              </button>
                            </li>
                          </ul>
                        </div>
                      </div>

                      <div className="mb-3">
                        <small className="text-muted d-block">{warehouse.address}</small>
                        {warehouse.phone && (
                          <small className="text-muted d-block">{warehouse.phone}</small>
                        )}
                      </div>

                      <div className="mb-3">
                        <div className="d-flex justify-content-between align-items-center mb-1">
                          <small className="text-muted">Space Utilization</small>
                          <small className="text-muted">
                            {warehouse.capacity ? `${calculateUtilization(warehouse).toFixed(1)}%` : 'N/A'}
                          </small>
                        </div>
                        {warehouse.capacity && (
                          <div className="progress" style={{ height: '6px' }}>
                            <div
                              className="progress-bar"
                              style={{ width: `${calculateUtilization(warehouse)}%` }}
                            ></div>
                          </div>
                        )}
                      </div>

                      <div className="d-flex justify-content-between align-items-center">
                        <div>
                          <span className={`badge ${warehouse.isActive ? 'bg-success' : 'bg-secondary'}`}>
                            {warehouse.isActive ? 'Active' : 'Inactive'}
                          </span>
                          <span className="badge bg-light text-dark ms-1">
                            {WAREHOUSE_TYPES.find(t => t.value === warehouse.type)?.label}
                          </span>
                        </div>
                        <small className="text-muted">
                          {warehouse.zones.length} zones
                        </small>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Add/Edit Warehouse Modal */}
      {showAddWarehouse && (
        <WarehouseModal
          warehouse={editingWarehouse}
          onSave={(warehouseData) => {
            if (editingWarehouse) {
              // Update existing warehouse
              setWarehouses(prev => prev.map(w => 
                w.id === editingWarehouse.id 
                  ? { ...w, ...warehouseData, updatedAt: new Date().toISOString() }
                  : w
              ));
              toast.success('Warehouse updated successfully');
            } else {
              // Add new warehouse
              const newWarehouse: Warehouse = {
                id: (warehouses.length + 1).toString(),
                clientId: 'client_1',
                ...warehouseData,
                zones: [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
              };
              setWarehouses(prev => [...prev, newWarehouse]);
              toast.success('Warehouse added successfully');
            }
            setShowAddWarehouse(false);
          }}
          onCancel={() => setShowAddWarehouse(false)}
        />
      )}
    </div>
  );
};

// Warehouse Details Component
const WarehouseDetails: React.FC<{
  warehouse: Warehouse;
  onBack: () => void;
  onEdit: () => void;
  onAddZone: () => void;
}> = ({ warehouse, onBack, onEdit, onAddZone }) => {
  const [zones, setZones] = useState<WarehouseZone[]>(warehouse.zones);
  const [showAddZone, setShowAddZone] = useState(false);
  const [editingZone, setEditingZone] = useState<WarehouseZone | null>(null);

  const handleAddZone = () => {
    setEditingZone(null);
    setShowAddZone(true);
  };

  const handleEditZone = (zone: WarehouseZone) => {
    setEditingZone(zone);
    setShowAddZone(true);
  };

  const handleDeleteZone = (zoneId: string) => {
    if (window.confirm('Are you sure you want to delete this zone?')) {
      setZones(prev => prev.filter(z => z.id !== zoneId));
      toast.success('Zone deleted successfully');
    }
  };

  const handleSaveZone = (zoneData: any) => {
    if (editingZone) {
      setZones(prev => prev.map(z =>
        z.id === editingZone.id
          ? { ...z, ...zoneData }
          : z
      ));
      toast.success('Zone updated successfully');
    } else {
      const newZone: WarehouseZone = {
        id: `zone_${zones.length + 1}`,
        warehouseId: warehouse.id,
        ...zoneData,
        createdAt: new Date().toISOString()
      };
      setZones(prev => [...prev, newZone]);
      toast.success('Zone added successfully');
    }
    setShowAddZone(false);
  };

  return (
    <div className="container-fluid p-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div className="d-flex align-items-center">
          <button className="btn btn-outline-secondary me-3" onClick={onBack}>
            ← Back
          </button>
          <div>
            <h1 className="h3 mb-1">{warehouse.name}</h1>
            <p className="text-muted mb-0">{warehouse.code} • {warehouse.address}</p>
          </div>
        </div>
        <div className="d-flex gap-2">
          <button className="btn btn-outline-primary" onClick={onEdit}>
            ✏️ Edit Warehouse
          </button>
          <button className="btn btn-primary" onClick={handleAddZone}>
            ➕ Add Zone
          </button>
        </div>
      </div>

      {/* Warehouse Info Cards */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <h6 className="card-title">Total Zones</h6>
              <h4 className="mb-0">{zones.length}</h4>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <h6 className="card-title">Total Capacity</h6>
              <h4 className="mb-0">
                {warehouse.capacity ? `${warehouse.capacity.totalSpace.toLocaleString()} ${warehouse.capacity.unit}` : 'N/A'}
              </h4>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <h6 className="card-title">Used Space</h6>
              <h4 className="mb-0">
                {warehouse.capacity ? `${warehouse.capacity.usedSpace.toLocaleString()} ${warehouse.capacity.unit}` : 'N/A'}
              </h4>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <h6 className="card-title">Utilization</h6>
              <h4 className="mb-0">
                {warehouse.capacity ? `${((warehouse.capacity.usedSpace / warehouse.capacity.totalSpace) * 100).toFixed(1)}%` : 'N/A'}
              </h4>
            </div>
          </div>
        </div>
      </div>

      {/* Zones Management */}
      <div className="card">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h5 className="mb-0">Warehouse Zones</h5>
          <button className="btn btn-primary btn-sm" onClick={handleAddZone}>
            ➕ Add Zone
          </button>
        </div>
        <div className="card-body p-0">
          {zones.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <span className="display-1">📦</span>
              </div>
              <h5>No Zones Configured</h5>
              <p className="text-muted">Add zones to organize your warehouse space</p>
              <button className="btn btn-primary" onClick={handleAddZone}>
                Add First Zone
              </button>
            </div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead className="table-light">
                  <tr>
                    <th>Zone</th>
                    <th>Type</th>
                    <th>Capacity</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {zones.map((zone) => (
                    <tr key={zone.id}>
                      <td>
                        <div>
                          <div className="fw-medium">{zone.name}</div>
                          <small className="text-muted">{zone.code}</small>
                        </div>
                      </td>
                      <td>
                        <div className="d-flex align-items-center">
                          <span className="me-2">{getZoneTypeIcon(zone.type)}</span>
                          <span className="badge bg-light text-dark">
                            {WAREHOUSE_ZONE_TYPES.find(t => t.value === zone.type)?.label}
                          </span>
                        </div>
                      </td>
                      <td>
                        {zone.capacity ? `${zone.capacity.toLocaleString()} units` : 'N/A'}
                      </td>
                      <td>
                        <span className={`badge ${zone.isActive ? 'bg-success' : 'bg-secondary'}`}>
                          {zone.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td>
                        <small className="text-muted">
                          {new Date(zone.createdAt).toLocaleDateString()}
                        </small>
                      </td>
                      <td>
                        <div className="btn-group btn-group-sm">
                          <button
                            className="btn btn-outline-primary"
                            onClick={() => handleEditZone(zone)}
                          >
                            ✏️ Edit
                          </button>
                          <button
                            className="btn btn-outline-danger"
                            onClick={() => handleDeleteZone(zone.id)}
                          >
                            🗑️ Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Zone Modal */}
      {showAddZone && (
        <ZoneModal
          zone={editingZone}
          onSave={handleSaveZone}
          onCancel={() => setShowAddZone(false)}
        />
      )}
    </div>
  );
};

// Warehouse Modal Component
const WarehouseModal: React.FC<{
  warehouse: Warehouse | null;
  onSave: (data: WarehouseFormData) => void;
  onCancel: () => void;
}> = ({ warehouse, onSave, onCancel }) => {
  const [formData, setFormData] = useState<WarehouseFormData>({
    name: warehouse?.name || '',
    code: warehouse?.code || '',
    address: warehouse?.address || '',
    phone: warehouse?.phone || '',
    email: warehouse?.email || '',
    managerId: warehouse?.managerId || '',
    type: warehouse?.type || 'main',
    isActive: warehouse?.isActive ?? true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Warehouse name is required';
    }

    if (!formData.code.trim()) {
      newErrors.code = 'Warehouse code is required';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Address is required';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSave(formData);
    }
  };

  const handleInputChange = (field: keyof WarehouseFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-lg">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">
              {warehouse ? 'Edit Warehouse' : 'Add Warehouse'}
            </h5>
            <button type="button" className="btn-close" onClick={onCancel}></button>
          </div>
          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              <div className="row">
                <div className="col-md-6 mb-3">
                  <label className="form-label">Warehouse Name *</label>
                  <input
                    type="text"
                    className={`form-control ${errors.name ? 'is-invalid' : ''}`}
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter warehouse name"
                  />
                  {errors.name && <div className="invalid-feedback">{errors.name}</div>}
                </div>
                <div className="col-md-6 mb-3">
                  <label className="form-label">Warehouse Code *</label>
                  <input
                    type="text"
                    className={`form-control ${errors.code ? 'is-invalid' : ''}`}
                    value={formData.code}
                    onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
                    placeholder="WH001"
                  />
                  {errors.code && <div className="invalid-feedback">{errors.code}</div>}
                </div>
              </div>

              <div className="mb-3">
                <label className="form-label">Address *</label>
                <textarea
                  className={`form-control ${errors.address ? 'is-invalid' : ''}`}
                  rows={3}
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="Enter complete address"
                />
                {errors.address && <div className="invalid-feedback">{errors.address}</div>}
              </div>

              <div className="row">
                <div className="col-md-6 mb-3">
                  <label className="form-label">Phone</label>
                  <input
                    type="tel"
                    className="form-control"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="+****************"
                  />
                </div>
                <div className="col-md-6 mb-3">
                  <label className="form-label">Email</label>
                  <input
                    type="email"
                    className={`form-control ${errors.email ? 'is-invalid' : ''}`}
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && <div className="invalid-feedback">{errors.email}</div>}
                </div>
              </div>

              <div className="row">
                <div className="col-md-6 mb-3">
                  <label className="form-label">Warehouse Type</label>
                  <select
                    className="form-select"
                    value={formData.type}
                    onChange={(e) => handleInputChange('type', e.target.value)}
                  >
                    {WAREHOUSE_TYPES.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                  <small className="form-text text-muted">
                    {WAREHOUSE_TYPES.find(t => t.value === formData.type)?.description}
                  </small>
                </div>
                <div className="col-md-6 mb-3">
                  <label className="form-label">Manager</label>
                  <select
                    className="form-select"
                    value={formData.managerId}
                    onChange={(e) => handleInputChange('managerId', e.target.value)}
                  >
                    <option value="">Select Manager</option>
                    <option value="user_1">John Smith</option>
                    <option value="user_2">Jane Doe</option>
                    <option value="user_3">Mike Johnson</option>
                  </select>
                </div>
              </div>

              <div className="mb-3">
                <div className="form-check">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => handleInputChange('isActive', e.target.checked)}
                  />
                  <label className="form-check-label">
                    Active Warehouse
                  </label>
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onCancel}>
                Cancel
              </button>
              <button type="submit" className="btn btn-primary">
                {warehouse ? 'Update Warehouse' : 'Add Warehouse'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

// Zone Modal Component
const ZoneModal: React.FC<{
  zone: WarehouseZone | null;
  onSave: (data: any) => void;
  onCancel: () => void;
}> = ({ zone, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: zone?.name || '',
    code: zone?.code || '',
    type: zone?.type || 'storage',
    capacity: zone?.capacity || '',
    isActive: zone?.isActive ?? true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Zone name is required';
    }

    if (!formData.code.trim()) {
      newErrors.code = 'Zone code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSave({
        ...formData,
        capacity: formData.capacity ? Number(formData.capacity) : undefined
      });
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">
              {zone ? 'Edit Zone' : 'Add Zone'}
            </h5>
            <button type="button" className="btn-close" onClick={onCancel}></button>
          </div>
          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              <div className="row">
                <div className="col-md-6 mb-3">
                  <label className="form-label">Zone Name *</label>
                  <input
                    type="text"
                    className={`form-control ${errors.name ? 'is-invalid' : ''}`}
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter zone name"
                  />
                  {errors.name && <div className="invalid-feedback">{errors.name}</div>}
                </div>
                <div className="col-md-6 mb-3">
                  <label className="form-label">Zone Code *</label>
                  <input
                    type="text"
                    className={`form-control ${errors.code ? 'is-invalid' : ''}`}
                    value={formData.code}
                    onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
                    placeholder="STG001"
                  />
                  {errors.code && <div className="invalid-feedback">{errors.code}</div>}
                </div>
              </div>

              <div className="row">
                <div className="col-md-6 mb-3">
                  <label className="form-label">Zone Type</label>
                  <select
                    className="form-select"
                    value={formData.type}
                    onChange={(e) => handleInputChange('type', e.target.value)}
                  >
                    {WAREHOUSE_ZONE_TYPES.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                  <small className="form-text text-muted">
                    {WAREHOUSE_ZONE_TYPES.find(t => t.value === formData.type)?.description}
                  </small>
                </div>
                <div className="col-md-6 mb-3">
                  <label className="form-label">Capacity (units)</label>
                  <input
                    type="number"
                    className="form-control"
                    value={formData.capacity}
                    onChange={(e) => handleInputChange('capacity', e.target.value)}
                    placeholder="1000"
                    min="0"
                  />
                </div>
              </div>

              <div className="mb-3">
                <div className="form-check">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => handleInputChange('isActive', e.target.checked)}
                  />
                  <label className="form-check-label">
                    Active Zone
                  </label>
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onCancel}>
                Cancel
              </button>
              <button type="submit" className="btn btn-primary">
                {zone ? 'Update Zone' : 'Add Zone'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default WarehousePanel;
