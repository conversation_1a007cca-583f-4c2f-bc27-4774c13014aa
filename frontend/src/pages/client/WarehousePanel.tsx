import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { Warehouse, WarehouseZone, WarehouseFormData, WAREHOUSE_TYPES, WAREHOUSE_ZONE_TYPES } from '../../types/client';

const WarehousePanel: React.FC = () => {
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState<Warehouse | null>(null);
  const [showAddWarehouse, setShowAddWarehouse] = useState(false);
  const [showAddZone, setShowAddZone] = useState(false);
  const [editingWarehouse, setEditingWarehouse] = useState<Warehouse | null>(null);
  const [view, setView] = useState<'list' | 'details'>('list');

  // Load sample data
  useEffect(() => {
    const sampleWarehouses: Warehouse[] = [
      {
        id: '1',
        clientId: 'client_1',
        name: 'Main Warehouse',
        code: 'WH001',
        address: '123 Industrial Blvd, City, State 12345',
        phone: '+****************',
        email: '<EMAIL>',
        managerId: 'user_1',
        isActive: true,
        type: 'main',
        capacity: {
          totalSpace: 10000,
          usedSpace: 7500,
          unit: 'sqft'
        },
        zones: [
          {
            id: 'zone_1',
            warehouseId: '1',
            name: 'Receiving Area',
            code: 'RCV001',
            type: 'receiving',
            capacity: 500,
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z'
          },
          {
            id: 'zone_2',
            warehouseId: '1',
            name: 'Storage Area A',
            code: 'STG001',
            type: 'storage',
            capacity: 3000,
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z'
          },
          {
            id: 'zone_3',
            warehouseId: '1',
            name: 'Picking Area',
            code: 'PCK001',
            type: 'picking',
            capacity: 800,
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z'
          }
        ],
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-15T00:00:00Z'
      },
      {
        id: '2',
        clientId: 'client_1',
        name: 'Branch Warehouse',
        code: 'WH002',
        address: '456 Commerce St, City, State 12345',
        phone: '+****************',
        managerId: 'user_2',
        isActive: true,
        type: 'branch',
        capacity: {
          totalSpace: 5000,
          usedSpace: 3200,
          unit: 'sqft'
        },
        zones: [
          {
            id: 'zone_4',
            warehouseId: '2',
            name: 'Storage Area B',
            code: 'STG002',
            type: 'storage',
            capacity: 2000,
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z'
          }
        ],
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-10T00:00:00Z'
      }
    ];
    setWarehouses(sampleWarehouses);
  }, []);

  const handleAddWarehouse = () => {
    setEditingWarehouse(null);
    setShowAddWarehouse(true);
  };

  const handleEditWarehouse = (warehouse: Warehouse) => {
    setEditingWarehouse(warehouse);
    setShowAddWarehouse(true);
  };

  const handleDeleteWarehouse = (warehouseId: string) => {
    if (window.confirm('Are you sure you want to delete this warehouse?')) {
      setWarehouses(prev => prev.filter(w => w.id !== warehouseId));
      toast.success('Warehouse deleted successfully');
    }
  };

  const handleToggleWarehouse = (warehouseId: string) => {
    setWarehouses(prev => prev.map(warehouse => 
      warehouse.id === warehouseId 
        ? { ...warehouse, isActive: !warehouse.isActive }
        : warehouse
    ));
    toast.success('Warehouse status updated');
  };

  const handleViewWarehouse = (warehouse: Warehouse) => {
    setSelectedWarehouse(warehouse);
    setView('details');
  };

  const getTypeIcon = (type: string) => {
    const icons = {
      main: '🏭',
      branch: '🏢',
      storage: '📦',
      distribution: '🚚'
    };
    return icons[type as keyof typeof icons] || '🏭';
  };

  const getZoneTypeIcon = (type: string) => {
    const icons = {
      receiving: '📥',
      storage: '📦',
      picking: '🎯',
      shipping: '📤',
      returns: '↩️'
    };
    return icons[type as keyof typeof icons] || '📦';
  };

  const calculateUtilization = (warehouse: Warehouse) => {
    if (!warehouse.capacity) return 0;
    return (warehouse.capacity.usedSpace / warehouse.capacity.totalSpace) * 100;
  };

  if (view === 'details' && selectedWarehouse) {
    return (
      <WarehouseDetails
        warehouse={selectedWarehouse}
        onBack={() => setView('list')}
        onEdit={() => handleEditWarehouse(selectedWarehouse)}
        onAddZone={() => setShowAddZone(true)}
      />
    );
  }

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Warehouse Management</h1>
          <p className="text-muted mb-0">Manage warehouse locations and operations</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-primary"
            onClick={() => toast.success('Warehouse analytics coming soon!')}
          >
            📊 Analytics
          </button>
          <button
            className="btn btn-primary"
            onClick={handleAddWarehouse}
          >
            ➕ Add Warehouse
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary bg-opacity-10 p-3 rounded">
                    <span className="text-primary fs-4">🏭</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Warehouses</h6>
                  <h4 className="mb-0">{warehouses.length}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-success bg-opacity-10 p-3 rounded">
                    <span className="text-success fs-4">✅</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Active Warehouses</h6>
                  <h4 className="mb-0">{warehouses.filter(w => w.isActive).length}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-warning bg-opacity-10 p-3 rounded">
                    <span className="text-warning fs-4">📦</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Zones</h6>
                  <h4 className="mb-0">{warehouses.reduce((sum, w) => sum + w.zones.length, 0)}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-info bg-opacity-10 p-3 rounded">
                    <span className="text-info fs-4">📏</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Space</h6>
                  <h4 className="mb-0">
                    {warehouses.reduce((sum, w) => sum + (w.capacity?.totalSpace || 0), 0).toLocaleString()} sqft
                  </h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Warehouses List */}
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">Warehouse Locations</h5>
        </div>
        <div className="card-body p-0">
          {warehouses.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <span className="display-1">🏭</span>
              </div>
              <h5>No Warehouses Configured</h5>
              <p className="text-muted">Add your first warehouse to start managing inventory locations</p>
              <button className="btn btn-primary" onClick={handleAddWarehouse}>
                Add Warehouse
              </button>
            </div>
          ) : (
            <div className="row g-0">
              {warehouses.map((warehouse) => (
                <div key={warehouse.id} className="col-lg-6 col-xl-4">
                  <div className="card border-0 border-end border-bottom">
                    <div className="card-body">
                      <div className="d-flex align-items-start justify-content-between mb-3">
                        <div className="d-flex align-items-center">
                          <span className="me-2 fs-4">{getTypeIcon(warehouse.type)}</span>
                          <div>
                            <h6 className="mb-0">{warehouse.name}</h6>
                            <small className="text-muted">{warehouse.code}</small>
                          </div>
                        </div>
                        <div className="dropdown">
                          <button
                            className="btn btn-sm btn-outline-secondary dropdown-toggle"
                            type="button"
                            data-bs-toggle="dropdown"
                          >
                            Actions
                          </button>
                          <ul className="dropdown-menu">
                            <li>
                              <button
                                className="dropdown-item"
                                onClick={() => handleViewWarehouse(warehouse)}
                              >
                                👁️ View Details
                              </button>
                            </li>
                            <li>
                              <button
                                className="dropdown-item"
                                onClick={() => handleEditWarehouse(warehouse)}
                              >
                                ✏️ Edit
                              </button>
                            </li>
                            <li>
                              <button
                                className="dropdown-item"
                                onClick={() => handleToggleWarehouse(warehouse.id)}
                              >
                                {warehouse.isActive ? '⏸️ Deactivate' : '▶️ Activate'}
                              </button>
                            </li>
                            <li><hr className="dropdown-divider" /></li>
                            <li>
                              <button
                                className="dropdown-item text-danger"
                                onClick={() => handleDeleteWarehouse(warehouse.id)}
                              >
                                🗑️ Delete
                              </button>
                            </li>
                          </ul>
                        </div>
                      </div>

                      <div className="mb-3">
                        <small className="text-muted d-block">{warehouse.address}</small>
                        {warehouse.phone && (
                          <small className="text-muted d-block">{warehouse.phone}</small>
                        )}
                      </div>

                      <div className="mb-3">
                        <div className="d-flex justify-content-between align-items-center mb-1">
                          <small className="text-muted">Space Utilization</small>
                          <small className="text-muted">
                            {warehouse.capacity ? `${calculateUtilization(warehouse).toFixed(1)}%` : 'N/A'}
                          </small>
                        </div>
                        {warehouse.capacity && (
                          <div className="progress" style={{ height: '6px' }}>
                            <div
                              className="progress-bar"
                              style={{ width: `${calculateUtilization(warehouse)}%` }}
                            ></div>
                          </div>
                        )}
                      </div>

                      <div className="d-flex justify-content-between align-items-center">
                        <div>
                          <span className={`badge ${warehouse.isActive ? 'bg-success' : 'bg-secondary'}`}>
                            {warehouse.isActive ? 'Active' : 'Inactive'}
                          </span>
                          <span className="badge bg-light text-dark ms-1">
                            {WAREHOUSE_TYPES.find(t => t.value === warehouse.type)?.label}
                          </span>
                        </div>
                        <small className="text-muted">
                          {warehouse.zones.length} zones
                        </small>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Add/Edit Warehouse Modal */}
      {showAddWarehouse && (
        <WarehouseModal
          warehouse={editingWarehouse}
          onSave={(warehouseData) => {
            if (editingWarehouse) {
              // Update existing warehouse
              setWarehouses(prev => prev.map(w => 
                w.id === editingWarehouse.id 
                  ? { ...w, ...warehouseData, updatedAt: new Date().toISOString() }
                  : w
              ));
              toast.success('Warehouse updated successfully');
            } else {
              // Add new warehouse
              const newWarehouse: Warehouse = {
                id: (warehouses.length + 1).toString(),
                clientId: 'client_1',
                ...warehouseData,
                zones: [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
              };
              setWarehouses(prev => [...prev, newWarehouse]);
              toast.success('Warehouse added successfully');
            }
            setShowAddWarehouse(false);
          }}
          onCancel={() => setShowAddWarehouse(false)}
        />
      )}
    </div>
  );
};

// Warehouse Details Component (placeholder)
const WarehouseDetails: React.FC<{
  warehouse: Warehouse;
  onBack: () => void;
  onEdit: () => void;
  onAddZone: () => void;
}> = ({ warehouse, onBack, onEdit, onAddZone }) => {
  return (
    <div className="container-fluid p-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div className="d-flex align-items-center">
          <button className="btn btn-outline-secondary me-3" onClick={onBack}>
            ← Back
          </button>
          <div>
            <h1 className="h3 mb-1">{warehouse.name}</h1>
            <p className="text-muted mb-0">{warehouse.code} • {warehouse.address}</p>
          </div>
        </div>
        <div className="d-flex gap-2">
          <button className="btn btn-outline-primary" onClick={onEdit}>
            ✏️ Edit Warehouse
          </button>
          <button className="btn btn-primary" onClick={onAddZone}>
            ➕ Add Zone
          </button>
        </div>
      </div>

      <div className="text-center py-5">
        <p>Warehouse details view will be implemented here</p>
        <p className="text-muted">This will include zone management, inventory tracking, and operational metrics</p>
      </div>
    </div>
  );
};

// Warehouse Modal Component (placeholder)
const WarehouseModal: React.FC<{
  warehouse: Warehouse | null;
  onSave: (data: WarehouseFormData) => void;
  onCancel: () => void;
}> = ({ warehouse, onSave, onCancel }) => {
  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-lg">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">
              {warehouse ? 'Edit Warehouse' : 'Add Warehouse'}
            </h5>
            <button type="button" className="btn-close" onClick={onCancel}></button>
          </div>
          <div className="modal-body">
            <div className="text-center py-4">
              <p>Warehouse Configuration Form</p>
              <p className="text-muted">This will be implemented in the next step</p>
            </div>
          </div>
          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onCancel}>
              Cancel
            </button>
            <button type="button" className="btn btn-primary" onClick={() => onSave({} as any)}>
              Save Warehouse
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WarehousePanel;
