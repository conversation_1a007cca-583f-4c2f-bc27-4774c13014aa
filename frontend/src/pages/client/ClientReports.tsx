import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface DateRange {
  startDate: string;
  endDate: string;
}

interface ReportData {
  sales: {
    totalRevenue: number;
    totalOrders: number;
    averageOrderValue: number;
    topProducts: Array<{ name: string; quantity: number; revenue: number }>;
    dailySales: Array<{ date: string; revenue: number; orders: number }>;
  };
  inventory: {
    totalProducts: number;
    lowStockItems: number;
    outOfStockItems: number;
    totalValue: number;
    topCategories: Array<{ name: string; count: number; value: number }>;
  };
  customers: {
    totalCustomers: number;
    newCustomers: number;
    activeCustomers: number;
    topCustomers: Array<{ name: string; orders: number; spent: number }>;
  };
  financial: {
    grossProfit: number;
    netProfit: number;
    expenses: number;
    profitMargin: number;
  };
}

const ClientReports: React.FC = () => {
  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    endDate: new Date().toISOString().split('T')[0] // today
  });

  const [selectedReport, setSelectedReport] = useState<string>('overview');
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(false);

  // Sample report data
  useEffect(() => {
    generateSampleData();
  }, [dateRange]);

  const generateSampleData = () => {
    setLoading(true);
    
    // Simulate API call delay
    setTimeout(() => {
      const sampleData: ReportData = {
        sales: {
          totalRevenue: 45750.25,
          totalOrders: 187,
          averageOrderValue: 244.65,
          topProducts: [
            { name: 'Wireless Headphones', quantity: 45, revenue: 4499.55 },
            { name: 'Office Chair', quantity: 12, revenue: 3599.88 },
            { name: 'Coffee Mug', quantity: 89, revenue: 1156.11 },
            { name: 'Laptop Stand', quantity: 23, revenue: 2299.77 },
            { name: 'Desk Lamp', quantity: 34, revenue: 1699.66 }
          ],
          dailySales: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            revenue: Math.random() * 2000 + 500,
            orders: Math.floor(Math.random() * 15) + 3
          }))
        },
        inventory: {
          totalProducts: 156,
          lowStockItems: 12,
          outOfStockItems: 3,
          totalValue: 89450.75,
          topCategories: [
            { name: 'Electronics', count: 45, value: 34500.25 },
            { name: 'Furniture', count: 23, value: 28750.50 },
            { name: 'Home & Garden', count: 34, value: 15200.00 },
            { name: 'Books', count: 28, value: 8900.00 },
            { name: 'Sports', count: 26, value: 12100.00 }
          ]
        },
        customers: {
          totalCustomers: 234,
          newCustomers: 18,
          activeCustomers: 156,
          topCustomers: [
            { name: 'John Smith', orders: 15, spent: 2450.75 },
            { name: 'Sarah Johnson', orders: 12, spent: 1890.25 },
            { name: 'Michael Brown', orders: 8, spent: 1650.00 },
            { name: 'Emily Davis', orders: 10, spent: 1420.50 },
            { name: 'David Wilson', orders: 7, spent: 1250.25 }
          ]
        },
        financial: {
          grossProfit: 27450.15,
          netProfit: 18300.10,
          expenses: 9150.05,
          profitMargin: 40.0
        }
      };

      setReportData(sampleData);
      setLoading(false);
    }, 1000);
  };

  const handleDateRangeChange = (field: keyof DateRange, value: string) => {
    setDateRange(prev => ({ ...prev, [field]: value }));
  };

  const exportReport = (format: 'pdf' | 'excel' | 'csv') => {
    toast.success(`Exporting report as ${format.toUpperCase()}...`);
    // Simulate export
    setTimeout(() => {
      toast.success(`Report exported successfully as ${format.toUpperCase()}`);
    }, 2000);
  };

  const refreshData = () => {
    generateSampleData();
    toast.success('Report data refreshed');
  };

  if (loading || !reportData) {
    return (
      <div className="container-fluid p-4">
        <div className="text-center py-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Generating reports...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Reports & Analytics</h1>
          <p className="text-muted mb-0">Comprehensive business insights and analytics</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-success"
            onClick={refreshData}
          >
            🔄 Refresh
          </button>
          <div className="dropdown">
            <button
              className="btn btn-primary dropdown-toggle"
              type="button"
              data-bs-toggle="dropdown"
            >
              📤 Export
            </button>
            <ul className="dropdown-menu">
              <li>
                <button className="dropdown-item" onClick={() => exportReport('pdf')}>
                  📄 Export as PDF
                </button>
              </li>
              <li>
                <button className="dropdown-item" onClick={() => exportReport('excel')}>
                  📊 Export as Excel
                </button>
              </li>
              <li>
                <button className="dropdown-item" onClick={() => exportReport('csv')}>
                  📋 Export as CSV
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Date Range and Report Type Selection */}
      <div className="card mb-4">
        <div className="card-body">
          <div className="row align-items-center">
            <div className="col-md-3">
              <label className="form-label">Start Date</label>
              <input
                type="date"
                className="form-control"
                value={dateRange.startDate}
                onChange={(e) => handleDateRangeChange('startDate', e.target.value)}
              />
            </div>
            <div className="col-md-3">
              <label className="form-label">End Date</label>
              <input
                type="date"
                className="form-control"
                value={dateRange.endDate}
                onChange={(e) => handleDateRangeChange('endDate', e.target.value)}
              />
            </div>
            <div className="col-md-4">
              <label className="form-label">Report Type</label>
              <select
                className="form-select"
                value={selectedReport}
                onChange={(e) => setSelectedReport(e.target.value)}
              >
                <option value="overview">📊 Overview</option>
                <option value="sales">💰 Sales Report</option>
                <option value="inventory">📦 Inventory Report</option>
                <option value="customers">👥 Customer Report</option>
                <option value="financial">💼 Financial Report</option>
                <option value="products">🛍️ Product Performance</option>
                <option value="profit-loss">📈 Profit & Loss</option>
              </select>
            </div>
            <div className="col-md-2">
              <label className="form-label">&nbsp;</label>
              <div className="d-flex gap-1">
                <button
                  className="btn btn-outline-primary btn-sm"
                  onClick={() => {
                    const today = new Date();
                    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                    setDateRange({
                      startDate: lastWeek.toISOString().split('T')[0],
                      endDate: today.toISOString().split('T')[0]
                    });
                  }}
                >
                  7D
                </button>
                <button
                  className="btn btn-outline-primary btn-sm"
                  onClick={() => {
                    const today = new Date();
                    const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
                    setDateRange({
                      startDate: lastMonth.toISOString().split('T')[0],
                      endDate: today.toISOString().split('T')[0]
                    });
                  }}
                >
                  30D
                </button>
                <button
                  className="btn btn-outline-primary btn-sm"
                  onClick={() => {
                    const today = new Date();
                    const lastQuarter = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);
                    setDateRange({
                      startDate: lastQuarter.toISOString().split('T')[0],
                      endDate: today.toISOString().split('T')[0]
                    });
                  }}
                >
                  90D
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Overview Dashboard */}
      {selectedReport === 'overview' && (
        <>
          {/* Key Metrics */}
          <div className="row mb-4">
            <div className="col-md-3">
              <div className="card">
                <div className="card-body">
                  <div className="d-flex align-items-center">
                    <div className="flex-shrink-0">
                      <div className="bg-success bg-opacity-10 p-3 rounded">
                        <span className="text-success fs-4">💰</span>
                      </div>
                    </div>
                    <div className="flex-grow-1 ms-3">
                      <h6 className="mb-0">Total Revenue</h6>
                      <h4 className="mb-0">${reportData.sales.totalRevenue.toLocaleString()}</h4>
                      <small className="text-success">+12.5% from last period</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="card">
                <div className="card-body">
                  <div className="d-flex align-items-center">
                    <div className="flex-shrink-0">
                      <div className="bg-primary bg-opacity-10 p-3 rounded">
                        <span className="text-primary fs-4">📋</span>
                      </div>
                    </div>
                    <div className="flex-grow-1 ms-3">
                      <h6 className="mb-0">Total Orders</h6>
                      <h4 className="mb-0">{reportData.sales.totalOrders}</h4>
                      <small className="text-primary">+8.3% from last period</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="card">
                <div className="card-body">
                  <div className="d-flex align-items-center">
                    <div className="flex-shrink-0">
                      <div className="bg-info bg-opacity-10 p-3 rounded">
                        <span className="text-info fs-4">👥</span>
                      </div>
                    </div>
                    <div className="flex-grow-1 ms-3">
                      <h6 className="mb-0">Active Customers</h6>
                      <h4 className="mb-0">{reportData.customers.activeCustomers}</h4>
                      <small className="text-info">+5.7% from last period</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="card">
                <div className="card-body">
                  <div className="d-flex align-items-center">
                    <div className="flex-shrink-0">
                      <div className="bg-warning bg-opacity-10 p-3 rounded">
                        <span className="text-warning fs-4">📈</span>
                      </div>
                    </div>
                    <div className="flex-grow-1 ms-3">
                      <h6 className="mb-0">Avg Order Value</h6>
                      <h4 className="mb-0">${reportData.sales.averageOrderValue.toFixed(2)}</h4>
                      <small className="text-warning">+3.2% from last period</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Charts Row */}
          <div className="row mb-4">
            <div className="col-md-8">
              <div className="card">
                <div className="card-header">
                  <h5 className="mb-0">Sales Trend</h5>
                </div>
                <div className="card-body">
                  <div className="text-center py-4">
                    <div className="bg-light rounded p-4">
                      <span className="display-1">📈</span>
                      <h5 className="mt-3">Sales Chart</h5>
                      <p className="text-muted">Interactive sales chart would be displayed here</p>
                      <small className="text-muted">
                        Daily revenue: ${reportData.sales.dailySales.slice(-7).reduce((sum, day) => sum + day.revenue, 0).toFixed(2)} (last 7 days)
                      </small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-4">
              <div className="card">
                <div className="card-header">
                  <h5 className="mb-0">Top Products</h5>
                </div>
                <div className="card-body">
                  {reportData.sales.topProducts.slice(0, 5).map((product, index) => (
                    <div key={index} className="d-flex justify-content-between align-items-center mb-3">
                      <div>
                        <div className="fw-medium">{product.name}</div>
                        <small className="text-muted">{product.quantity} sold</small>
                      </div>
                      <div className="text-end">
                        <div className="fw-bold">${product.revenue.toFixed(2)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Additional Metrics */}
          <div className="row">
            <div className="col-md-4">
              <div className="card">
                <div className="card-header">
                  <h5 className="mb-0">Inventory Status</h5>
                </div>
                <div className="card-body">
                  <div className="row text-center">
                    <div className="col-4">
                      <h4 className="text-primary">{reportData.inventory.totalProducts}</h4>
                      <small className="text-muted">Total Products</small>
                    </div>
                    <div className="col-4">
                      <h4 className="text-warning">{reportData.inventory.lowStockItems}</h4>
                      <small className="text-muted">Low Stock</small>
                    </div>
                    <div className="col-4">
                      <h4 className="text-danger">{reportData.inventory.outOfStockItems}</h4>
                      <small className="text-muted">Out of Stock</small>
                    </div>
                  </div>
                  <hr />
                  <div className="text-center">
                    <h5 className="text-success">${reportData.inventory.totalValue.toLocaleString()}</h5>
                    <small className="text-muted">Total Inventory Value</small>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-4">
              <div className="card">
                <div className="card-header">
                  <h5 className="mb-0">Customer Insights</h5>
                </div>
                <div className="card-body">
                  <div className="row text-center">
                    <div className="col-4">
                      <h4 className="text-info">{reportData.customers.totalCustomers}</h4>
                      <small className="text-muted">Total</small>
                    </div>
                    <div className="col-4">
                      <h4 className="text-success">{reportData.customers.newCustomers}</h4>
                      <small className="text-muted">New</small>
                    </div>
                    <div className="col-4">
                      <h4 className="text-primary">{reportData.customers.activeCustomers}</h4>
                      <small className="text-muted">Active</small>
                    </div>
                  </div>
                  <hr />
                  <div className="text-center">
                    <h5 className="text-warning">
                      {((reportData.customers.activeCustomers / reportData.customers.totalCustomers) * 100).toFixed(1)}%
                    </h5>
                    <small className="text-muted">Customer Retention Rate</small>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-4">
              <div className="card">
                <div className="card-header">
                  <h5 className="mb-0">Financial Summary</h5>
                </div>
                <div className="card-body">
                  <div className="mb-3">
                    <div className="d-flex justify-content-between">
                      <span>Gross Profit:</span>
                      <strong className="text-success">${reportData.financial.grossProfit.toLocaleString()}</strong>
                    </div>
                  </div>
                  <div className="mb-3">
                    <div className="d-flex justify-content-between">
                      <span>Expenses:</span>
                      <strong className="text-danger">${reportData.financial.expenses.toLocaleString()}</strong>
                    </div>
                  </div>
                  <hr />
                  <div className="mb-3">
                    <div className="d-flex justify-content-between">
                      <span>Net Profit:</span>
                      <strong className="text-primary">${reportData.financial.netProfit.toLocaleString()}</strong>
                    </div>
                  </div>
                  <div className="text-center">
                    <h5 className="text-info">{reportData.financial.profitMargin.toFixed(1)}%</h5>
                    <small className="text-muted">Profit Margin</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Other Report Types */}
      {selectedReport !== 'overview' && (
        <div className="card">
          <div className="card-header">
            <h5 className="mb-0">
              {selectedReport === 'sales' && '💰 Sales Report'}
              {selectedReport === 'inventory' && '📦 Inventory Report'}
              {selectedReport === 'customers' && '👥 Customer Report'}
              {selectedReport === 'financial' && '💼 Financial Report'}
              {selectedReport === 'products' && '🛍️ Product Performance'}
              {selectedReport === 'profit-loss' && '📈 Profit & Loss Report'}
            </h5>
          </div>
          <div className="card-body">
            <div className="text-center py-5">
              <div className="mb-3">
                <span className="display-1">
                  {selectedReport === 'sales' && '💰'}
                  {selectedReport === 'inventory' && '📦'}
                  {selectedReport === 'customers' && '👥'}
                  {selectedReport === 'financial' && '💼'}
                  {selectedReport === 'products' && '🛍️'}
                  {selectedReport === 'profit-loss' && '📈'}
                </span>
              </div>
              <h5>
                {selectedReport === 'sales' && 'Detailed Sales Analysis'}
                {selectedReport === 'inventory' && 'Comprehensive Inventory Report'}
                {selectedReport === 'customers' && 'Customer Analytics & Insights'}
                {selectedReport === 'financial' && 'Financial Performance Report'}
                {selectedReport === 'products' && 'Product Performance Analysis'}
                {selectedReport === 'profit-loss' && 'Profit & Loss Statement'}
              </h5>
              <p className="text-muted">
                Detailed {selectedReport} report with charts, tables, and insights would be displayed here
              </p>
              <div className="mt-4">
                <button
                  className="btn btn-primary me-2"
                  onClick={() => toast.success(`Generating detailed ${selectedReport} report...`)}
                >
                  📊 Generate Detailed Report
                </button>
                <button
                  className="btn btn-outline-secondary"
                  onClick={() => setSelectedReport('overview')}
                >
                  ← Back to Overview
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientReports;
