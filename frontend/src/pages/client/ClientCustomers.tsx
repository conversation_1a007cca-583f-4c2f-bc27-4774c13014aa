import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

// Timeline CSS
const timelineStyles = `
  .timeline {
    position: relative;
    padding-left: 30px;
  }

  .timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
  }

  .timeline-item {
    position: relative;
    margin-bottom: 20px;
  }

  .timeline-marker {
    position: absolute;
    left: -23px;
    top: 5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
  }

  .timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
  }

  .timeline-title {
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 600;
  }

  .timeline-text {
    margin-bottom: 5px;
    font-size: 13px;
    color: #6c757d;
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = timelineStyles;
  document.head.appendChild(styleElement);
}

interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  company?: string;
  status: 'active' | 'inactive' | 'blocked';
  customerType: 'individual' | 'business';
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  billingAddress?: Address;
  shippingAddress?: Address;
  notes?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

interface Address {
  name: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone?: string;
}

interface CustomerFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  company?: string;
  status: 'active' | 'inactive' | 'blocked';
  customerType: 'individual' | 'business';
  billingAddress?: Address;
  shippingAddress?: Address;
  notes?: string;
  tags: string[];
}

const ClientCustomers: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [showAddCustomer, setShowAddCustomer] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showCustomerDetails, setShowCustomerDetails] = useState(false);

  // Load sample data
  useEffect(() => {
    const sampleCustomers: Customer[] = [
      {
        id: '1',
        firstName: 'John',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+****************',
        company: 'Tech Solutions Inc',
        status: 'active',
        customerType: 'business',
        totalOrders: 15,
        totalSpent: 2450.75,
        lastOrderDate: '2024-01-15T10:30:00Z',
        billingAddress: {
          name: 'John Smith',
          company: 'Tech Solutions Inc',
          address1: '123 Business Ave',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'United States',
          phone: '+****************'
        },
        notes: 'VIP customer, prefers express shipping',
        tags: ['VIP', 'Business', 'Regular'],
        createdAt: '2023-06-15T00:00:00Z',
        updatedAt: '2024-01-15T10:30:00Z'
      },
      {
        id: '2',
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '+****************',
        status: 'active',
        customerType: 'individual',
        totalOrders: 8,
        totalSpent: 890.25,
        lastOrderDate: '2024-01-10T14:20:00Z',
        billingAddress: {
          name: 'Sarah Johnson',
          address1: '456 Residential St',
          city: 'Los Angeles',
          state: 'CA',
          zipCode: '90210',
          country: 'United States'
        },
        tags: ['Frequent', 'Individual'],
        createdAt: '2023-09-20T00:00:00Z',
        updatedAt: '2024-01-10T14:20:00Z'
      },
      {
        id: '3',
        firstName: 'Michael',
        lastName: 'Brown',
        email: '<EMAIL>',
        phone: '+****************',
        company: 'Brown Enterprises',
        status: 'inactive',
        customerType: 'business',
        totalOrders: 3,
        totalSpent: 1250.00,
        lastOrderDate: '2023-11-05T09:15:00Z',
        billingAddress: {
          name: 'Michael Brown',
          company: 'Brown Enterprises',
          address1: '789 Corporate Blvd',
          city: 'Chicago',
          state: 'IL',
          zipCode: '60601',
          country: 'United States'
        },
        notes: 'Seasonal customer, orders mainly in Q4',
        tags: ['Seasonal', 'Business'],
        createdAt: '2023-03-10T00:00:00Z',
        updatedAt: '2023-11-05T09:15:00Z'
      }
    ];
    setCustomers(sampleCustomers);
    setFilteredCustomers(sampleCustomers);
  }, []);

  // Filter customers
  useEffect(() => {
    let filtered = customers;

    if (searchTerm) {
      filtered = filtered.filter(customer =>
        customer.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.company?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(customer => customer.status === statusFilter);
    }

    if (typeFilter) {
      filtered = filtered.filter(customer => customer.customerType === typeFilter);
    }

    setFilteredCustomers(filtered);
  }, [customers, searchTerm, statusFilter, typeFilter]);

  const handleAddCustomer = () => {
    setEditingCustomer(null);
    setShowAddCustomer(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer);
    setShowAddCustomer(true);
  };

  const handleViewCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowCustomerDetails(true);
  };

  const handleDeleteCustomer = (customerId: string) => {
    if (window.confirm('Are you sure you want to delete this customer?')) {
      setCustomers(prev => prev.filter(c => c.id !== customerId));
      toast.success('Customer deleted successfully');
    }
  };

  const handleSaveCustomer = (customerData: CustomerFormData) => {
    if (editingCustomer) {
      setCustomers(prev => prev.map(c => 
        c.id === editingCustomer.id 
          ? { 
              ...c, 
              ...customerData, 
              updatedAt: new Date().toISOString() 
            }
          : c
      ));
      toast.success('Customer updated successfully');
    } else {
      const newCustomer: Customer = {
        id: (customers.length + 1).toString(),
        ...customerData,
        totalOrders: 0,
        totalSpent: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      setCustomers(prev => [...prev, newCustomer]);
      toast.success('Customer added successfully');
    }
    setShowAddCustomer(false);
  };

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'success',
      inactive: 'warning',
      blocked: 'danger'
    };
    return colors[status as keyof typeof colors] || 'secondary';
  };

  const getCustomerTypeIcon = (type: string) => {
    return type === 'business' ? '🏢' : '👤';
  };

  const getTotalCustomers = () => customers.length;
  const getActiveCustomers = () => customers.filter(c => c.status === 'active').length;
  const getTotalRevenue = () => customers.reduce((sum, c) => sum + c.totalSpent, 0);
  const getAverageOrderValue = () => {
    const totalOrders = customers.reduce((sum, c) => sum + c.totalOrders, 0);
    const totalRevenue = getTotalRevenue();
    return totalOrders > 0 ? totalRevenue / totalOrders : 0;
  };

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Customer Management</h1>
          <p className="text-muted mb-0">Manage your customer database and relationships</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-primary"
            onClick={() => toast.success('Customer reports coming soon!')}
          >
            📊 Reports
          </button>
          <button
            className="btn btn-outline-success"
            onClick={() => toast.success('Export customers coming soon!')}
          >
            📤 Export
          </button>
          <button
            className="btn btn-primary"
            onClick={handleAddCustomer}
          >
            ➕ Add Customer
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary bg-opacity-10 p-3 rounded">
                    <span className="text-primary fs-4">👥</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Customers</h6>
                  <h4 className="mb-0">{getTotalCustomers()}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-success bg-opacity-10 p-3 rounded">
                    <span className="text-success fs-4">✅</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Active Customers</h6>
                  <h4 className="mb-0">{getActiveCustomers()}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-info bg-opacity-10 p-3 rounded">
                    <span className="text-info fs-4">💰</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Revenue</h6>
                  <h4 className="mb-0">${getTotalRevenue().toFixed(2)}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-warning bg-opacity-10 p-3 rounded">
                    <span className="text-warning fs-4">📈</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Avg Order Value</h6>
                  <h4 className="mb-0">${getAverageOrderValue().toFixed(2)}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="card mb-4">
        <div className="card-body">
          <div className="row align-items-center">
            <div className="col-md-4">
              <input
                type="text"
                className="form-control"
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="col-md-2">
              <select
                className="form-select"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="blocked">Blocked</option>
              </select>
            </div>
            <div className="col-md-2">
              <select
                className="form-select"
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
              >
                <option value="">All Types</option>
                <option value="individual">Individual</option>
                <option value="business">Business</option>
              </select>
            </div>
            <div className="col-md-2">
              <button
                className="btn btn-outline-secondary w-100"
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                  setTypeFilter('');
                }}
              >
                🔄 Clear Filters
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Customers List */}
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">Customers ({filteredCustomers.length})</h5>
        </div>
        <div className="card-body p-0">
          {filteredCustomers.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <span className="display-1">👥</span>
              </div>
              <h5>No Customers Found</h5>
              <p className="text-muted">
                {searchTerm || statusFilter || typeFilter 
                  ? 'Try adjusting your filters' 
                  : 'Add your first customer to get started'
                }
              </p>
              {!searchTerm && !statusFilter && !typeFilter && (
                <button className="btn btn-primary" onClick={handleAddCustomer}>
                  Add Customer
                </button>
              )}
            </div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead className="table-light">
                  <tr>
                    <th>Customer</th>
                    <th>Contact</th>
                    <th>Type</th>
                    <th>Orders</th>
                    <th>Total Spent</th>
                    <th>Status</th>
                    <th>Last Order</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredCustomers.map((customer) => (
                    <tr key={customer.id}>
                      <td>
                        <div className="d-flex align-items-center">
                          <span className="me-2">{getCustomerTypeIcon(customer.customerType)}</span>
                          <div>
                            <div className="fw-medium">
                              {customer.firstName} {customer.lastName}
                            </div>
                            {customer.company && (
                              <small className="text-muted">{customer.company}</small>
                            )}
                          </div>
                        </div>
                      </td>
                      <td>
                        <div>
                          <div className="fw-medium">{customer.email}</div>
                          {customer.phone && (
                            <small className="text-muted">{customer.phone}</small>
                          )}
                        </div>
                      </td>
                      <td>
                        <span className="badge bg-light text-dark">
                          {customer.customerType}
                        </span>
                      </td>
                      <td>
                        <span className="fw-medium">{customer.totalOrders}</span>
                      </td>
                      <td>
                        <span className="fw-medium">${customer.totalSpent.toFixed(2)}</span>
                      </td>
                      <td>
                        <span className={`badge bg-${getStatusColor(customer.status)}`}>
                          {customer.status}
                        </span>
                      </td>
                      <td>
                        {customer.lastOrderDate ? (
                          <small className="text-muted">
                            {new Date(customer.lastOrderDate).toLocaleDateString()}
                          </small>
                        ) : (
                          <small className="text-muted">Never</small>
                        )}
                      </td>
                      <td>
                        <div className="btn-group btn-group-sm">
                          <button
                            className="btn btn-outline-primary"
                            onClick={() => handleViewCustomer(customer)}
                          >
                            👁️ View
                          </button>
                          <button
                            className="btn btn-outline-secondary"
                            onClick={() => handleEditCustomer(customer)}
                          >
                            ✏️ Edit
                          </button>
                          <button
                            className="btn btn-outline-danger"
                            onClick={() => handleDeleteCustomer(customer.id)}
                          >
                            🗑️ Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Add/Edit Customer Modal */}
      {showAddCustomer && (
        <CustomerModal
          customer={editingCustomer}
          onSave={handleSaveCustomer}
          onCancel={() => setShowAddCustomer(false)}
        />
      )}

      {/* Customer Details Modal */}
      {showCustomerDetails && selectedCustomer && (
        <CustomerDetailsModal
          customer={selectedCustomer}
          onClose={() => setShowCustomerDetails(false)}
          onEdit={() => {
            setShowCustomerDetails(false);
            handleEditCustomer(selectedCustomer);
          }}
        />
      )}
    </div>
  );
};

// Customer Modal Component
const CustomerModal: React.FC<{
  customer: Customer | null;
  onSave: (data: CustomerFormData) => void;
  onCancel: () => void;
}> = ({ customer, onSave, onCancel }) => {
  const [formData, setFormData] = useState<CustomerFormData>({
    firstName: customer?.firstName || '',
    lastName: customer?.lastName || '',
    email: customer?.email || '',
    phone: customer?.phone || '',
    company: customer?.company || '',
    status: customer?.status || 'active',
    customerType: customer?.customerType || 'individual',
    billingAddress: customer?.billingAddress || {
      name: '',
      address1: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'United States'
    },
    shippingAddress: customer?.shippingAddress,
    notes: customer?.notes || '',
    tags: customer?.tags || []
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [sameAsbilling, setSameAsBinding] = useState(!customer?.shippingAddress);
  const [newTag, setNewTag] = useState('');

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    if (formData.billingAddress && !formData.billingAddress.address1.trim()) {
      newErrors.billingAddress = 'Billing address is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      const submitData = {
        ...formData,
        shippingAddress: sameAsBinding ? formData.billingAddress : formData.shippingAddress
      };
      onSave(submitData);
    }
  };

  const handleInputChange = (field: keyof CustomerFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleAddressChange = (type: 'billingAddress' | 'shippingAddress', field: keyof Address, value: string) => {
    setFormData(prev => ({
      ...prev,
      [type]: {
        ...prev[type],
        [field]: value
      }
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-xl">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">
              {customer ? 'Edit Customer' : 'Add Customer'}
            </h5>
            <button type="button" className="btn-close" onClick={onCancel}></button>
          </div>
          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              <div className="row">
                {/* Basic Information */}
                <div className="col-md-6">
                  <div className="card h-100">
                    <div className="card-header">
                      <h6 className="mb-0">Basic Information</h6>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6 mb-3">
                          <label className="form-label">First Name *</label>
                          <input
                            type="text"
                            className={`form-control ${errors.firstName ? 'is-invalid' : ''}`}
                            value={formData.firstName}
                            onChange={(e) => handleInputChange('firstName', e.target.value)}
                            placeholder="John"
                          />
                          {errors.firstName && <div className="invalid-feedback">{errors.firstName}</div>}
                        </div>
                        <div className="col-md-6 mb-3">
                          <label className="form-label">Last Name *</label>
                          <input
                            type="text"
                            className={`form-control ${errors.lastName ? 'is-invalid' : ''}`}
                            value={formData.lastName}
                            onChange={(e) => handleInputChange('lastName', e.target.value)}
                            placeholder="Smith"
                          />
                          {errors.lastName && <div className="invalid-feedback">{errors.lastName}</div>}
                        </div>
                      </div>

                      <div className="mb-3">
                        <label className="form-label">Email *</label>
                        <input
                          type="email"
                          className={`form-control ${errors.email ? 'is-invalid' : ''}`}
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          placeholder="<EMAIL>"
                        />
                        {errors.email && <div className="invalid-feedback">{errors.email}</div>}
                      </div>

                      <div className="mb-3">
                        <label className="form-label">Phone</label>
                        <input
                          type="tel"
                          className="form-control"
                          value={formData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          placeholder="+****************"
                        />
                      </div>

                      <div className="row">
                        <div className="col-md-6 mb-3">
                          <label className="form-label">Customer Type</label>
                          <select
                            className="form-select"
                            value={formData.customerType}
                            onChange={(e) => handleInputChange('customerType', e.target.value)}
                          >
                            <option value="individual">Individual</option>
                            <option value="business">Business</option>
                          </select>
                        </div>
                        <div className="col-md-6 mb-3">
                          <label className="form-label">Status</label>
                          <select
                            className="form-select"
                            value={formData.status}
                            onChange={(e) => handleInputChange('status', e.target.value)}
                          >
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="blocked">Blocked</option>
                          </select>
                        </div>
                      </div>

                      {formData.customerType === 'business' && (
                        <div className="mb-3">
                          <label className="form-label">Company</label>
                          <input
                            type="text"
                            className="form-control"
                            value={formData.company}
                            onChange={(e) => handleInputChange('company', e.target.value)}
                            placeholder="Company Name"
                          />
                        </div>
                      )}

                      <div className="mb-3">
                        <label className="form-label">Notes</label>
                        <textarea
                          className="form-control"
                          rows={3}
                          value={formData.notes}
                          onChange={(e) => handleInputChange('notes', e.target.value)}
                          placeholder="Additional notes about the customer"
                        />
                      </div>

                      {/* Tags */}
                      <div className="mb-3">
                        <label className="form-label">Tags</label>
                        <div className="input-group mb-2">
                          <input
                            type="text"
                            className="form-control"
                            value={newTag}
                            onChange={(e) => setNewTag(e.target.value)}
                            placeholder="Add tag"
                            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                          />
                          <button
                            type="button"
                            className="btn btn-outline-secondary"
                            onClick={addTag}
                          >
                            Add
                          </button>
                        </div>
                        <div className="d-flex flex-wrap gap-1">
                          {formData.tags.map((tag, index) => (
                            <span key={index} className="badge bg-primary">
                              {tag}
                              <button
                                type="button"
                                className="btn-close btn-close-white ms-1"
                                style={{ fontSize: '0.7em' }}
                                onClick={() => removeTag(tag)}
                              ></button>
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Address Information */}
                <div className="col-md-6">
                  {/* Billing Address */}
                  <div className="card mb-3">
                    <div className="card-header">
                      <h6 className="mb-0">Billing Address</h6>
                    </div>
                    <div className="card-body">
                      <div className="mb-3">
                        <label className="form-label">Full Name</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.billingAddress?.name || ''}
                          onChange={(e) => handleAddressChange('billingAddress', 'name', e.target.value)}
                          placeholder="Full Name"
                        />
                      </div>

                      {formData.customerType === 'business' && (
                        <div className="mb-3">
                          <label className="form-label">Company</label>
                          <input
                            type="text"
                            className="form-control"
                            value={formData.billingAddress?.company || ''}
                            onChange={(e) => handleAddressChange('billingAddress', 'company', e.target.value)}
                            placeholder="Company Name"
                          />
                        </div>
                      )}

                      <div className="mb-3">
                        <label className="form-label">Address *</label>
                        <input
                          type="text"
                          className={`form-control ${errors.billingAddress ? 'is-invalid' : ''}`}
                          value={formData.billingAddress?.address1 || ''}
                          onChange={(e) => handleAddressChange('billingAddress', 'address1', e.target.value)}
                          placeholder="Street address"
                        />
                        {errors.billingAddress && <div className="invalid-feedback">{errors.billingAddress}</div>}
                      </div>

                      <div className="mb-3">
                        <input
                          type="text"
                          className="form-control"
                          value={formData.billingAddress?.address2 || ''}
                          onChange={(e) => handleAddressChange('billingAddress', 'address2', e.target.value)}
                          placeholder="Apartment, suite, etc. (optional)"
                        />
                      </div>

                      <div className="row">
                        <div className="col-md-6 mb-3">
                          <label className="form-label">City</label>
                          <input
                            type="text"
                            className="form-control"
                            value={formData.billingAddress?.city || ''}
                            onChange={(e) => handleAddressChange('billingAddress', 'city', e.target.value)}
                            placeholder="City"
                          />
                        </div>
                        <div className="col-md-6 mb-3">
                          <label className="form-label">State</label>
                          <input
                            type="text"
                            className="form-control"
                            value={formData.billingAddress?.state || ''}
                            onChange={(e) => handleAddressChange('billingAddress', 'state', e.target.value)}
                            placeholder="State"
                          />
                        </div>
                      </div>

                      <div className="row">
                        <div className="col-md-6 mb-3">
                          <label className="form-label">ZIP Code</label>
                          <input
                            type="text"
                            className="form-control"
                            value={formData.billingAddress?.zipCode || ''}
                            onChange={(e) => handleAddressChange('billingAddress', 'zipCode', e.target.value)}
                            placeholder="ZIP Code"
                          />
                        </div>
                        <div className="col-md-6 mb-3">
                          <label className="form-label">Country</label>
                          <select
                            className="form-select"
                            value={formData.billingAddress?.country || 'United States'}
                            onChange={(e) => handleAddressChange('billingAddress', 'country', e.target.value)}
                          >
                            <option value="United States">United States</option>
                            <option value="Canada">Canada</option>
                            <option value="United Kingdom">United Kingdom</option>
                            <option value="Australia">Australia</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Shipping Address */}
                  <div className="card">
                    <div className="card-header">
                      <div className="d-flex justify-content-between align-items-center">
                        <h6 className="mb-0">Shipping Address</h6>
                        <div className="form-check">
                          <input
                            className="form-check-input"
                            type="checkbox"
                            checked={sameAsBinding}
                            onChange={(e) => setSameAsBinding(e.target.checked)}
                          />
                          <label className="form-check-label">
                            Same as billing
                          </label>
                        </div>
                      </div>
                    </div>
                    {!sameAsBinding && (
                      <div className="card-body">
                        <p className="text-muted">Shipping address form would be similar to billing address</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onCancel}>
                Cancel
              </button>
              <button type="submit" className="btn btn-primary">
                {customer ? 'Update Customer' : 'Add Customer'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

// Customer Details Modal Component
const CustomerDetailsModal: React.FC<{
  customer: Customer;
  onClose: () => void;
  onEdit: () => void;
}> = ({ customer, onClose, onEdit }) => {
  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-xl">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">
              Customer Details - {customer.firstName} {customer.lastName}
            </h5>
            <button type="button" className="btn-close" onClick={onClose}></button>
          </div>
          <div className="modal-body">
            <div className="row">
              {/* Customer Overview */}
              <div className="col-md-4">
                <div className="card">
                  <div className="card-header">
                    <h6 className="mb-0">Customer Overview</h6>
                  </div>
                  <div className="card-body">
                    <div className="text-center mb-3">
                      <div className="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style={{ width: '80px', height: '80px' }}>
                        <span className="fs-1">{customer.customerType === 'business' ? '🏢' : '👤'}</span>
                      </div>
                      <h5 className="mt-2 mb-1">{customer.firstName} {customer.lastName}</h5>
                      <span className={`badge bg-${customer.status === 'active' ? 'success' : customer.status === 'inactive' ? 'warning' : 'danger'}`}>
                        {customer.status}
                      </span>
                    </div>

                    <div className="mb-3">
                      <strong>Email:</strong><br />
                      <a href={`mailto:${customer.email}`}>{customer.email}</a>
                    </div>

                    {customer.phone && (
                      <div className="mb-3">
                        <strong>Phone:</strong><br />
                        <a href={`tel:${customer.phone}`}>{customer.phone}</a>
                      </div>
                    )}

                    {customer.company && (
                      <div className="mb-3">
                        <strong>Company:</strong><br />
                        {customer.company}
                      </div>
                    )}

                    <div className="mb-3">
                      <strong>Customer Type:</strong><br />
                      <span className="badge bg-light text-dark">{customer.customerType}</span>
                    </div>

                    <div className="mb-3">
                      <strong>Member Since:</strong><br />
                      {new Date(customer.createdAt).toLocaleDateString()}
                    </div>

                    {customer.tags.length > 0 && (
                      <div className="mb-3">
                        <strong>Tags:</strong><br />
                        <div className="d-flex flex-wrap gap-1 mt-1">
                          {customer.tags.map((tag, index) => (
                            <span key={index} className="badge bg-primary">{tag}</span>
                          ))}
                        </div>
                      </div>
                    )}

                    {customer.notes && (
                      <div className="mb-3">
                        <strong>Notes:</strong><br />
                        <small className="text-muted">{customer.notes}</small>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Order Statistics */}
              <div className="col-md-4">
                <div className="card">
                  <div className="card-header">
                    <h6 className="mb-0">Order Statistics</h6>
                  </div>
                  <div className="card-body">
                    <div className="row text-center">
                      <div className="col-6 mb-3">
                        <div className="bg-info bg-opacity-10 p-3 rounded">
                          <h4 className="mb-1 text-info">{customer.totalOrders}</h4>
                          <small className="text-muted">Total Orders</small>
                        </div>
                      </div>
                      <div className="col-6 mb-3">
                        <div className="bg-success bg-opacity-10 p-3 rounded">
                          <h4 className="mb-1 text-success">${customer.totalSpent.toFixed(2)}</h4>
                          <small className="text-muted">Total Spent</small>
                        </div>
                      </div>
                      <div className="col-6 mb-3">
                        <div className="bg-warning bg-opacity-10 p-3 rounded">
                          <h4 className="mb-1 text-warning">
                            ${customer.totalOrders > 0 ? (customer.totalSpent / customer.totalOrders).toFixed(2) : '0.00'}
                          </h4>
                          <small className="text-muted">Avg Order Value</small>
                        </div>
                      </div>
                      <div className="col-6 mb-3">
                        <div className="bg-primary bg-opacity-10 p-3 rounded">
                          <h4 className="mb-1 text-primary">
                            {customer.lastOrderDate ? new Date(customer.lastOrderDate).toLocaleDateString() : 'Never'}
                          </h4>
                          <small className="text-muted">Last Order</small>
                        </div>
                      </div>
                    </div>

                    <div className="mt-3">
                      <button
                        className="btn btn-outline-primary w-100 mb-2"
                        onClick={() => toast.success('Order history coming soon!')}
                      >
                        📋 View Order History
                      </button>
                      <button
                        className="btn btn-outline-success w-100"
                        onClick={() => toast.success('Create order coming soon!')}
                      >
                        ➕ Create New Order
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div className="col-md-4">
                <div className="card">
                  <div className="card-header">
                    <h6 className="mb-0">Address Information</h6>
                  </div>
                  <div className="card-body">
                    {customer.billingAddress && (
                      <div className="mb-4">
                        <h6 className="text-primary">📍 Billing Address</h6>
                        <address className="mb-0">
                          {customer.billingAddress.name}<br />
                          {customer.billingAddress.company && (
                            <>{customer.billingAddress.company}<br /></>
                          )}
                          {customer.billingAddress.address1}<br />
                          {customer.billingAddress.address2 && (
                            <>{customer.billingAddress.address2}<br /></>
                          )}
                          {customer.billingAddress.city}, {customer.billingAddress.state} {customer.billingAddress.zipCode}<br />
                          {customer.billingAddress.country}
                          {customer.billingAddress.phone && (
                            <><br />📞 {customer.billingAddress.phone}</>
                          )}
                        </address>
                      </div>
                    )}

                    {customer.shippingAddress && customer.shippingAddress !== customer.billingAddress && (
                      <div className="mb-4">
                        <h6 className="text-success">🚚 Shipping Address</h6>
                        <address className="mb-0">
                          {customer.shippingAddress.name}<br />
                          {customer.shippingAddress.company && (
                            <>{customer.shippingAddress.company}<br /></>
                          )}
                          {customer.shippingAddress.address1}<br />
                          {customer.shippingAddress.address2 && (
                            <>{customer.shippingAddress.address2}<br /></>
                          )}
                          {customer.shippingAddress.city}, {customer.shippingAddress.state} {customer.shippingAddress.zipCode}<br />
                          {customer.shippingAddress.country}
                          {customer.shippingAddress.phone && (
                            <><br />📞 {customer.shippingAddress.phone}</>
                          )}
                        </address>
                      </div>
                    )}

                    <div className="mt-3">
                      <button
                        className="btn btn-outline-primary w-100 mb-2"
                        onClick={() => toast.success('Send email coming soon!')}
                      >
                        ✉️ Send Email
                      </button>
                      <button
                        className="btn btn-outline-info w-100"
                        onClick={() => toast.success('Customer activity coming soon!')}
                      >
                        📊 View Activity
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="row mt-4">
              <div className="col-12">
                <div className="card">
                  <div className="card-header">
                    <h6 className="mb-0">Recent Activity</h6>
                  </div>
                  <div className="card-body">
                    <div className="timeline">
                      <div className="timeline-item">
                        <div className="timeline-marker bg-success"></div>
                        <div className="timeline-content">
                          <h6 className="timeline-title">Order Completed</h6>
                          <p className="timeline-text">Order #ORD-2024-001 was completed successfully</p>
                          <small className="text-muted">2 days ago</small>
                        </div>
                      </div>
                      <div className="timeline-item">
                        <div className="timeline-marker bg-info"></div>
                        <div className="timeline-content">
                          <h6 className="timeline-title">Profile Updated</h6>
                          <p className="timeline-text">Customer updated their billing address</p>
                          <small className="text-muted">1 week ago</small>
                        </div>
                      </div>
                      <div className="timeline-item">
                        <div className="timeline-marker bg-primary"></div>
                        <div className="timeline-content">
                          <h6 className="timeline-title">Account Created</h6>
                          <p className="timeline-text">Customer account was created</p>
                          <small className="text-muted">{new Date(customer.createdAt).toLocaleDateString()}</small>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Close
            </button>
            <button type="button" className="btn btn-primary" onClick={onEdit}>
              ✏️ Edit Customer
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientCustomers;
