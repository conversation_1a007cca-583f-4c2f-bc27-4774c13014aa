import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { useNavigate } from 'react-router-dom';

interface Client {
  id: string;
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  domain: string;
  plan: 'starter' | 'professional' | 'enterprise' | 'custom';
  status: 'active' | 'suspended' | 'expired' | 'trial';
  licenseKey: string;
  createdAt: string;
  expiresAt: string;
  lastAccess: string;
  features: string[];
  maxUsers: number;
  usedUsers: number;
  maxStorage: number; // in GB
  usedStorage: number; // in GB
  billingCycle: 'monthly' | 'yearly';
  amount: number;
  currency: string;
}

interface PlanConfig {
  id: string;
  name: string;
  description: string;
  features: string[];
  maxUsers: number;
  maxStorage: number;
  monthlyPrice: number;
  yearlyPrice: number;
  popular?: boolean;
}

const ClientManagement: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('clients');
  const [clients, setClients] = useState<Client[]>([]);
  const [plans, setPlans] = useState<PlanConfig[]>([]);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [showAddClient, setShowAddClient] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [planFilter, setPlanFilter] = useState('all');
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  const planConfigs: PlanConfig[] = [
    {
      id: 'starter',
      name: 'Starter',
      description: 'Perfect for small businesses getting started',
      features: [
        'Basic Inventory Management',
        'Up to 1,000 Products',
        'Basic Reports',
        'Email Support',
        'Single Store Integration',
        'Basic Tally Sync'
      ],
      maxUsers: 3,
      maxStorage: 5,
      monthlyPrice: 29,
      yearlyPrice: 290
    },
    {
      id: 'professional',
      name: 'Professional',
      description: 'Advanced features for growing businesses',
      features: [
        'Advanced Inventory Management',
        'Up to 10,000 Products',
        'Advanced Reports & Analytics',
        'Priority Email Support',
        'Multiple Store Integrations',
        'Advanced Tally Sync',
        'API Access',
        'Custom Fields',
        'Barcode Scanning'
      ],
      maxUsers: 10,
      maxStorage: 25,
      monthlyPrice: 79,
      yearlyPrice: 790,
      popular: true
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      description: 'Complete solution for large organizations',
      features: [
        'Enterprise Inventory Management',
        'Unlimited Products',
        'Custom Reports & Dashboards',
        'Phone & Email Support',
        'Unlimited Store Integrations',
        'Real-time Tally Sync',
        'Full API Access',
        'Custom Integrations',
        'Advanced Security',
        'Multi-location Support',
        'White-label Options',
        'Dedicated Account Manager'
      ],
      maxUsers: 50,
      maxStorage: 100,
      monthlyPrice: 199,
      yearlyPrice: 1990
    },
    {
      id: 'custom',
      name: 'Custom',
      description: 'Tailored solution for specific requirements',
      features: [
        'All Enterprise Features',
        'Custom Development',
        'On-premise Deployment',
        'Custom Integrations',
        'SLA Guarantees',
        '24/7 Support'
      ],
      maxUsers: 999,
      maxStorage: 1000,
      monthlyPrice: 0,
      yearlyPrice: 0
    }
  ];

  useEffect(() => {
    loadClients();
    setPlans(planConfigs);
  }, []);

  const loadClients = () => {
    // Mock client data
    const mockClients: Client[] = [
      {
        id: '1',
        companyName: 'TechCorp Solutions',
        contactName: 'John Smith',
        email: '<EMAIL>',
        phone: '******-0123',
        domain: 'techcorp.com',
        plan: 'professional',
        status: 'active',
        licenseKey: 'TC-PRO-2024-001',
        createdAt: '2024-01-15',
        expiresAt: '2025-01-15',
        lastAccess: '2024-12-06 10:30:00',
        features: planConfigs.find(p => p.id === 'professional')?.features || [],
        maxUsers: 10,
        usedUsers: 7,
        maxStorage: 25,
        usedStorage: 18.5,
        billingCycle: 'yearly',
        amount: 790,
        currency: 'USD'
      },
      {
        id: '2',
        companyName: 'StartupXYZ',
        contactName: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '******-0456',
        domain: 'startupxyz.com',
        plan: 'starter',
        status: 'active',
        licenseKey: 'SX-STR-2024-002',
        createdAt: '2024-03-20',
        expiresAt: '2024-12-20',
        lastAccess: '2024-12-05 15:45:00',
        features: planConfigs.find(p => p.id === 'starter')?.features || [],
        maxUsers: 3,
        usedUsers: 2,
        maxStorage: 5,
        usedStorage: 3.2,
        billingCycle: 'monthly',
        amount: 29,
        currency: 'USD'
      },
      {
        id: '3',
        companyName: 'Enterprise Corp',
        contactName: 'Michael Brown',
        email: '<EMAIL>',
        phone: '******-0789',
        domain: 'enterprise.com',
        plan: 'enterprise',
        status: 'active',
        licenseKey: 'EC-ENT-2024-003',
        createdAt: '2024-02-10',
        expiresAt: '2025-02-10',
        lastAccess: '2024-12-06 09:15:00',
        features: planConfigs.find(p => p.id === 'enterprise')?.features || [],
        maxUsers: 50,
        usedUsers: 35,
        maxStorage: 100,
        usedStorage: 67.8,
        billingCycle: 'yearly',
        amount: 1990,
        currency: 'USD'
      },
      {
        id: '4',
        companyName: 'Trial Company',
        contactName: 'Lisa Wilson',
        email: '<EMAIL>',
        phone: '******-0321',
        domain: 'trialcompany.com',
        plan: 'professional',
        status: 'trial',
        licenseKey: 'TC-TRL-2024-004',
        createdAt: '2024-11-20',
        expiresAt: '2024-12-20',
        lastAccess: '2024-12-06 11:00:00',
        features: planConfigs.find(p => p.id === 'professional')?.features || [],
        maxUsers: 10,
        usedUsers: 3,
        maxStorage: 25,
        usedStorage: 2.1,
        billingCycle: 'monthly',
        amount: 0,
        currency: 'USD'
      }
    ];
    setClients(mockClients);
  };

  const filteredClients = clients.filter(client => {
    const matchesSearch = client.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.contactName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.domain.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || client.status === statusFilter;
    const matchesPlan = planFilter === 'all' || client.plan === planFilter;
    return matchesSearch && matchesStatus && matchesPlan;
  });

  const getStatusBadge = (status: string) => {
    const badges = {
      active: 'bg-success',
      suspended: 'bg-warning',
      expired: 'bg-danger',
      trial: 'bg-info'
    };
    return badges[status as keyof typeof badges] || 'bg-secondary';
  };

  const getPlanBadge = (plan: string) => {
    const badges = {
      starter: 'bg-primary',
      professional: 'bg-success',
      enterprise: 'bg-warning',
      custom: 'bg-dark'
    };
    return badges[plan as keyof typeof badges] || 'bg-secondary';
  };

  const getDaysUntilExpiry = (expiresAt: string) => {
    const expiry = new Date(expiresAt);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const handleExtendLicense = (clientId: string, months: number) => {
    setClients(prev => prev.map(client => {
      if (client.id === clientId) {
        const currentExpiry = new Date(client.expiresAt);
        const newExpiry = new Date(currentExpiry);
        newExpiry.setMonth(newExpiry.getMonth() + months);
        return {
          ...client,
          expiresAt: newExpiry.toISOString().split('T')[0],
          status: 'active' as const
        };
      }
      return client;
    }));
    toast.success(`License extended by ${months} months`);
  };

  const handleSuspendClient = (clientId: string) => {
    setClients(prev => prev.map(client => {
      if (client.id === clientId) {
        return { ...client, status: 'suspended' as const };
      }
      return client;
    }));
    toast.success('Client suspended successfully');
  };

  const handleActivateClient = (clientId: string) => {
    setClients(prev => prev.map(client => {
      if (client.id === clientId) {
        return { ...client, status: 'active' as const };
      }
      return client;
    }));
    toast.success('Client activated successfully');
  };



  const showAnalytics = () => {
    navigate('/analytics');
  };

  const showBilling = () => {
    navigate('/billing');
  };

  const sendExpiryNotices = () => {
    const expiringClients = clients.filter(client => {
      const daysUntilExpiry = getDaysUntilExpiry(client.expiresAt);
      return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
    });

    if (expiringClients.length === 0) {
      toast.info('No clients with expiring licenses found');
    } else {
      toast.success(`Expiry notices sent to ${expiringClients.length} clients`);
    }
  };

  const suspendExpiredClients = () => {
    const expiredClients = clients.filter(client => {
      const daysUntilExpiry = getDaysUntilExpiry(client.expiresAt);
      return daysUntilExpiry <= 0 && client.status !== 'expired';
    });

    if (expiredClients.length === 0) {
      toast.info('No expired clients found');
    } else {
      setClients(prev => prev.map(client => {
        const daysUntilExpiry = getDaysUntilExpiry(client.expiresAt);
        if (daysUntilExpiry <= 0 && client.status !== 'expired') {
          return { ...client, status: 'expired' as const };
        }
        return client;
      }));
      toast.success(`${expiredClients.length} expired clients suspended`);
    }
  };

  const exportReport = () => {
    const csvContent = [
      ['Company', 'Contact', 'Email', 'Domain', 'Plan', 'Status', 'Expires', 'Revenue'].join(','),
      ...clients.map(client => [
        client.companyName,
        client.contactName,
        client.email,
        client.domain,
        client.plan,
        client.status,
        client.expiresAt,
        `$${client.amount}`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `clients-report-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    toast.success('Client report exported successfully!');
  };

  const toggleDropdown = (clientId: string) => {
    setOpenDropdown(openDropdown === clientId ? null : clientId);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setOpenDropdown(null);
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Client Management</h1>
          <p className="text-muted mb-0">Manage SAAS clients, licenses, and subscriptions</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-primary"
            onClick={() => setShowAddClient(true)}
          >
            👥 Add Client
          </button>
          <button
            className="btn btn-outline-success"
            onClick={showAnalytics}
          >
            📊 Analytics
          </button>
          <button
            className="btn btn-primary"
            onClick={showBilling}
          >
            💰 Billing
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card bg-primary text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{clients.filter(c => c.status === 'active').length}</h4>
                  <small>Active Clients</small>
                </div>
                <div className="align-self-center">
                  <i className="fas fa-users fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-success text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">
                    ${clients.reduce((sum, c) => sum + (c.status === 'active' ? c.amount : 0), 0).toLocaleString()}
                  </h4>
                  <small>Monthly Revenue</small>
                </div>
                <div className="align-self-center">
                  <i className="fas fa-dollar-sign fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-warning text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{clients.filter(c => getDaysUntilExpiry(c.expiresAt) <= 30).length}</h4>
                  <small>Expiring Soon</small>
                </div>
                <div className="align-self-center">
                  <i className="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-info text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{clients.filter(c => c.status === 'trial').length}</h4>
                  <small>Trial Users</small>
                </div>
                <div className="align-self-center">
                  <i className="fas fa-clock fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <ul className="nav nav-tabs mb-4">
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'clients' ? 'active' : ''}`}
            onClick={() => setActiveTab('clients')}
          >
            👥 Clients ({clients.length})
          </button>
        </li>
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'plans' ? 'active' : ''}`}
            onClick={() => setActiveTab('plans')}
          >
            📋 Plans & Pricing
          </button>
        </li>
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'licenses' ? 'active' : ''}`}
            onClick={() => setActiveTab('licenses')}
          >
            🔑 License Management
          </button>
        </li>
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'notifications' ? 'active' : ''}`}
            onClick={() => setActiveTab('notifications')}
          >
            📧 Notifications
          </button>
        </li>
      </ul>

      {/* Clients Tab */}
      {activeTab === 'clients' && (
        <>
          {/* Filters */}
          <div className="card mb-4">
            <div className="card-body">
              <div className="row g-3">
                <div className="col-md-4">
                  <label className="form-label">Search Clients</label>
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Search by company, contact, email, or domain..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="col-md-3">
                  <label className="form-label">Status</label>
                  <select
                    className="form-select"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="trial">Trial</option>
                    <option value="suspended">Suspended</option>
                    <option value="expired">Expired</option>
                  </select>
                </div>
                <div className="col-md-3">
                  <label className="form-label">Plan</label>
                  <select
                    className="form-select"
                    value={planFilter}
                    onChange={(e) => setPlanFilter(e.target.value)}
                  >
                    <option value="all">All Plans</option>
                    <option value="starter">Starter</option>
                    <option value="professional">Professional</option>
                    <option value="enterprise">Enterprise</option>
                    <option value="custom">Custom</option>
                  </select>
                </div>
                <div className="col-md-2 d-flex align-items-end">
                  <button
                    className="btn btn-outline-secondary w-100"
                    onClick={() => {
                      setSearchTerm('');
                      setStatusFilter('all');
                      setPlanFilter('all');
                    }}
                  >
                    Clear
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Clients List */}
          <div className="card">
            <div className="card-header">
              <h5 className="mb-0">Clients ({filteredClients.length})</h5>
            </div>
            <div className="card-body p-0">
              {filteredClients.length === 0 ? (
                <div className="text-center py-5">
                  <i className="fas fa-users fa-3x text-muted mb-3"></i>
                  <h6 className="text-muted">No clients found</h6>
                  <p className="text-muted">
                    {searchTerm || statusFilter !== 'all' || planFilter !== 'all'
                      ? 'Try adjusting your search or filter criteria.'
                      : 'Add your first client to get started.'}
                  </p>
                </div>
              ) : (
                <div className="table-responsive">
                  <table className="table table-hover mb-0">
                    <thead className="table-light">
                      <tr>
                        <th>Company</th>
                        <th>Contact</th>
                        <th>Domain</th>
                        <th>Plan</th>
                        <th>Status</th>
                        <th>Expires</th>
                        <th>Usage</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredClients.map((client) => {
                        const daysUntilExpiry = getDaysUntilExpiry(client.expiresAt);
                        return (
                          <tr key={client.id}>
                            <td>
                              <div>
                                <strong>{client.companyName}</strong>
                                <br />
                                <small className="text-muted">{client.licenseKey}</small>
                              </div>
                            </td>
                            <td>
                              <div>
                                <div>{client.contactName}</div>
                                <small className="text-muted">{client.email}</small>
                              </div>
                            </td>
                            <td>
                              <span className="badge bg-light text-dark">{client.domain}</span>
                            </td>
                            <td>
                              <span className={`badge ${getPlanBadge(client.plan)}`}>
                                {client.plan.charAt(0).toUpperCase() + client.plan.slice(1)}
                              </span>
                            </td>
                            <td>
                              <span className={`badge ${getStatusBadge(client.status)}`}>
                                {client.status.charAt(0).toUpperCase() + client.status.slice(1)}
                              </span>
                            </td>
                            <td>
                              <div>
                                <div>{client.expiresAt}</div>
                                <small className={`${daysUntilExpiry <= 30 ? 'text-warning' : 'text-muted'}`}>
                                  {daysUntilExpiry > 0 ? `${daysUntilExpiry} days left` : `${Math.abs(daysUntilExpiry)} days overdue`}
                                </small>
                              </div>
                            </td>
                            <td>
                              <div>
                                <small>Users: {client.usedUsers}/{client.maxUsers}</small>
                                <div className="progress" style={{ height: '4px' }}>
                                  <div
                                    className="progress-bar"
                                    style={{ width: `${(client.usedUsers / client.maxUsers) * 100}%` }}
                                  ></div>
                                </div>
                                <small>Storage: {client.usedStorage}GB/{client.maxStorage}GB</small>
                              </div>
                            </td>
                            <td>
                              <div className="btn-group btn-group-sm">
                                <button
                                  className="btn btn-outline-primary"
                                  onClick={() => setSelectedClient(client)}
                                >
                                  👁️
                                </button>
                                <div className="btn-group btn-group-sm position-relative">
                                  <button
                                    className="btn btn-outline-secondary"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toggleDropdown(client.id);
                                    }}
                                  >
                                    ⚙️
                                  </button>
                                  {openDropdown === client.id && (
                                    <div
                                      className="dropdown-menu show position-absolute"
                                      style={{
                                        top: '100%',
                                        left: '0',
                                        zIndex: 1000,
                                        minWidth: '160px'
                                      }}
                                    >
                                      <button
                                        className="dropdown-item"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleExtendLicense(client.id, 1);
                                          setOpenDropdown(null);
                                        }}
                                      >
                                        📅 Extend 1 Month
                                      </button>
                                      <button
                                        className="dropdown-item"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleExtendLicense(client.id, 12);
                                          setOpenDropdown(null);
                                        }}
                                      >
                                        📅 Extend 1 Year
                                      </button>
                                      <hr className="dropdown-divider" />
                                      {client.status === 'active' ? (
                                        <button
                                          className="dropdown-item text-warning"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleSuspendClient(client.id);
                                            setOpenDropdown(null);
                                          }}
                                        >
                                          ⚠️ Suspend
                                        </button>
                                      ) : (
                                        <button
                                          className="dropdown-item text-success"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleActivateClient(client.id);
                                            setOpenDropdown(null);
                                          }}
                                        >
                                          ✅ Activate
                                        </button>
                                      )}
                                      <hr className="dropdown-divider" />
                                      <button
                                        className="dropdown-item text-info"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          setSelectedClient(client);
                                          setOpenDropdown(null);
                                        }}
                                      >
                                        📝 Edit Details
                                      </button>
                                      <button
                                        className="dropdown-item text-primary"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          navigator.clipboard.writeText(client.licenseKey);
                                          toast.success('License key copied to clipboard!');
                                          setOpenDropdown(null);
                                        }}
                                      >
                                        📋 Copy License Key
                                      </button>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </>
      )}

      {/* Plans & Pricing Tab */}
      {activeTab === 'plans' && (
        <div className="row">
          {planConfigs.map((plan) => (
            <div key={plan.id} className="col-md-6 col-lg-3 mb-4">
              <div className={`card h-100 ${plan.popular ? 'border-primary' : ''}`}>
                {plan.popular && (
                  <div className="card-header bg-primary text-white text-center">
                    <small>⭐ Most Popular</small>
                  </div>
                )}
                <div className="card-body d-flex flex-column">
                  <div className="text-center mb-3">
                    <h5 className="card-title">{plan.name}</h5>
                    <p className="card-text text-muted small">{plan.description}</p>
                    <div className="mb-3">
                      {plan.monthlyPrice > 0 ? (
                        <>
                          <h3 className="text-primary mb-0">${plan.monthlyPrice}</h3>
                          <small className="text-muted">per month</small>
                          <div className="mt-1">
                            <small className="text-success">
                              ${plan.yearlyPrice}/year (Save ${(plan.monthlyPrice * 12) - plan.yearlyPrice})
                            </small>
                          </div>
                        </>
                      ) : (
                        <h3 className="text-primary mb-0">Custom</h3>
                      )}
                    </div>
                  </div>

                  <div className="mb-3">
                    <small className="text-muted d-block mb-2">Plan Limits:</small>
                    <div className="row text-center">
                      <div className="col-6">
                        <strong>{plan.maxUsers === 999 ? '∞' : plan.maxUsers}</strong>
                        <br />
                        <small className="text-muted">Users</small>
                      </div>
                      <div className="col-6">
                        <strong>{plan.maxStorage === 1000 ? '∞' : plan.maxStorage}GB</strong>
                        <br />
                        <small className="text-muted">Storage</small>
                      </div>
                    </div>
                  </div>

                  <div className="flex-grow-1">
                    <h6 className="mb-2">Features:</h6>
                    <ul className="list-unstyled small">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="mb-1">
                          <i className="fas fa-check text-success me-2"></i>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="mt-auto">
                    <button className="btn btn-outline-primary w-100">
                      Edit Plan
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* License Management Tab */}
      {activeTab === 'licenses' && (
        <div className="row">
          <div className="col-md-8">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">License Configuration</h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-md-6">
                    <label className="form-label">Default Trial Period (days)</label>
                    <input type="number" className="form-control" defaultValue="30" />
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Grace Period (days)</label>
                    <input type="number" className="form-control" defaultValue="7" />
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">License Key Format</label>
                    <input type="text" className="form-control" defaultValue="{PREFIX}-{PLAN}-{YEAR}-{RANDOM}" readOnly />
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Domain Validation</label>
                    <select className="form-select">
                      <option value="strict">Strict (Exact match)</option>
                      <option value="subdomain">Allow Subdomains</option>
                      <option value="wildcard">Wildcard Support</option>
                    </select>
                  </div>
                  <div className="col-12">
                    <div className="form-check">
                      <input className="form-check-input" type="checkbox" id="autoSuspend" defaultChecked />
                      <label className="form-check-label" htmlFor="autoSuspend">
                        Automatically suspend expired licenses
                      </label>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="form-check">
                      <input className="form-check-input" type="checkbox" id="emailNotifications" defaultChecked />
                      <label className="form-check-label" htmlFor="emailNotifications">
                        Send email notifications for license expiry
                      </label>
                    </div>
                  </div>
                </div>
                <div className="mt-3">
                  <button
                    className="btn btn-primary"
                    onClick={() => toast.success('License configuration saved!')}
                  >
                    Save Configuration
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card">
              <div className="card-header">
                <h6 className="mb-0">License Statistics</h6>
              </div>
              <div className="card-body">
                <div className="row text-center">
                  <div className="col-6 mb-3">
                    <h4 className="text-success">{clients.filter(c => c.status === 'active').length}</h4>
                    <small className="text-muted">Active</small>
                  </div>
                  <div className="col-6 mb-3">
                    <h4 className="text-info">{clients.filter(c => c.status === 'trial').length}</h4>
                    <small className="text-muted">Trial</small>
                  </div>
                  <div className="col-6 mb-3">
                    <h4 className="text-warning">{clients.filter(c => c.status === 'suspended').length}</h4>
                    <small className="text-muted">Suspended</small>
                  </div>
                  <div className="col-6 mb-3">
                    <h4 className="text-danger">{clients.filter(c => c.status === 'expired').length}</h4>
                    <small className="text-muted">Expired</small>
                  </div>
                </div>
              </div>
            </div>

            <div className="card mt-3">
              <div className="card-header">
                <h6 className="mb-0">Quick Actions</h6>
              </div>
              <div className="card-body">
                <div className="d-grid gap-2">
                  <button
                    className="btn btn-outline-primary btn-sm"
                    onClick={() => setShowAddClient(true)}
                  >
                    🔑 Generate License
                  </button>
                  <button
                    className="btn btn-outline-warning btn-sm"
                    onClick={sendExpiryNotices}
                  >
                    📧 Send Expiry Notices
                  </button>
                  <button
                    className="btn btn-outline-danger btn-sm"
                    onClick={suspendExpiredClients}
                  >
                    🚫 Suspend Expired
                  </button>
                  <button
                    className="btn btn-outline-success btn-sm"
                    onClick={exportReport}
                  >
                    📊 Export Report
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Notifications Tab */}
      {activeTab === 'notifications' && (
        <div className="row">
          <div className="col-md-8">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">Email Notification Settings</h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-12">
                    <h6>License Expiry Notifications</h6>
                  </div>
                  <div className="col-md-4">
                    <div className="form-check">
                      <input className="form-check-input" type="checkbox" id="notify30" defaultChecked />
                      <label className="form-check-label" htmlFor="notify30">
                        30 days before expiry
                      </label>
                    </div>
                  </div>
                  <div className="col-md-4">
                    <div className="form-check">
                      <input className="form-check-input" type="checkbox" id="notify7" defaultChecked />
                      <label className="form-check-label" htmlFor="notify7">
                        7 days before expiry
                      </label>
                    </div>
                  </div>
                  <div className="col-md-4">
                    <div className="form-check">
                      <input className="form-check-input" type="checkbox" id="notify1" defaultChecked />
                      <label className="form-check-label" htmlFor="notify1">
                        1 day before expiry
                      </label>
                    </div>
                  </div>
                  <div className="col-md-4">
                    <div className="form-check">
                      <input className="form-check-input" type="checkbox" id="notifyExpired" defaultChecked />
                      <label className="form-check-label" htmlFor="notifyExpired">
                        On expiry day
                      </label>
                    </div>
                  </div>
                  <div className="col-md-4">
                    <div className="form-check">
                      <input className="form-check-input" type="checkbox" id="notifyOverdue" defaultChecked />
                      <label className="form-check-label" htmlFor="notifyOverdue">
                        7 days after expiry
                      </label>
                    </div>
                  </div>

                  <div className="col-12 mt-4">
                    <h6>Email Templates</h6>
                  </div>
                  <div className="col-12">
                    <label className="form-label">License Expiry Warning Template</label>
                    <textarea
                      className="form-control"
                      rows={4}
                      defaultValue={`Dear {CLIENT_NAME},

Your license for {PRODUCT_NAME} will expire in {DAYS_LEFT} days on {EXPIRY_DATE}.

Please renew your license to continue using our services.

License Key: {LICENSE_KEY}
Domain: {DOMAIN}

Best regards,
Support Team`}
                    />
                  </div>
                  <div className="col-12">
                    <label className="form-label">License Expired Template</label>
                    <textarea
                      className="form-control"
                      rows={4}
                      defaultValue={`Dear {CLIENT_NAME},

Your license for {PRODUCT_NAME} has expired on {EXPIRY_DATE}.

Your account has been suspended. Please renew your license immediately to restore access.

License Key: {LICENSE_KEY}
Domain: {DOMAIN}

Contact us for renewal options.

Best regards,
Support Team`}
                    />
                  </div>
                </div>
                <div className="mt-3">
                  <button
                    className="btn btn-primary me-2"
                    onClick={() => toast.success('Notification settings saved!')}
                  >
                    Save Settings
                  </button>
                  <button
                    className="btn btn-outline-secondary"
                    onClick={() => {
                      const email = prompt('Enter email address for test:');
                      if (email) {
                        toast.success(`Test email sent to ${email}`);
                      }
                    }}
                  >
                    Test Email
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card">
              <div className="card-header">
                <h6 className="mb-0">Recent Notifications</h6>
              </div>
              <div className="card-body">
                <div className="list-group list-group-flush">
                  <div className="list-group-item px-0">
                    <div className="d-flex justify-content-between">
                      <small className="text-muted">2024-12-06 10:30</small>
                      <span className="badge bg-warning">Expiry Warning</span>
                    </div>
                    <div className="mt-1">
                      <strong>StartupXYZ</strong>
                      <br />
                      <small>License expires in 14 days</small>
                    </div>
                  </div>
                  <div className="list-group-item px-0">
                    <div className="d-flex justify-content-between">
                      <small className="text-muted">2024-12-05 09:15</small>
                      <span className="badge bg-success">Renewed</span>
                    </div>
                    <div className="mt-1">
                      <strong>TechCorp Solutions</strong>
                      <br />
                      <small>License renewed for 1 year</small>
                    </div>
                  </div>
                  <div className="list-group-item px-0">
                    <div className="d-flex justify-content-between">
                      <small className="text-muted">2024-12-04 14:20</small>
                      <span className="badge bg-info">New Trial</span>
                    </div>
                    <div className="mt-1">
                      <strong>Trial Company</strong>
                      <br />
                      <small>Started 30-day trial</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Client Modal */}
      {showAddClient && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Add New Client</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowAddClient(false)}
                ></button>
              </div>
              <div className="modal-body">
                <AddClientForm
                  onSave={(clientData) => {
                    // Generate new client
                    const newClient: Client = {
                      id: (clients.length + 1).toString(),
                      ...clientData,
                      licenseKey: generateLicenseKey(clientData.companyName, clientData.plan),
                      createdAt: new Date().toISOString().split('T')[0],
                      expiresAt: calculateExpiryDate(clientData.billingCycle),
                      lastAccess: new Date().toISOString(),
                      features: getPlanFeatures(clientData.plan),
                      usedUsers: 0,
                      usedStorage: 0,
                      status: 'trial'
                    };

                    setClients(prev => [...prev, newClient]);
                    setShowAddClient(false);
                    toast.success('Client added successfully!');
                  }}
                  onCancel={() => setShowAddClient(false)}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Client Details Modal */}
      {selectedClient && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-xl">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Client Details - {selectedClient.companyName}</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setSelectedClient(null)}
                ></button>
              </div>
              <div className="modal-body">
                <ClientDetailsView client={selectedClient} />
              </div>
              <div className="modal-footer">
                <button
                  className="btn btn-secondary"
                  onClick={() => setSelectedClient(null)}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // Helper functions
  function calculateExpiryDate(billingCycle: 'monthly' | 'yearly'): string {
    const date = new Date();
    if (billingCycle === 'yearly') {
      date.setFullYear(date.getFullYear() + 1);
    } else {
      date.setMonth(date.getMonth() + 1);
    }
    return date.toISOString().split('T')[0];
  }

  function getPlanFeatures(plan: string): string[] {
    const planConfig = planConfigs.find(p => p.id === plan);
    return planConfig ? planConfig.features : [];
  }


};

// Add Client Form Component
interface AddClientFormProps {
  onSave: (clientData: Omit<Client, 'id' | 'licenseKey' | 'createdAt' | 'expiresAt' | 'lastAccess' | 'features' | 'usedUsers' | 'usedStorage' | 'status'>) => void;
  onCancel: () => void;
}

const AddClientForm: React.FC<AddClientFormProps> = ({ onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    companyName: '',
    contactName: '',
    email: '',
    phone: '',
    domain: '',
    plan: 'professional' as 'starter' | 'professional' | 'enterprise' | 'custom',
    billingCycle: 'monthly' as 'monthly' | 'yearly',
    amount: 79,
    currency: 'USD',
    maxUsers: 10,
    maxStorage: 25
  });

  const planPricing = {
    starter: { monthly: 29, yearly: 290, users: 3, storage: 5 },
    professional: { monthly: 79, yearly: 790, users: 10, storage: 25 },
    enterprise: { monthly: 199, yearly: 1990, users: 50, storage: 100 },
    custom: { monthly: 0, yearly: 0, users: 999, storage: 1000 }
  };

  const handlePlanChange = (plan: string) => {
    const pricing = planPricing[plan as keyof typeof planPricing];
    setFormData(prev => ({
      ...prev,
      plan: plan as any,
      amount: formData.billingCycle === 'yearly' ? pricing.yearly : pricing.monthly,
      maxUsers: pricing.users,
      maxStorage: pricing.storage
    }));
  };

  const handleBillingCycleChange = (cycle: 'monthly' | 'yearly') => {
    const pricing = planPricing[formData.plan];
    setFormData(prev => ({
      ...prev,
      billingCycle: cycle,
      amount: cycle === 'yearly' ? pricing.yearly : pricing.monthly
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="row g-3">
        <div className="col-md-6">
          <label className="form-label">Company Name *</label>
          <input
            type="text"
            className="form-control"
            value={formData.companyName}
            onChange={(e) => setFormData(prev => ({ ...prev, companyName: e.target.value }))}
            required
          />
        </div>
        <div className="col-md-6">
          <label className="form-label">Contact Name *</label>
          <input
            type="text"
            className="form-control"
            value={formData.contactName}
            onChange={(e) => setFormData(prev => ({ ...prev, contactName: e.target.value }))}
            required
          />
        </div>
        <div className="col-md-6">
          <label className="form-label">Email *</label>
          <input
            type="email"
            className="form-control"
            value={formData.email}
            onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
            required
          />
        </div>
        <div className="col-md-6">
          <label className="form-label">Phone</label>
          <input
            type="tel"
            className="form-control"
            value={formData.phone}
            onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
          />
        </div>
        <div className="col-md-6">
          <label className="form-label">Domain *</label>
          <input
            type="text"
            className="form-control"
            placeholder="example.com"
            value={formData.domain}
            onChange={(e) => setFormData(prev => ({ ...prev, domain: e.target.value }))}
            required
          />
        </div>
        <div className="col-md-6">
          <label className="form-label">Plan *</label>
          <select
            className="form-select"
            value={formData.plan}
            onChange={(e) => handlePlanChange(e.target.value)}
            required
          >
            <option value="starter">Starter - $29/month</option>
            <option value="professional">Professional - $79/month</option>
            <option value="enterprise">Enterprise - $199/month</option>
            <option value="custom">Custom - Contact Sales</option>
          </select>
        </div>
        <div className="col-md-6">
          <label className="form-label">Billing Cycle</label>
          <select
            className="form-select"
            value={formData.billingCycle}
            onChange={(e) => handleBillingCycleChange(e.target.value as 'monthly' | 'yearly')}
          >
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly (Save 20%)</option>
          </select>
        </div>
        <div className="col-md-6">
          <label className="form-label">Amount</label>
          <div className="input-group">
            <span className="input-group-text">$</span>
            <input
              type="number"
              className="form-control"
              value={formData.amount}
              onChange={(e) => setFormData(prev => ({ ...prev, amount: Number(e.target.value) }))}
              readOnly={formData.plan !== 'custom'}
            />
            <span className="input-group-text">
              {formData.billingCycle === 'yearly' ? '/year' : '/month'}
            </span>
          </div>
        </div>
        <div className="col-md-6">
          <label className="form-label">Max Users</label>
          <input
            type="number"
            className="form-control"
            value={formData.maxUsers}
            onChange={(e) => setFormData(prev => ({ ...prev, maxUsers: Number(e.target.value) }))}
            readOnly={formData.plan !== 'custom'}
          />
        </div>
        <div className="col-md-6">
          <label className="form-label">Max Storage (GB)</label>
          <input
            type="number"
            className="form-control"
            value={formData.maxStorage}
            onChange={(e) => setFormData(prev => ({ ...prev, maxStorage: Number(e.target.value) }))}
            readOnly={formData.plan !== 'custom'}
          />
        </div>
      </div>

      <div className="mt-4 d-flex justify-content-end gap-2">
        <button type="button" className="btn btn-secondary" onClick={onCancel}>
          Cancel
        </button>
        <button type="submit" className="btn btn-primary">
          Add Client
        </button>
      </div>
    </form>
  );
};

// Client Details View Component
interface ClientDetailsViewProps {
  client: Client;
}

const ClientDetailsView: React.FC<ClientDetailsViewProps> = ({ client }) => {
  const daysUntilExpiry = getDaysUntilExpiry(client.expiresAt);

  return (
    <div className="row g-4">
      <div className="col-md-6">
        <div className="card">
          <div className="card-header">
            <h6 className="mb-0">Company Information</h6>
          </div>
          <div className="card-body">
            <div className="row g-3">
              <div className="col-12">
                <label className="form-label">Company Name</label>
                <div className="form-control-plaintext">{client.companyName}</div>
              </div>
              <div className="col-12">
                <label className="form-label">Contact Person</label>
                <div className="form-control-plaintext">{client.contactName}</div>
              </div>
              <div className="col-12">
                <label className="form-label">Email</label>
                <div className="form-control-plaintext">{client.email}</div>
              </div>
              <div className="col-12">
                <label className="form-label">Phone</label>
                <div className="form-control-plaintext">{client.phone}</div>
              </div>
              <div className="col-12">
                <label className="form-label">Domain</label>
                <div className="form-control-plaintext">
                  <span className="badge bg-light text-dark">{client.domain}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="col-md-6">
        <div className="card">
          <div className="card-header">
            <h6 className="mb-0">License Information</h6>
          </div>
          <div className="card-body">
            <div className="row g-3">
              <div className="col-12">
                <label className="form-label">License Key</label>
                <div className="form-control-plaintext">
                  <code>{client.licenseKey}</code>
                </div>
              </div>
              <div className="col-6">
                <label className="form-label">Plan</label>
                <div className="form-control-plaintext">
                  <span className={`badge ${getPlanBadge(client.plan)}`}>
                    {client.plan.charAt(0).toUpperCase() + client.plan.slice(1)}
                  </span>
                </div>
              </div>
              <div className="col-6">
                <label className="form-label">Status</label>
                <div className="form-control-plaintext">
                  <span className={`badge ${getStatusBadge(client.status)}`}>
                    {client.status.charAt(0).toUpperCase() + client.status.slice(1)}
                  </span>
                </div>
              </div>
              <div className="col-6">
                <label className="form-label">Created</label>
                <div className="form-control-plaintext">{client.createdAt}</div>
              </div>
              <div className="col-6">
                <label className="form-label">Expires</label>
                <div className="form-control-plaintext">
                  {client.expiresAt}
                  <div className={`small ${daysUntilExpiry <= 30 ? 'text-warning' : 'text-muted'}`}>
                    {daysUntilExpiry > 0
                      ? `${daysUntilExpiry} days remaining`
                      : `${Math.abs(daysUntilExpiry)} days overdue`
                    }
                  </div>
                </div>
              </div>
              <div className="col-6">
                <label className="form-label">Last Access</label>
                <div className="form-control-plaintext">{client.lastAccess}</div>
              </div>
              <div className="col-6">
                <label className="form-label">Billing</label>
                <div className="form-control-plaintext">
                  ${client.amount} {client.currency} / {client.billingCycle}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="col-md-6">
        <div className="card">
          <div className="card-header">
            <h6 className="mb-0">Usage Statistics</h6>
          </div>
          <div className="card-body">
            <div className="mb-3">
              <label className="form-label">Users</label>
              <div className="progress mb-1">
                <div
                  className="progress-bar"
                  style={{ width: `${(client.usedUsers / client.maxUsers) * 100}%` }}
                ></div>
              </div>
              <small className="text-muted">{client.usedUsers} / {client.maxUsers} users</small>
            </div>

            <div className="mb-3">
              <label className="form-label">Storage</label>
              <div className="progress mb-1">
                <div
                  className="progress-bar bg-info"
                  style={{ width: `${(client.usedStorage / client.maxStorage) * 100}%` }}
                ></div>
              </div>
              <small className="text-muted">{client.usedStorage}GB / {client.maxStorage}GB</small>
            </div>
          </div>
        </div>
      </div>

      <div className="col-md-6">
        <div className="card">
          <div className="card-header">
            <h6 className="mb-0">Available Features</h6>
          </div>
          <div className="card-body">
            <div className="d-flex flex-wrap gap-1">
              {client.features.map((feature, index) => (
                <span key={index} className="badge bg-success">
                  {feature}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper functions for ClientDetailsView
function getDaysUntilExpiry(expiresAt: string): number {
  const expiry = new Date(expiresAt);
  const now = new Date();
  const diffTime = expiry.getTime() - now.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

function generateLicenseKey(companyName: string, plan: string): string {
  const prefix = companyName.substring(0, 2).toUpperCase();
  const planCode = plan.substring(0, 3).toUpperCase();
  const year = new Date().getFullYear();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${planCode}-${year}-${random}`;
}

function getStatusBadge(status: string): string {
  const badges = {
    active: 'bg-success',
    suspended: 'bg-warning',
    expired: 'bg-danger',
    trial: 'bg-info'
  };
  return badges[status as keyof typeof badges] || 'bg-secondary';
}

function getPlanBadge(plan: string): string {
  const badges = {
    starter: 'bg-primary',
    professional: 'bg-success',
    enterprise: 'bg-warning',
    custom: 'bg-dark'
  };
  return badges[plan as keyof typeof badges] || 'bg-secondary';
}

export default ClientManagement;
