import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface SystemSettings {
  // General Settings
  companyName: string;
  companyEmail: string;
  companyPhone: string;
  companyAddress: string;
  companyLogo: string;

  // Currency & Localization
  defaultCurrency: string;
  currencySymbol: string;
  currencyPosition: 'before' | 'after';
  decimalPlaces: number;
  thousandSeparator: string;
  decimalSeparator: string;

  // Language & Region
  language: string;
  timezone: string;
  dateFormat: string;
  timeFormat: '12' | '24';

  // Business Settings
  taxRate: number;
  taxIncluded: boolean;
  lowStockThreshold: number;
  autoReorderEnabled: boolean;
  autoReorderQuantity: number;

  // Notification Settings
  emailNotifications: boolean;
  lowStockAlerts: boolean;
  orderNotifications: boolean;
  customerNotifications: boolean;
  reportNotifications: boolean;

  // E-commerce Integration
  woocommerceEnabled: boolean;
  woocommerceUrl: string;
  woocommerceKey: string;
  shopifyEnabled: boolean;
  shopifyUrl: string;
  shopifyToken: string;
  magentoEnabled: boolean;
  magentoUrl: string;
  magentoToken: string;

  // Payment Settings
  paypalEnabled: boolean;
  stripeEnabled: boolean;
  razorpayEnabled: boolean;
  bankTransferEnabled: boolean;
  cashOnDeliveryEnabled: boolean;

  // Shipping Settings
  freeShippingThreshold: number;
  standardShippingRate: number;
  expressShippingRate: number;
  internationalShippingEnabled: boolean;

  // Security Settings
  twoFactorEnabled: boolean;
  sessionTimeout: number;
  passwordExpiry: number;
  loginAttempts: number;

  // Backup & Data
  autoBackupEnabled: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  dataRetentionPeriod: number;

  // API Settings
  apiEnabled: boolean;
  apiRateLimit: number;
  webhooksEnabled: boolean;
}

const Settings: React.FC = () => {
  const [settings, setSettings] = useState<SystemSettings>({
    // General Settings
    companyName: 'Inventory Management System',
    companyEmail: '<EMAIL>',
    companyPhone: '+****************',
    companyAddress: '123 Business Street, City, State 12345',
    companyLogo: '',

    // Currency & Localization
    defaultCurrency: 'USD',
    currencySymbol: '$',
    currencyPosition: 'before',
    decimalPlaces: 2,
    thousandSeparator: ',',
    decimalSeparator: '.',

    // Language & Region
    language: 'en',
    timezone: 'America/New_York',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12',

    // Business Settings
    taxRate: 8.5,
    taxIncluded: false,
    lowStockThreshold: 10,
    autoReorderEnabled: false,
    autoReorderQuantity: 50,

    // Notification Settings
    emailNotifications: true,
    lowStockAlerts: true,
    orderNotifications: true,
    customerNotifications: true,
    reportNotifications: false,

    // E-commerce Integration
    woocommerceEnabled: false,
    woocommerceUrl: '',
    woocommerceKey: '',
    shopifyEnabled: false,
    shopifyUrl: '',
    shopifyToken: '',
    magentoEnabled: false,
    magentoUrl: '',
    magentoToken: '',

    // Payment Settings
    paypalEnabled: true,
    stripeEnabled: true,
    razorpayEnabled: false,
    bankTransferEnabled: true,
    cashOnDeliveryEnabled: true,

    // Shipping Settings
    freeShippingThreshold: 100,
    standardShippingRate: 15,
    expressShippingRate: 25,
    internationalShippingEnabled: false,

    // Security Settings
    twoFactorEnabled: false,
    sessionTimeout: 30,
    passwordExpiry: 90,
    loginAttempts: 5,

    // Backup & Data
    autoBackupEnabled: true,
    backupFrequency: 'daily',
    dataRetentionPeriod: 365,

    // API Settings
    apiEnabled: true,
    apiRateLimit: 1000,
    webhooksEnabled: false
  });

  const [activeTab, setActiveTab] = useState('general');
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const currencies = [
    { code: 'USD', name: 'US Dollar', symbol: '$' },
    { code: 'EUR', name: 'Euro', symbol: '€' },
    { code: 'GBP', name: 'British Pound', symbol: '£' },
    { code: 'INR', name: 'Indian Rupee', symbol: '₹' },
    { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
    { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
    { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
    { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF' },
    { code: 'CNY', name: 'Chinese Yuan', symbol: '¥' },
    { code: 'SEK', name: 'Swedish Krona', symbol: 'kr' }
  ];

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Spanish' },
    { code: 'fr', name: 'French' },
    { code: 'de', name: 'German' },
    { code: 'it', name: 'Italian' },
    { code: 'pt', name: 'Portuguese' },
    { code: 'ru', name: 'Russian' },
    { code: 'ja', name: 'Japanese' },
    { code: 'ko', name: 'Korean' },
    { code: 'zh', name: 'Chinese' },
    { code: 'ar', name: 'Arabic' },
    { code: 'hi', name: 'Hindi' }
  ];

  const timezones = [
    'America/New_York',
    'America/Chicago',
    'America/Denver',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Europe/Berlin',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Asia/Kolkata',
    'Australia/Sydney',
    'Pacific/Auckland'
  ];

  const handleInputChange = (field: keyof SystemSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  };

  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Save to localStorage for persistence
      localStorage.setItem('inventorySettings', JSON.stringify(settings));

      setHasChanges(false);
      toast.success('Settings saved successfully!');
    } catch (error) {
      toast.error('Failed to save settings');
      console.error('Save settings error:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetSettings = () => {
    if (confirm('Are you sure you want to reset all settings to default values? This action cannot be undone.')) {
      // Reset to default values
      setSettings({
        companyName: 'Inventory Management System',
        companyEmail: '<EMAIL>',
        companyPhone: '+****************',
        companyAddress: '123 Business Street, City, State 12345',
        companyLogo: '',
        defaultCurrency: 'USD',
        currencySymbol: '$',
        currencyPosition: 'before',
        decimalPlaces: 2,
        thousandSeparator: ',',
        decimalSeparator: '.',
        language: 'en',
        timezone: 'America/New_York',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12',
        taxRate: 8.5,
        taxIncluded: false,
        lowStockThreshold: 10,
        autoReorderEnabled: false,
        autoReorderQuantity: 50,
        emailNotifications: true,
        lowStockAlerts: true,
        orderNotifications: true,
        customerNotifications: true,
        reportNotifications: false,
        woocommerceEnabled: false,
        woocommerceUrl: '',
        woocommerceKey: '',
        shopifyEnabled: false,
        shopifyUrl: '',
        shopifyToken: '',
        magentoEnabled: false,
        magentoUrl: '',
        magentoToken: '',
        paypalEnabled: true,
        stripeEnabled: true,
        razorpayEnabled: false,
        bankTransferEnabled: true,
        cashOnDeliveryEnabled: true,
        freeShippingThreshold: 100,
        standardShippingRate: 15,
        expressShippingRate: 25,
        internationalShippingEnabled: false,
        twoFactorEnabled: false,
        sessionTimeout: 30,
        passwordExpiry: 90,
        loginAttempts: 5,
        autoBackupEnabled: true,
        backupFrequency: 'daily',
        dataRetentionPeriod: 365,
        apiEnabled: true,
        apiRateLimit: 1000,
        webhooksEnabled: false
      });
      setHasChanges(true);
      toast.success('Settings reset to default values');
    }
  };

  // Load settings from localStorage on component mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('inventorySettings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(parsedSettings);
      } catch (error) {
        console.error('Error loading saved settings:', error);
      }
    }
  }, []);

  // Update currency symbol when currency changes
  useEffect(() => {
    const selectedCurrency = currencies.find(c => c.code === settings.defaultCurrency);
    if (selectedCurrency) {
      handleInputChange('currencySymbol', selectedCurrency.symbol);
    }
  }, [settings.defaultCurrency]);

  const tabs = [
    { id: 'general', name: 'General', icon: '🏢' },
    { id: 'currency', name: 'Currency & Localization', icon: '💱' },
    { id: 'business', name: 'Business', icon: '📊' },
    { id: 'notifications', name: 'Notifications', icon: '🔔' },
    { id: 'ecommerce', name: 'E-commerce', icon: '🛒' },
    { id: 'payments', name: 'Payments', icon: '💳' },
    { id: 'shipping', name: 'Shipping', icon: '📦' },
    { id: 'security', name: 'Security', icon: '🔒' },
    { id: 'backup', name: 'Backup & Data', icon: '💾' },
    { id: 'api', name: 'API', icon: '🔌' }
  ];

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">System Settings</h1>
          <p className="text-muted mb-0">Configure your inventory management system</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-secondary"
            onClick={handleResetSettings}
          >
            🔄 Reset to Defaults
          </button>
          <button
            className="btn btn-success"
            onClick={handleSaveSettings}
            disabled={!hasChanges || isSaving}
          >
            {isSaving ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Saving...
              </>
            ) : (
              <>💾 Save Settings</>
            )}
          </button>
        </div>
      </div>

      {/* Changes Indicator */}
      {hasChanges && (
        <div className="alert alert-warning mb-4">
          <strong>⚠️ Unsaved Changes:</strong> You have unsaved changes. Don't forget to save your settings.
        </div>
      )}

      {/* Tabs Navigation */}
      <div className="card mb-4">
        <div className="card-header p-0">
          <ul className="nav nav-tabs card-header-tabs">
            {tabs.map((tab) => (
              <li key={tab.id} className="nav-item">
                <button
                  className={`nav-link ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  {tab.icon} {tab.name}
                </button>
              </li>
            ))}
          </ul>
        </div>
        <div className="card-body">
          {/* General Settings Tab */}
          {activeTab === 'general' && (
            <div>
              <h5 className="mb-4">🏢 General Settings</h5>
              <div className="row g-3">
                <div className="col-md-6">
                  <label className="form-label">Company Name *</label>
                  <input
                    type="text"
                    className="form-control"
                    value={settings.companyName}
                    onChange={(e) => handleInputChange('companyName', e.target.value)}
                    placeholder="Your Company Name"
                  />
                </div>
                <div className="col-md-6">
                  <label className="form-label">Company Email *</label>
                  <input
                    type="email"
                    className="form-control"
                    value={settings.companyEmail}
                    onChange={(e) => handleInputChange('companyEmail', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="col-md-6">
                  <label className="form-label">Company Phone</label>
                  <input
                    type="tel"
                    className="form-control"
                    value={settings.companyPhone}
                    onChange={(e) => handleInputChange('companyPhone', e.target.value)}
                    placeholder="+****************"
                  />
                </div>
                <div className="col-md-6">
                  <label className="form-label">Company Address</label>
                  <input
                    type="text"
                    className="form-control"
                    value={settings.companyAddress}
                    onChange={(e) => handleInputChange('companyAddress', e.target.value)}
                    placeholder="123 Business Street, City, State 12345"
                  />
                </div>
                <div className="col-12">
                  <label className="form-label">Company Logo URL</label>
                  <input
                    type="url"
                    className="form-control"
                    value={settings.companyLogo}
                    onChange={(e) => handleInputChange('companyLogo', e.target.value)}
                    placeholder="https://example.com/logo.png"
                  />
                  <div className="form-text">Enter the URL of your company logo image</div>
                </div>
              </div>
            </div>
          )}

          {/* Currency & Localization Tab */}
          {activeTab === 'currency' && (
            <div>
              <h5 className="mb-4">💱 Currency & Localization</h5>
              <div className="row g-3">
                <div className="col-md-4">
                  <label className="form-label">Default Currency *</label>
                  <select
                    className="form-select"
                    value={settings.defaultCurrency}
                    onChange={(e) => handleInputChange('defaultCurrency', e.target.value)}
                  >
                    {currencies.map((currency) => (
                      <option key={currency.code} value={currency.code}>
                        {currency.code} - {currency.name} ({currency.symbol})
                      </option>
                    ))}
                  </select>
                </div>
                <div className="col-md-4">
                  <label className="form-label">Currency Symbol</label>
                  <input
                    type="text"
                    className="form-control"
                    value={settings.currencySymbol}
                    onChange={(e) => handleInputChange('currencySymbol', e.target.value)}
                    placeholder="$"
                  />
                </div>
                <div className="col-md-4">
                  <label className="form-label">Currency Position</label>
                  <select
                    className="form-select"
                    value={settings.currencyPosition}
                    onChange={(e) => handleInputChange('currencyPosition', e.target.value as 'before' | 'after')}
                  >
                    <option value="before">Before Amount ($100)</option>
                    <option value="after">After Amount (100$)</option>
                  </select>
                </div>
                <div className="col-md-4">
                  <label className="form-label">Decimal Places</label>
                  <select
                    className="form-select"
                    value={settings.decimalPlaces}
                    onChange={(e) => handleInputChange('decimalPlaces', parseInt(e.target.value))}
                  >
                    <option value={0}>0 (100)</option>
                    <option value={1}>1 (100.0)</option>
                    <option value={2}>2 (100.00)</option>
                    <option value={3}>3 (100.000)</option>
                  </select>
                </div>
                <div className="col-md-4">
                  <label className="form-label">Thousand Separator</label>
                  <select
                    className="form-select"
                    value={settings.thousandSeparator}
                    onChange={(e) => handleInputChange('thousandSeparator', e.target.value)}
                  >
                    <option value=",">Comma (1,000)</option>
                    <option value=".">Period (1.000)</option>
                    <option value=" ">Space (1 000)</option>
                    <option value="">None (1000)</option>
                  </select>
                </div>
                <div className="col-md-4">
                  <label className="form-label">Decimal Separator</label>
                  <select
                    className="form-select"
                    value={settings.decimalSeparator}
                    onChange={(e) => handleInputChange('decimalSeparator', e.target.value)}
                  >
                    <option value=".">Period (100.50)</option>
                    <option value=",">Comma (100,50)</option>
                  </select>
                </div>
                <div className="col-md-6">
                  <label className="form-label">Language</label>
                  <select
                    className="form-select"
                    value={settings.language}
                    onChange={(e) => handleInputChange('language', e.target.value)}
                  >
                    {languages.map((language) => (
                      <option key={language.code} value={language.code}>
                        {language.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="col-md-6">
                  <label className="form-label">Timezone</label>
                  <select
                    className="form-select"
                    value={settings.timezone}
                    onChange={(e) => handleInputChange('timezone', e.target.value)}
                  >
                    {timezones.map((timezone) => (
                      <option key={timezone} value={timezone}>
                        {timezone}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="col-md-6">
                  <label className="form-label">Date Format</label>
                  <select
                    className="form-select"
                    value={settings.dateFormat}
                    onChange={(e) => handleInputChange('dateFormat', e.target.value)}
                  >
                    <option value="MM/DD/YYYY">MM/DD/YYYY (12/31/2024)</option>
                    <option value="DD/MM/YYYY">DD/MM/YYYY (31/12/2024)</option>
                    <option value="YYYY-MM-DD">YYYY-MM-DD (2024-12-31)</option>
                    <option value="DD-MM-YYYY">DD-MM-YYYY (31-12-2024)</option>
                  </select>
                </div>
                <div className="col-md-6">
                  <label className="form-label">Time Format</label>
                  <select
                    className="form-select"
                    value={settings.timeFormat}
                    onChange={(e) => handleInputChange('timeFormat', e.target.value as '12' | '24')}
                  >
                    <option value="12">12 Hour (2:30 PM)</option>
                    <option value="24">24 Hour (14:30)</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {/* Business Settings Tab */}
          {activeTab === 'business' && (
            <div>
              <h5 className="mb-4">📊 Business Settings</h5>
              <div className="row g-3">
                <div className="col-md-6">
                  <label className="form-label">Tax Rate (%)</label>
                  <input
                    type="number"
                    className="form-control"
                    value={settings.taxRate}
                    onChange={(e) => handleInputChange('taxRate', parseFloat(e.target.value) || 0)}
                    min="0"
                    max="100"
                    step="0.1"
                    placeholder="8.5"
                  />
                  <div className="form-text">Default tax rate for products</div>
                </div>
                <div className="col-md-6">
                  <div className="form-check mt-4">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.taxIncluded}
                      onChange={(e) => handleInputChange('taxIncluded', e.target.checked)}
                    />
                    <label className="form-check-label">
                      Tax Included in Prices
                    </label>
                    <div className="form-text">Check if product prices include tax</div>
                  </div>
                </div>
                <div className="col-md-6">
                  <label className="form-label">Low Stock Threshold</label>
                  <input
                    type="number"
                    className="form-control"
                    value={settings.lowStockThreshold}
                    onChange={(e) => handleInputChange('lowStockThreshold', parseInt(e.target.value) || 0)}
                    min="0"
                    placeholder="10"
                  />
                  <div className="form-text">Alert when stock falls below this level</div>
                </div>
                <div className="col-md-6">
                  <label className="form-label">Auto Reorder Quantity</label>
                  <input
                    type="number"
                    className="form-control"
                    value={settings.autoReorderQuantity}
                    onChange={(e) => handleInputChange('autoReorderQuantity', parseInt(e.target.value) || 0)}
                    min="0"
                    placeholder="50"
                  />
                  <div className="form-text">Default quantity for auto reorders</div>
                </div>
                <div className="col-12">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.autoReorderEnabled}
                      onChange={(e) => handleInputChange('autoReorderEnabled', e.target.checked)}
                    />
                    <label className="form-check-label">
                      Enable Auto Reorder
                    </label>
                    <div className="form-text">Automatically create purchase orders when stock is low</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Notifications Tab */}
          {activeTab === 'notifications' && (
            <div>
              <h5 className="mb-4">🔔 Notification Settings</h5>
              <div className="row g-3">
                <div className="col-12">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.emailNotifications}
                      onChange={(e) => handleInputChange('emailNotifications', e.target.checked)}
                    />
                    <label className="form-check-label">
                      <strong>Enable Email Notifications</strong>
                    </label>
                    <div className="form-text">Master switch for all email notifications</div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.lowStockAlerts}
                      onChange={(e) => handleInputChange('lowStockAlerts', e.target.checked)}
                      disabled={!settings.emailNotifications}
                    />
                    <label className="form-check-label">
                      Low Stock Alerts
                    </label>
                    <div className="form-text">Get notified when products are running low</div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.orderNotifications}
                      onChange={(e) => handleInputChange('orderNotifications', e.target.checked)}
                      disabled={!settings.emailNotifications}
                    />
                    <label className="form-check-label">
                      Order Notifications
                    </label>
                    <div className="form-text">Get notified about new orders and status changes</div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.customerNotifications}
                      onChange={(e) => handleInputChange('customerNotifications', e.target.checked)}
                      disabled={!settings.emailNotifications}
                    />
                    <label className="form-check-label">
                      Customer Notifications
                    </label>
                    <div className="form-text">Get notified about new customer registrations</div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.reportNotifications}
                      onChange={(e) => handleInputChange('reportNotifications', e.target.checked)}
                      disabled={!settings.emailNotifications}
                    />
                    <label className="form-check-label">
                      Report Notifications
                    </label>
                    <div className="form-text">Get notified when scheduled reports are generated</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* E-commerce Integration Tab */}
          {activeTab === 'ecommerce' && (
            <div>
              <h5 className="mb-4">🛒 E-commerce Integration</h5>

              {/* WooCommerce */}
              <div className="card mb-3">
                <div className="card-header">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.woocommerceEnabled}
                      onChange={(e) => handleInputChange('woocommerceEnabled', e.target.checked)}
                    />
                    <label className="form-check-label">
                      <strong>WooCommerce Integration</strong>
                    </label>
                  </div>
                </div>
                {settings.woocommerceEnabled && (
                  <div className="card-body">
                    <div className="row g-3">
                      <div className="col-md-6">
                        <label className="form-label">Store URL</label>
                        <input
                          type="url"
                          className="form-control"
                          value={settings.woocommerceUrl}
                          onChange={(e) => handleInputChange('woocommerceUrl', e.target.value)}
                          placeholder="https://yourstore.com"
                        />
                      </div>
                      <div className="col-md-6">
                        <label className="form-label">API Key</label>
                        <input
                          type="password"
                          className="form-control"
                          value={settings.woocommerceKey}
                          onChange={(e) => handleInputChange('woocommerceKey', e.target.value)}
                          placeholder="ck_xxxxxxxxxxxxxxxx"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Shopify */}
              <div className="card mb-3">
                <div className="card-header">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.shopifyEnabled}
                      onChange={(e) => handleInputChange('shopifyEnabled', e.target.checked)}
                    />
                    <label className="form-check-label">
                      <strong>Shopify Integration</strong>
                    </label>
                  </div>
                </div>
                {settings.shopifyEnabled && (
                  <div className="card-body">
                    <div className="row g-3">
                      <div className="col-md-6">
                        <label className="form-label">Store URL</label>
                        <input
                          type="url"
                          className="form-control"
                          value={settings.shopifyUrl}
                          onChange={(e) => handleInputChange('shopifyUrl', e.target.value)}
                          placeholder="https://yourstore.myshopify.com"
                        />
                      </div>
                      <div className="col-md-6">
                        <label className="form-label">Access Token</label>
                        <input
                          type="password"
                          className="form-control"
                          value={settings.shopifyToken}
                          onChange={(e) => handleInputChange('shopifyToken', e.target.value)}
                          placeholder="shpat_xxxxxxxxxxxxxxxx"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Magento */}
              <div className="card mb-3">
                <div className="card-header">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.magentoEnabled}
                      onChange={(e) => handleInputChange('magentoEnabled', e.target.checked)}
                    />
                    <label className="form-check-label">
                      <strong>Magento Integration</strong>
                    </label>
                  </div>
                </div>
                {settings.magentoEnabled && (
                  <div className="card-body">
                    <div className="row g-3">
                      <div className="col-md-6">
                        <label className="form-label">Store URL</label>
                        <input
                          type="url"
                          className="form-control"
                          value={settings.magentoUrl}
                          onChange={(e) => handleInputChange('magentoUrl', e.target.value)}
                          placeholder="https://yourstore.com"
                        />
                      </div>
                      <div className="col-md-6">
                        <label className="form-label">Access Token</label>
                        <input
                          type="password"
                          className="form-control"
                          value={settings.magentoToken}
                          onChange={(e) => handleInputChange('magentoToken', e.target.value)}
                          placeholder="xxxxxxxxxxxxxxxx"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Payments Tab */}
          {activeTab === 'payments' && (
            <div>
              <h5 className="mb-4">💳 Payment Settings</h5>
              <div className="row g-3">
                <div className="col-md-6">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.paypalEnabled}
                      onChange={(e) => handleInputChange('paypalEnabled', e.target.checked)}
                    />
                    <label className="form-check-label">
                      <strong>PayPal</strong>
                    </label>
                    <div className="form-text">Enable PayPal payments</div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.stripeEnabled}
                      onChange={(e) => handleInputChange('stripeEnabled', e.target.checked)}
                    />
                    <label className="form-check-label">
                      <strong>Stripe</strong>
                    </label>
                    <div className="form-text">Enable Stripe credit card payments</div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.razorpayEnabled}
                      onChange={(e) => handleInputChange('razorpayEnabled', e.target.checked)}
                    />
                    <label className="form-check-label">
                      <strong>Razorpay</strong>
                    </label>
                    <div className="form-text">Enable Razorpay payments (India)</div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.bankTransferEnabled}
                      onChange={(e) => handleInputChange('bankTransferEnabled', e.target.checked)}
                    />
                    <label className="form-check-label">
                      <strong>Bank Transfer</strong>
                    </label>
                    <div className="form-text">Enable bank transfer payments</div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.cashOnDeliveryEnabled}
                      onChange={(e) => handleInputChange('cashOnDeliveryEnabled', e.target.checked)}
                    />
                    <label className="form-check-label">
                      <strong>Cash on Delivery</strong>
                    </label>
                    <div className="form-text">Enable cash on delivery option</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Shipping Tab */}
          {activeTab === 'shipping' && (
            <div>
              <h5 className="mb-4">📦 Shipping Settings</h5>
              <div className="row g-3">
                <div className="col-md-6">
                  <label className="form-label">Free Shipping Threshold</label>
                  <div className="input-group">
                    <span className="input-group-text">{settings.currencySymbol}</span>
                    <input
                      type="number"
                      className="form-control"
                      value={settings.freeShippingThreshold}
                      onChange={(e) => handleInputChange('freeShippingThreshold', parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.01"
                      placeholder="100.00"
                    />
                  </div>
                  <div className="form-text">Orders above this amount get free shipping</div>
                </div>
                <div className="col-md-6">
                  <label className="form-label">Standard Shipping Rate</label>
                  <div className="input-group">
                    <span className="input-group-text">{settings.currencySymbol}</span>
                    <input
                      type="number"
                      className="form-control"
                      value={settings.standardShippingRate}
                      onChange={(e) => handleInputChange('standardShippingRate', parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.01"
                      placeholder="15.00"
                    />
                  </div>
                  <div className="form-text">Standard shipping cost</div>
                </div>
                <div className="col-md-6">
                  <label className="form-label">Express Shipping Rate</label>
                  <div className="input-group">
                    <span className="input-group-text">{settings.currencySymbol}</span>
                    <input
                      type="number"
                      className="form-control"
                      value={settings.expressShippingRate}
                      onChange={(e) => handleInputChange('expressShippingRate', parseFloat(e.target.value) || 0)}
                      min="0"
                      step="0.01"
                      placeholder="25.00"
                    />
                  </div>
                  <div className="form-text">Express shipping cost</div>
                </div>
                <div className="col-md-6">
                  <div className="form-check mt-4">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.internationalShippingEnabled}
                      onChange={(e) => handleInputChange('internationalShippingEnabled', e.target.checked)}
                    />
                    <label className="form-check-label">
                      Enable International Shipping
                    </label>
                    <div className="form-text">Allow shipping to international addresses</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Security Tab */}
          {activeTab === 'security' && (
            <div>
              <h5 className="mb-4">🔒 Security Settings</h5>
              <div className="row g-3">
                <div className="col-md-6">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.twoFactorEnabled}
                      onChange={(e) => handleInputChange('twoFactorEnabled', e.target.checked)}
                    />
                    <label className="form-check-label">
                      <strong>Enable Two-Factor Authentication</strong>
                    </label>
                    <div className="form-text">Require 2FA for admin accounts</div>
                  </div>
                </div>
                <div className="col-md-6">
                  <label className="form-label">Session Timeout (minutes)</label>
                  <input
                    type="number"
                    className="form-control"
                    value={settings.sessionTimeout}
                    onChange={(e) => handleInputChange('sessionTimeout', parseInt(e.target.value) || 30)}
                    min="5"
                    max="480"
                    placeholder="30"
                  />
                  <div className="form-text">Auto-logout after inactivity</div>
                </div>
                <div className="col-md-6">
                  <label className="form-label">Password Expiry (days)</label>
                  <input
                    type="number"
                    className="form-control"
                    value={settings.passwordExpiry}
                    onChange={(e) => handleInputChange('passwordExpiry', parseInt(e.target.value) || 90)}
                    min="30"
                    max="365"
                    placeholder="90"
                  />
                  <div className="form-text">Force password change after this period</div>
                </div>
                <div className="col-md-6">
                  <label className="form-label">Max Login Attempts</label>
                  <input
                    type="number"
                    className="form-control"
                    value={settings.loginAttempts}
                    onChange={(e) => handleInputChange('loginAttempts', parseInt(e.target.value) || 5)}
                    min="3"
                    max="10"
                    placeholder="5"
                  />
                  <div className="form-text">Lock account after failed attempts</div>
                </div>
              </div>
            </div>
          )}

          {/* Backup & Data Tab */}
          {activeTab === 'backup' && (
            <div>
              <h5 className="mb-4">💾 Backup & Data Settings</h5>
              <div className="row g-3">
                <div className="col-md-6">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.autoBackupEnabled}
                      onChange={(e) => handleInputChange('autoBackupEnabled', e.target.checked)}
                    />
                    <label className="form-check-label">
                      <strong>Enable Auto Backup</strong>
                    </label>
                    <div className="form-text">Automatically backup data</div>
                  </div>
                </div>
                <div className="col-md-6">
                  <label className="form-label">Backup Frequency</label>
                  <select
                    className="form-select"
                    value={settings.backupFrequency}
                    onChange={(e) => handleInputChange('backupFrequency', e.target.value as 'daily' | 'weekly' | 'monthly')}
                    disabled={!settings.autoBackupEnabled}
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                  <div className="form-text">How often to create backups</div>
                </div>
                <div className="col-md-6">
                  <label className="form-label">Data Retention Period (days)</label>
                  <input
                    type="number"
                    className="form-control"
                    value={settings.dataRetentionPeriod}
                    onChange={(e) => handleInputChange('dataRetentionPeriod', parseInt(e.target.value) || 365)}
                    min="30"
                    max="2555"
                    placeholder="365"
                  />
                  <div className="form-text">How long to keep historical data</div>
                </div>
              </div>
            </div>
          )}

          {/* API Tab */}
          {activeTab === 'api' && (
            <div>
              <h5 className="mb-4">🔌 API Settings</h5>
              <div className="row g-3">
                <div className="col-md-6">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.apiEnabled}
                      onChange={(e) => handleInputChange('apiEnabled', e.target.checked)}
                    />
                    <label className="form-check-label">
                      <strong>Enable API Access</strong>
                    </label>
                    <div className="form-text">Allow external API access</div>
                  </div>
                </div>
                <div className="col-md-6">
                  <label className="form-label">API Rate Limit (requests/hour)</label>
                  <input
                    type="number"
                    className="form-control"
                    value={settings.apiRateLimit}
                    onChange={(e) => handleInputChange('apiRateLimit', parseInt(e.target.value) || 1000)}
                    min="100"
                    max="10000"
                    placeholder="1000"
                    disabled={!settings.apiEnabled}
                  />
                  <div className="form-text">Maximum API requests per hour</div>
                </div>
                <div className="col-md-6">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      checked={settings.webhooksEnabled}
                      onChange={(e) => handleInputChange('webhooksEnabled', e.target.checked)}
                      disabled={!settings.apiEnabled}
                    />
                    <label className="form-check-label">
                      <strong>Enable Webhooks</strong>
                    </label>
                    <div className="form-text">Allow webhook notifications</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;
