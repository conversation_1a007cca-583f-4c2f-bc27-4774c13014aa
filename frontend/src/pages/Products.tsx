import React, { useState } from 'react';
import ProductTable from '../components/Products/ProductTable';
import ProductForm from '../components/Products/ProductForm';

interface Product {
  id?: string;
  name: string;
  sku: string;
  category: string;
  price: number;
  stock: number;
  minStock: number;
  description: string;
  status: 'active' | 'inactive';
}

const Products: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [products, setProducts] = useState<Product[]>([]);

  const handleAddProduct = () => {
    setEditingProduct(null);
    setShowForm(true);
  };

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setShowForm(true);
  };

  const handleDeleteProduct = (productId: string) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      setProducts(prev => prev.filter(p => p.id !== productId));
      // Here you would typically make an API call to delete the product
      console.log('Deleting product:', productId);
    }
  };

  const handleViewProduct = (product: Product) => {
    // Here you could open a modal or navigate to a detail page
    console.log('Viewing product:', product);
    alert(`Viewing product: ${product.name}`);
  };

  const handleSaveProduct = (productData: Product) => {
    if (editingProduct) {
      // Update existing product
      setProducts(prev => prev.map(p =>
        p.id === editingProduct.id ? { ...productData, id: editingProduct.id } : p
      ));
      console.log('Updated product:', productData);
    } else {
      // Add new product
      const newProduct = {
        ...productData,
        id: `PRD-${Date.now()}`
      };
      setProducts(prev => [...prev, newProduct]);
      console.log('Added new product:', newProduct);
    }

    setShowForm(false);
    setEditingProduct(null);
  };

  const handleCancelForm = () => {
    setShowForm(false);
    setEditingProduct(null);
  };

  if (showForm) {
    return (
      <div className="container-fluid">
        <div className="row mb-4">
          <div className="col-12">
            <div className="d-flex align-items-center">
              <button
                className="btn btn-outline-secondary me-3"
                onClick={handleCancelForm}
              >
                ← Back to Products
              </button>
              <div>
                <h4 className="mb-0 fw-bold">
                  {editingProduct ? 'Edit Product' : 'Add New Product'}
                </h4>
                <p className="text-muted mb-0">
                  {editingProduct ? 'Update product information' : 'Add a new product to your inventory'}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="row">
          <div className="col-12">
            <ProductForm
              product={editingProduct || undefined}
              onSave={handleSaveProduct}
              onCancel={handleCancelForm}
              isEdit={!!editingProduct}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Page Title */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-sm-flex align-items-center justify-content-between">
            <div>
              <h4 className="mb-1 fw-bold">Products</h4>
              <p className="text-muted mb-0">Manage your inventory products and stock levels</p>
            </div>
            <div className="d-flex gap-2">
              <button className="btn btn-outline-primary btn-sm">
                📤 Export
              </button>
              <button className="btn btn-outline-secondary btn-sm">
                📥 Import
              </button>
              <button
                className="btn btn-primary btn-sm"
                onClick={handleAddProduct}
              >
                ➕ Add Product
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Products Table */}
      <div className="row">
        <div className="col-12">
          <ProductTable
            products={products}
            onEdit={handleEditProduct}
            onDelete={handleDeleteProduct}
            onView={handleViewProduct}
          />
        </div>
      </div>
    </div>
  );
};

export default Products;
