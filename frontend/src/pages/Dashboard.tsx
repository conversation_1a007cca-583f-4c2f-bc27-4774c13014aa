import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import StatsCard from '../components/Dashboard/StatsCard';
import RecentOrders from '../components/Dashboard/RecentOrders';
import LowStockAlert from '../components/Dashboard/LowStockAlert';
import SalesChart from '../components/Dashboard/SalesChart';

interface DashboardStats {
  totalProducts: number;
  totalOrders: number;
  totalCustomers: number;
  revenue: number;
  lowStockItems: number;
  pendingOrders: number;
  monthlyGrowth: {
    products: number;
    orders: number;
    customers: number;
    revenue: number;
  };
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalOrders: 0,
    totalCustomers: 0,
    revenue: 0,
    lowStockItems: 0,
    pendingOrders: 0,
    monthlyGrowth: {
      products: 0,
      orders: 0,
      customers: 0,
      revenue: 0
    }
  });
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('30d');

  // Load dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock data - replace with actual API calls
        setStats({
          totalProducts: 1234,
          totalOrders: 567,
          totalCustomers: 89,
          revenue: 45678.90,
          lowStockItems: 12,
          pendingOrders: 8,
          monthlyGrowth: {
            products: 12.5,
            orders: 8.2,
            customers: 15.3,
            revenue: 23.1
          }
        });
      } catch (error) {
        toast.error('Failed to load dashboard data');
        console.error('Dashboard error:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [dateRange]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  return (
    <div className="container-fluid">
      {/* Page Title */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-sm-flex align-items-center justify-content-between">
            <div>
              <h4 className="mb-1 fw-bold">Dashboard</h4>
              <p className="text-muted mb-0">Welcome back! Here's what's happening with your inventory.</p>
            </div>
            <div className="d-flex gap-2">
              <div className="d-flex gap-2">
                <select
                  className="form-select form-select-sm"
                  value={dateRange}
                  onChange={(e) => setDateRange(e.target.value)}
                  style={{ width: 'auto' }}
                >
                  <option value="7d">Last 7 days</option>
                  <option value="30d">Last 30 days</option>
                  <option value="90d">Last 90 days</option>
                  <option value="1y">Last year</option>
                </select>
                <button className="btn btn-outline-primary btn-sm">
                  📊 Export Report
                </button>
                <button className="btn btn-primary btn-sm">
                  ➕ Add Product
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards Row */}
      <div className="row">
        <StatsCard
          title="Total Products"
          value={loading ? "..." : formatNumber(stats.totalProducts)}
          change={loading ? "..." : `+${stats.monthlyGrowth.products}%`}
          changeType="positive"
          icon="📦"
          color="primary"
        />
        <StatsCard
          title="Total Orders"
          value={loading ? "..." : formatNumber(stats.totalOrders)}
          change={loading ? "..." : `+${stats.monthlyGrowth.orders}%`}
          changeType="positive"
          icon="📋"
          color="success"
        />
        <StatsCard
          title="Total Customers"
          value={loading ? "..." : formatNumber(stats.totalCustomers)}
          change={loading ? "..." : `+${stats.monthlyGrowth.customers}%`}
          changeType="positive"
          icon="👥"
          color="info"
        />
        <StatsCard
          title="Revenue"
          value={loading ? "..." : formatCurrency(stats.revenue)}
          change={loading ? "..." : `+${stats.monthlyGrowth.revenue}%`}
          changeType="positive"
          icon="💰"
          color="warning"
        />
      </div>

      {/* Additional Stats Row */}
      <div className="row mb-4">
        <div className="col-md-6">
          <div className="card border-left-danger">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-danger text-uppercase mb-1">
                    Low Stock Items
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">
                    {loading ? "..." : stats.lowStockItems}
                  </div>
                </div>
                <div className="col-auto">
                  <i className="fas fa-exclamation-triangle fa-2x text-danger"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-6">
          <div className="card border-left-warning">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-warning text-uppercase mb-1">
                    Pending Orders
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">
                    {loading ? "..." : stats.pendingOrders}
                  </div>
                </div>
                <div className="col-auto">
                  <i className="fas fa-clock fa-2x text-warning"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Content Row */}
      <div className="row">
        {/* Sales Chart */}
        <div className="col-xl-8 col-lg-7 mb-4">
          <SalesChart />
        </div>

        {/* Low Stock Alert */}
        <div className="col-xl-4 col-lg-5 mb-4">
          <LowStockAlert />
        </div>
      </div>

      {/* Recent Orders */}
      <div className="row">
        <div className="col-12">
          <RecentOrders />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
