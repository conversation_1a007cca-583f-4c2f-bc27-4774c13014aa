import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface Invoice {
  id: string;
  clientId: string;
  clientName: string;
  invoiceNumber: string;
  amount: number;
  currency: string;
  status: 'paid' | 'pending' | 'overdue' | 'cancelled';
  issueDate: string;
  dueDate: string;
  paidDate?: string;
  plan: string;
  billingPeriod: string;
  paymentMethod?: string;
}

interface BillingStats {
  totalRevenue: number;
  pendingAmount: number;
  overdueAmount: number;
  paidInvoices: number;
  pendingInvoices: number;
  overdueInvoices: number;
  averageInvoiceValue: number;
  collectionRate: number;
}

interface PaymentMethod {
  id: string;
  clientId: string;
  clientName: string;
  type: 'credit_card' | 'bank_transfer' | 'paypal' | 'stripe';
  last4?: string;
  expiryDate?: string;
  isDefault: boolean;
  status: 'active' | 'expired' | 'failed';
}

const Billing: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [billingStats, setBillingStats] = useState<BillingStats | null>(null);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');
  const [dateRange, setDateRange] = useState('30');
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  useEffect(() => {
    loadBillingData();
  }, [dateRange, filterStatus]);

  const loadBillingData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockInvoices: Invoice[] = [
        {
          id: '1',
          clientId: '1',
          clientName: 'TechCorp Solutions',
          invoiceNumber: 'INV-2024-001',
          amount: 790,
          currency: 'USD',
          status: 'paid',
          issueDate: '2024-12-01',
          dueDate: '2024-12-15',
          paidDate: '2024-12-10',
          plan: 'Professional',
          billingPeriod: 'Annual',
          paymentMethod: 'Credit Card'
        },
        {
          id: '2',
          clientId: '2',
          clientName: 'StartupXYZ',
          invoiceNumber: 'INV-2024-002',
          amount: 29,
          currency: 'USD',
          status: 'pending',
          issueDate: '2024-12-05',
          dueDate: '2024-12-20',
          plan: 'Starter',
          billingPeriod: 'Monthly'
        },
        {
          id: '3',
          clientId: '3',
          clientName: 'Enterprise Corp',
          invoiceNumber: 'INV-2024-003',
          amount: 1990,
          currency: 'USD',
          status: 'paid',
          issueDate: '2024-11-15',
          dueDate: '2024-11-30',
          paidDate: '2024-11-28',
          plan: 'Enterprise',
          billingPeriod: 'Annual',
          paymentMethod: 'Bank Transfer'
        },
        {
          id: '4',
          clientId: '4',
          clientName: 'Global Industries',
          invoiceNumber: 'INV-2024-004',
          amount: 199,
          currency: 'USD',
          status: 'overdue',
          issueDate: '2024-11-01',
          dueDate: '2024-11-15',
          plan: 'Enterprise',
          billingPeriod: 'Monthly'
        },
        {
          id: '5',
          clientId: '5',
          clientName: 'Innovation Labs',
          invoiceNumber: 'INV-2024-005',
          amount: 2500,
          currency: 'USD',
          status: 'paid',
          issueDate: '2024-12-01',
          dueDate: '2024-12-15',
          paidDate: '2024-12-12',
          plan: 'Custom',
          billingPeriod: 'Annual',
          paymentMethod: 'Stripe'
        }
      ];

      const mockStats: BillingStats = {
        totalRevenue: 45280,
        pendingAmount: 1250,
        overdueAmount: 850,
        paidInvoices: 142,
        pendingInvoices: 8,
        overdueInvoices: 3,
        averageInvoiceValue: 298,
        collectionRate: 94.2
      };

      const mockPaymentMethods: PaymentMethod[] = [
        {
          id: '1',
          clientId: '1',
          clientName: 'TechCorp Solutions',
          type: 'credit_card',
          last4: '4242',
          expiryDate: '12/25',
          isDefault: true,
          status: 'active'
        },
        {
          id: '2',
          clientId: '2',
          clientName: 'StartupXYZ',
          type: 'paypal',
          isDefault: true,
          status: 'active'
        },
        {
          id: '3',
          clientId: '3',
          clientName: 'Enterprise Corp',
          type: 'bank_transfer',
          isDefault: true,
          status: 'active'
        }
      ];

      setInvoices(mockInvoices);
      setBillingStats(mockStats);
      setPaymentMethods(mockPaymentMethods);
    } catch (error) {
      toast.error('Failed to load billing data');
    } finally {
      setLoading(false);
    }
  };

  const filteredInvoices = invoices.filter(invoice => {
    if (filterStatus === 'all') return true;
    return invoice.status === filterStatus;
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const badges = {
      paid: 'bg-success',
      pending: 'bg-warning',
      overdue: 'bg-danger',
      cancelled: 'bg-secondary'
    };
    return badges[status as keyof typeof badges] || 'bg-secondary';
  };

  const getPaymentMethodIcon = (type: string) => {
    const icons = {
      credit_card: 'fas fa-credit-card',
      bank_transfer: 'fas fa-university',
      paypal: 'fab fa-paypal',
      stripe: 'fab fa-stripe'
    };
    return icons[type as keyof typeof icons] || 'fas fa-payment';
  };

  const handleMarkAsPaid = (invoiceId: string) => {
    setInvoices(prev => prev.map(invoice => 
      invoice.id === invoiceId 
        ? { ...invoice, status: 'paid' as const, paidDate: new Date().toISOString().split('T')[0] }
        : invoice
    ));
    toast.success('Invoice marked as paid');
  };

  const handleSendReminder = (invoiceId: string) => {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (invoice) {
      toast.success(`Payment reminder sent to ${invoice.clientName}`);
    }
  };

  const generateInvoice = () => {
    toast.success('Invoice generation feature coming soon!');
  };

  const exportBillingReport = () => {
    const csvContent = [
      ['Invoice', 'Client', 'Amount', 'Status', 'Issue Date', 'Due Date', 'Plan'].join(','),
      ...filteredInvoices.map(invoice => [
        invoice.invoiceNumber,
        invoice.clientName,
        invoice.amount,
        invoice.status,
        invoice.issueDate,
        invoice.dueDate,
        invoice.plan
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `billing-report-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    toast.success('Billing report exported successfully!');
  };

  const toggleDropdown = (invoiceId: string) => {
    setOpenDropdown(openDropdown === invoiceId ? null : invoiceId);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setOpenDropdown(null);
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  if (loading) {
    return (
      <div className="container-fluid p-4">
        <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
          <div className="text-center">
            <div className="spinner-border text-primary mb-3" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <h5>Loading Billing Data...</h5>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Billing Dashboard</h1>
          <p className="text-muted mb-0">Manage invoices, payments, and billing operations</p>
        </div>
        <div className="d-flex gap-2">
          <button 
            className="btn btn-outline-primary"
            onClick={generateInvoice}
          >
            📄 Generate Invoice
          </button>
          <button 
            className="btn btn-outline-success"
            onClick={exportBillingReport}
          >
            📊 Export Report
          </button>
          <button className="btn btn-primary">
            💳 Payment Settings
          </button>
        </div>
      </div>

      {/* Billing Stats */}
      {billingStats && (
        <div className="row mb-4">
          <div className="col-md-3 mb-3">
            <div className="card bg-success text-white h-100">
              <div className="card-body">
                <div className="d-flex justify-content-between">
                  <div>
                    <h4 className="mb-0">{formatCurrency(billingStats.totalRevenue)}</h4>
                    <small>Total Revenue</small>
                    <div className="mt-1">
                      <small className="opacity-75">
                        {billingStats.paidInvoices} paid invoices
                      </small>
                    </div>
                  </div>
                  <div className="align-self-center">
                    <i className="fas fa-dollar-sign fa-2x opacity-75"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-3 mb-3">
            <div className="card bg-warning text-white h-100">
              <div className="card-body">
                <div className="d-flex justify-content-between">
                  <div>
                    <h4 className="mb-0">{formatCurrency(billingStats.pendingAmount)}</h4>
                    <small>Pending Amount</small>
                    <div className="mt-1">
                      <small className="opacity-75">
                        {billingStats.pendingInvoices} pending invoices
                      </small>
                    </div>
                  </div>
                  <div className="align-self-center">
                    <i className="fas fa-clock fa-2x opacity-75"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-3 mb-3">
            <div className="card bg-danger text-white h-100">
              <div className="card-body">
                <div className="d-flex justify-content-between">
                  <div>
                    <h4 className="mb-0">{formatCurrency(billingStats.overdueAmount)}</h4>
                    <small>Overdue Amount</small>
                    <div className="mt-1">
                      <small className="opacity-75">
                        {billingStats.overdueInvoices} overdue invoices
                      </small>
                    </div>
                  </div>
                  <div className="align-self-center">
                    <i className="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-3 mb-3">
            <div className="card bg-info text-white h-100">
              <div className="card-body">
                <div className="d-flex justify-content-between">
                  <div>
                    <h4 className="mb-0">{billingStats.collectionRate.toFixed(1)}%</h4>
                    <small>Collection Rate</small>
                    <div className="mt-1">
                      <small className="opacity-75">
                        Avg: {formatCurrency(billingStats.averageInvoiceValue)}
                      </small>
                    </div>
                  </div>
                  <div className="align-self-center">
                    <i className="fas fa-chart-pie fa-2x opacity-75"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <ul className="nav nav-tabs mb-4">
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            📊 Overview
          </button>
        </li>
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'invoices' ? 'active' : ''}`}
            onClick={() => setActiveTab('invoices')}
          >
            📄 Invoices ({filteredInvoices.length})
          </button>
        </li>
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'payments' ? 'active' : ''}`}
            onClick={() => setActiveTab('payments')}
          >
            💳 Payment Methods
          </button>
        </li>
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'reports' ? 'active' : ''}`}
            onClick={() => setActiveTab('reports')}
          >
            📈 Reports
          </button>
        </li>
      </ul>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="row">
          <div className="col-md-8">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">Recent Invoices</h5>
              </div>
              <div className="card-body">
                <div className="table-responsive">
                  <table className="table table-hover">
                    <thead className="table-light">
                      <tr>
                        <th>Invoice</th>
                        <th>Client</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Due Date</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {invoices.slice(0, 5).map((invoice) => (
                        <tr key={invoice.id}>
                          <td>
                            <div className="fw-bold">{invoice.invoiceNumber}</div>
                            <small className="text-muted">{invoice.plan}</small>
                          </td>
                          <td>{invoice.clientName}</td>
                          <td className="fw-bold">{formatCurrency(invoice.amount)}</td>
                          <td>
                            <span className={`badge ${getStatusBadge(invoice.status)}`}>
                              {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                            </span>
                          </td>
                          <td>{invoice.dueDate}</td>
                          <td>
                            <div className="btn-group btn-group-sm">
                              <button
                                className="btn btn-outline-primary"
                                onClick={() => {
                                  setSelectedInvoice(invoice);
                                  setShowInvoiceModal(true);
                                }}
                              >
                                👁️
                              </button>
                              {invoice.status === 'pending' && (
                                <button
                                  className="btn btn-outline-success"
                                  onClick={() => handleMarkAsPaid(invoice.id)}
                                >
                                  ✅
                                </button>
                              )}
                              {(invoice.status === 'pending' || invoice.status === 'overdue') && (
                                <button
                                  className="btn btn-outline-warning"
                                  onClick={() => handleSendReminder(invoice.id)}
                                >
                                  📧
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">Quick Actions</h5>
              </div>
              <div className="card-body">
                <div className="d-grid gap-2">
                  <button
                    className="btn btn-primary"
                    onClick={generateInvoice}
                  >
                    📄 Generate New Invoice
                  </button>
                  <button
                    className="btn btn-outline-warning"
                    onClick={() => {
                      const overdueCount = invoices.filter(inv => inv.status === 'overdue').length;
                      if (overdueCount > 0) {
                        toast.success(`Payment reminders sent to ${overdueCount} clients`);
                      } else {
                        toast.info('No overdue invoices found');
                      }
                    }}
                  >
                    📧 Send Overdue Reminders
                  </button>
                  <button
                    className="btn btn-outline-success"
                    onClick={exportBillingReport}
                  >
                    📊 Export Billing Report
                  </button>
                  <button
                    className="btn btn-outline-info"
                    onClick={() => toast.success('Bulk payment processing coming soon!')}
                  >
                    💳 Process Bulk Payments
                  </button>
                </div>
              </div>
            </div>

            <div className="card mt-3">
              <div className="card-header">
                <h6 className="mb-0">Payment Status Summary</h6>
              </div>
              <div className="card-body">
                <div className="row text-center">
                  <div className="col-6 mb-2">
                    <h5 className="text-success">{billingStats?.paidInvoices || 0}</h5>
                    <small className="text-muted">Paid</small>
                  </div>
                  <div className="col-6 mb-2">
                    <h5 className="text-warning">{billingStats?.pendingInvoices || 0}</h5>
                    <small className="text-muted">Pending</small>
                  </div>
                  <div className="col-6">
                    <h5 className="text-danger">{billingStats?.overdueInvoices || 0}</h5>
                    <small className="text-muted">Overdue</small>
                  </div>
                  <div className="col-6">
                    <h5 className="text-info">{billingStats?.collectionRate.toFixed(1) || 0}%</h5>
                    <small className="text-muted">Collection</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Invoices Tab */}
      {activeTab === 'invoices' && (
        <>
          {/* Filters */}
          <div className="card mb-4">
            <div className="card-body">
              <div className="row g-3">
                <div className="col-md-3">
                  <label className="form-label">Status Filter</label>
                  <select
                    className="form-select"
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <option value="all">All Status</option>
                    <option value="paid">Paid</option>
                    <option value="pending">Pending</option>
                    <option value="overdue">Overdue</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
                <div className="col-md-3">
                  <label className="form-label">Date Range</label>
                  <select
                    className="form-select"
                    value={dateRange}
                    onChange={(e) => setDateRange(e.target.value)}
                  >
                    <option value="7">Last 7 days</option>
                    <option value="30">Last 30 days</option>
                    <option value="90">Last 90 days</option>
                    <option value="365">Last year</option>
                  </select>
                </div>
                <div className="col-md-3 d-flex align-items-end">
                  <button
                    className="btn btn-outline-secondary w-100"
                    onClick={() => {
                      setFilterStatus('all');
                      setDateRange('30');
                    }}
                  >
                    Clear Filters
                  </button>
                </div>
                <div className="col-md-3 d-flex align-items-end">
                  <button
                    className="btn btn-primary w-100"
                    onClick={generateInvoice}
                  >
                    📄 New Invoice
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Invoices List */}
          <div className="card">
            <div className="card-header">
              <h5 className="mb-0">Invoices ({filteredInvoices.length})</h5>
            </div>
            <div className="card-body p-0">
              <div className="table-responsive">
                <table className="table table-hover mb-0">
                  <thead className="table-light">
                    <tr>
                      <th>Invoice Number</th>
                      <th>Client</th>
                      <th>Plan</th>
                      <th>Amount</th>
                      <th>Status</th>
                      <th>Issue Date</th>
                      <th>Due Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredInvoices.map((invoice) => (
                      <tr key={invoice.id}>
                        <td>
                          <div className="fw-bold">{invoice.invoiceNumber}</div>
                          <small className="text-muted">{invoice.billingPeriod}</small>
                        </td>
                        <td>{invoice.clientName}</td>
                        <td>
                          <span className="badge bg-light text-dark">{invoice.plan}</span>
                        </td>
                        <td className="fw-bold">{formatCurrency(invoice.amount)}</td>
                        <td>
                          <span className={`badge ${getStatusBadge(invoice.status)}`}>
                            {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                          </span>
                        </td>
                        <td>{invoice.issueDate}</td>
                        <td>
                          <div>{invoice.dueDate}</div>
                          {invoice.status === 'overdue' && (
                            <small className="text-danger">
                              {Math.ceil((new Date().getTime() - new Date(invoice.dueDate).getTime()) / (1000 * 60 * 60 * 24))} days overdue
                            </small>
                          )}
                        </td>
                        <td>
                          <div className="btn-group btn-group-sm">
                            <button
                              className="btn btn-outline-primary"
                              onClick={() => {
                                setSelectedInvoice(invoice);
                                setShowInvoiceModal(true);
                              }}
                            >
                              👁️
                            </button>
                            <div className="btn-group btn-group-sm position-relative">
                              <button
                                className="btn btn-outline-secondary"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleDropdown(invoice.id);
                                }}
                              >
                                ⚙️
                              </button>
                              {openDropdown === invoice.id && (
                                <div
                                  className="dropdown-menu show position-absolute"
                                  style={{
                                    top: '100%',
                                    left: '0',
                                    zIndex: 1000,
                                    minWidth: '160px'
                                  }}
                                >
                                  {invoice.status === 'pending' && (
                                    <button
                                      className="dropdown-item"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleMarkAsPaid(invoice.id);
                                        setOpenDropdown(null);
                                      }}
                                    >
                                      ✅ Mark as Paid
                                    </button>
                                  )}
                                  {(invoice.status === 'pending' || invoice.status === 'overdue') && (
                                    <button
                                      className="dropdown-item"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleSendReminder(invoice.id);
                                        setOpenDropdown(null);
                                      }}
                                    >
                                      📧 Send Reminder
                                    </button>
                                  )}
                                  <button
                                    className="dropdown-item"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toast.success('Download invoice feature coming soon!');
                                      setOpenDropdown(null);
                                    }}
                                  >
                                    📄 Download PDF
                                  </button>
                                  <hr className="dropdown-divider" />
                                  <button
                                    className="dropdown-item text-danger"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      toast.success('Cancel invoice feature coming soon!');
                                      setOpenDropdown(null);
                                    }}
                                  >
                                    ❌ Cancel Invoice
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Payment Methods Tab */}
      {activeTab === 'payments' && (
        <div className="row">
          <div className="col-md-8">
            <div className="card">
              <div className="card-header">
                <div className="d-flex justify-content-between align-items-center">
                  <h5 className="mb-0">Client Payment Methods</h5>
                  <button
                    className="btn btn-outline-primary btn-sm"
                    onClick={() => toast.success('Add payment method feature coming soon!')}
                  >
                    ➕ Add Payment Method
                  </button>
                </div>
              </div>
              <div className="card-body">
                <div className="table-responsive">
                  <table className="table table-hover">
                    <thead className="table-light">
                      <tr>
                        <th>Client</th>
                        <th>Payment Method</th>
                        <th>Details</th>
                        <th>Status</th>
                        <th>Default</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {paymentMethods.map((method) => (
                        <tr key={method.id}>
                          <td className="fw-bold">{method.clientName}</td>
                          <td>
                            <div className="d-flex align-items-center">
                              <i className={`${getPaymentMethodIcon(method.type)} me-2`}></i>
                              <span className="text-capitalize">{method.type.replace('_', ' ')}</span>
                            </div>
                          </td>
                          <td>
                            {method.type === 'credit_card' && method.last4 && (
                              <div>
                                <div>**** **** **** {method.last4}</div>
                                <small className="text-muted">Expires {method.expiryDate}</small>
                              </div>
                            )}
                            {method.type === 'paypal' && (
                              <span className="text-muted">PayPal Account</span>
                            )}
                            {method.type === 'bank_transfer' && (
                              <span className="text-muted">Bank Transfer</span>
                            )}
                            {method.type === 'stripe' && (
                              <span className="text-muted">Stripe Payment</span>
                            )}
                          </td>
                          <td>
                            <span className={`badge ${method.status === 'active' ? 'bg-success' : 'bg-danger'}`}>
                              {method.status.charAt(0).toUpperCase() + method.status.slice(1)}
                            </span>
                          </td>
                          <td>
                            {method.isDefault && (
                              <span className="badge bg-primary">Default</span>
                            )}
                          </td>
                          <td>
                            <div className="btn-group btn-group-sm">
                              <button
                                className="btn btn-outline-primary"
                                onClick={() => toast.success('Edit payment method feature coming soon!')}
                              >
                                ✏️
                              </button>
                              <button
                                className="btn btn-outline-danger"
                                onClick={() => toast.success('Delete payment method feature coming soon!')}
                              >
                                🗑️
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card">
              <div className="card-header">
                <h6 className="mb-0">Payment Method Statistics</h6>
              </div>
              <div className="card-body">
                <div className="mb-3">
                  <div className="d-flex justify-content-between align-items-center mb-1">
                    <span>Credit Cards</span>
                    <span className="fw-bold">
                      {paymentMethods.filter(m => m.type === 'credit_card').length}
                    </span>
                  </div>
                  <div className="progress" style={{ height: '6px' }}>
                    <div
                      className="progress-bar bg-primary"
                      style={{
                        width: `${(paymentMethods.filter(m => m.type === 'credit_card').length / paymentMethods.length) * 100}%`
                      }}
                    ></div>
                  </div>
                </div>
                <div className="mb-3">
                  <div className="d-flex justify-content-between align-items-center mb-1">
                    <span>PayPal</span>
                    <span className="fw-bold">
                      {paymentMethods.filter(m => m.type === 'paypal').length}
                    </span>
                  </div>
                  <div className="progress" style={{ height: '6px' }}>
                    <div
                      className="progress-bar bg-info"
                      style={{
                        width: `${(paymentMethods.filter(m => m.type === 'paypal').length / paymentMethods.length) * 100}%`
                      }}
                    ></div>
                  </div>
                </div>
                <div className="mb-3">
                  <div className="d-flex justify-content-between align-items-center mb-1">
                    <span>Bank Transfer</span>
                    <span className="fw-bold">
                      {paymentMethods.filter(m => m.type === 'bank_transfer').length}
                    </span>
                  </div>
                  <div className="progress" style={{ height: '6px' }}>
                    <div
                      className="progress-bar bg-success"
                      style={{
                        width: `${(paymentMethods.filter(m => m.type === 'bank_transfer').length / paymentMethods.length) * 100}%`
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <div className="card mt-3">
              <div className="card-header">
                <h6 className="mb-0">Payment Settings</h6>
              </div>
              <div className="card-body">
                <div className="d-grid gap-2">
                  <button
                    className="btn btn-outline-primary btn-sm"
                    onClick={() => toast.success('Payment gateway settings coming soon!')}
                  >
                    ⚙️ Gateway Settings
                  </button>
                  <button
                    className="btn btn-outline-success btn-sm"
                    onClick={() => toast.success('Auto-billing setup coming soon!')}
                  >
                    🔄 Auto-billing Setup
                  </button>
                  <button
                    className="btn btn-outline-warning btn-sm"
                    onClick={() => toast.success('Failed payment handling coming soon!')}
                  >
                    ⚠️ Failed Payment Rules
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reports Tab */}
      {activeTab === 'reports' && (
        <div className="row">
          <div className="col-md-8">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">Billing Reports</h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-md-6">
                    <div className="card border">
                      <div className="card-body text-center">
                        <i className="fas fa-chart-bar fa-3x text-primary mb-3"></i>
                        <h6>Revenue Report</h6>
                        <p className="text-muted small">Monthly and yearly revenue analysis</p>
                        <button
                          className="btn btn-outline-primary btn-sm"
                          onClick={() => toast.success('Revenue report generation coming soon!')}
                        >
                          📊 Generate
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="card border">
                      <div className="card-body text-center">
                        <i className="fas fa-file-invoice fa-3x text-success mb-3"></i>
                        <h6>Invoice Summary</h6>
                        <p className="text-muted small">Detailed invoice status and aging report</p>
                        <button
                          className="btn btn-outline-success btn-sm"
                          onClick={exportBillingReport}
                        >
                          📄 Export CSV
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="card border">
                      <div className="card-body text-center">
                        <i className="fas fa-credit-card fa-3x text-info mb-3"></i>
                        <h6>Payment Analysis</h6>
                        <p className="text-muted small">Payment method performance and trends</p>
                        <button
                          className="btn btn-outline-info btn-sm"
                          onClick={() => toast.success('Payment analysis coming soon!')}
                        >
                          💳 Analyze
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="card border">
                      <div className="card-body text-center">
                        <i className="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h6>Overdue Report</h6>
                        <p className="text-muted small">Clients with overdue payments and aging</p>
                        <button
                          className="btn btn-outline-warning btn-sm"
                          onClick={() => {
                            const overdueInvoices = invoices.filter(inv => inv.status === 'overdue');
                            if (overdueInvoices.length > 0) {
                              toast.success(`Found ${overdueInvoices.length} overdue invoices`);
                            } else {
                              toast.info('No overdue invoices found');
                            }
                          }}
                        >
                          ⚠️ Check Overdue
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card">
              <div className="card-header">
                <h6 className="mb-0">Quick Stats</h6>
              </div>
              <div className="card-body">
                <div className="row text-center">
                  <div className="col-6 mb-3">
                    <h4 className="text-success">{formatCurrency(billingStats?.totalRevenue || 0)}</h4>
                    <small className="text-muted">Total Revenue</small>
                  </div>
                  <div className="col-6 mb-3">
                    <h4 className="text-warning">{billingStats?.pendingInvoices || 0}</h4>
                    <small className="text-muted">Pending</small>
                  </div>
                  <div className="col-6 mb-3">
                    <h4 className="text-danger">{billingStats?.overdueInvoices || 0}</h4>
                    <small className="text-muted">Overdue</small>
                  </div>
                  <div className="col-6 mb-3">
                    <h4 className="text-info">{billingStats?.collectionRate.toFixed(1) || 0}%</h4>
                    <small className="text-muted">Collection</small>
                  </div>
                </div>
              </div>
            </div>

            <div className="card mt-3">
              <div className="card-header">
                <h6 className="mb-0">Export Options</h6>
              </div>
              <div className="card-body">
                <div className="d-grid gap-2">
                  <button
                    className="btn btn-outline-primary btn-sm"
                    onClick={exportBillingReport}
                  >
                    📊 Export All Data
                  </button>
                  <button
                    className="btn btn-outline-success btn-sm"
                    onClick={() => toast.success('Tax report export coming soon!')}
                  >
                    🧾 Tax Report
                  </button>
                  <button
                    className="btn btn-outline-info btn-sm"
                    onClick={() => toast.success('Financial summary coming soon!')}
                  >
                    💰 Financial Summary
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Invoice Details Modal */}
      {showInvoiceModal && selectedInvoice && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Invoice Details - {selectedInvoice.invoiceNumber}</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowInvoiceModal(false)}
                ></button>
              </div>
              <div className="modal-body">
                <div className="row g-3">
                  <div className="col-md-6">
                    <label className="form-label">Client</label>
                    <div className="form-control-plaintext">{selectedInvoice.clientName}</div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Plan</label>
                    <div className="form-control-plaintext">
                      <span className="badge bg-light text-dark">{selectedInvoice.plan}</span>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Amount</label>
                    <div className="form-control-plaintext fw-bold">{formatCurrency(selectedInvoice.amount)}</div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Status</label>
                    <div className="form-control-plaintext">
                      <span className={`badge ${getStatusBadge(selectedInvoice.status)}`}>
                        {selectedInvoice.status.charAt(0).toUpperCase() + selectedInvoice.status.slice(1)}
                      </span>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Issue Date</label>
                    <div className="form-control-plaintext">{selectedInvoice.issueDate}</div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Due Date</label>
                    <div className="form-control-plaintext">{selectedInvoice.dueDate}</div>
                  </div>
                  {selectedInvoice.paidDate && (
                    <div className="col-md-6">
                      <label className="form-label">Paid Date</label>
                      <div className="form-control-plaintext text-success">{selectedInvoice.paidDate}</div>
                    </div>
                  )}
                  <div className="col-md-6">
                    <label className="form-label">Billing Period</label>
                    <div className="form-control-plaintext">{selectedInvoice.billingPeriod}</div>
                  </div>
                  {selectedInvoice.paymentMethod && (
                    <div className="col-md-6">
                      <label className="form-label">Payment Method</label>
                      <div className="form-control-plaintext">{selectedInvoice.paymentMethod}</div>
                    </div>
                  )}
                </div>
              </div>
              <div className="modal-footer">
                <button
                  className="btn btn-secondary"
                  onClick={() => setShowInvoiceModal(false)}
                >
                  Close
                </button>
                <button
                  className="btn btn-outline-primary"
                  onClick={() => toast.success('Download PDF feature coming soon!')}
                >
                  📄 Download PDF
                </button>
                {selectedInvoice.status === 'pending' && (
                  <button
                    className="btn btn-success"
                    onClick={() => {
                      handleMarkAsPaid(selectedInvoice.id);
                      setShowInvoiceModal(false);
                    }}
                  >
                    ✅ Mark as Paid
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Billing;
