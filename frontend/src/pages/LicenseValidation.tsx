import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface LicenseInfo {
  companyName: string;
  domain: string;
  plan: string;
  status: string;
  expiresAt: string;
  daysUntilExpiry: number;
  features: string[];
  limits: {
    maxUsers: number;
    maxStorage: number;
  };
}

const LicenseValidation: React.FC = () => {
  const [licenseKey, setLicenseKey] = useState('');
  const [licenseInfo, setLicenseInfo] = useState<LicenseInfo | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<any>(null);
  const [selectedFeature, setSelectedFeature] = useState('');
  const [usageType, setUsageType] = useState<'users' | 'storage'>('users');

  const availableFeatures = [
    'inventory',
    'reports',
    'tally-sync',
    'api-access',
    'multi-location',
    'custom-fields',
    'advanced-reports',
    'basic-reports'
  ];

  useEffect(() => {
    // Load saved license key from localStorage
    const savedLicenseKey = localStorage.getItem('licenseKey');
    if (savedLicenseKey) {
      setLicenseKey(savedLicenseKey);
    }
  }, []);

  const validateLicense = async () => {
    if (!licenseKey.trim()) {
      toast.error('Please enter a license key');
      return;
    }

    setIsValidating(true);
    try {
      const response = await fetch('http://localhost:3000/api/clients/license/validate', {
        method: 'GET',
        headers: {
          'x-license-key': licenseKey,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (result.success) {
        setValidationResult(result);
        toast.success('License is valid!');
        
        // Save license key to localStorage
        localStorage.setItem('licenseKey', licenseKey);
        
        // Get detailed license info
        await getLicenseInfo();
      } else {
        setValidationResult(result);
        setLicenseInfo(null);
        toast.error(result.message || 'License validation failed');
      }
    } catch (error) {
      console.error('License validation error:', error);
      toast.error('Failed to validate license');
      setValidationResult(null);
      setLicenseInfo(null);
    } finally {
      setIsValidating(false);
    }
  };

  const getLicenseInfo = async () => {
    if (!licenseKey.trim()) return;

    try {
      const response = await fetch('http://localhost:3000/api/clients/license/info', {
        method: 'GET',
        headers: {
          'x-license-key': licenseKey,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (result.success) {
        setLicenseInfo(result.license);
      }
    } catch (error) {
      console.error('Get license info error:', error);
    }
  };

  const validateFeature = async () => {
    if (!selectedFeature) {
      toast.error('Please select a feature to validate');
      return;
    }

    try {
      const response = await fetch(`http://localhost:3000/api/clients/license/validate-feature/${selectedFeature}`, {
        method: 'GET',
        headers: {
          'x-license-key': licenseKey,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (result.success) {
        toast.success(`Feature '${selectedFeature}' is available!`);
      } else {
        toast.error(result.message || 'Feature not available');
      }
    } catch (error) {
      console.error('Feature validation error:', error);
      toast.error('Failed to validate feature');
    }
  };

  const checkUsageLimit = async () => {
    try {
      const response = await fetch(`http://localhost:3000/api/clients/license/check-usage/${usageType}`, {
        method: 'POST',
        headers: {
          'x-license-key': licenseKey,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (result.success) {
        toast.success(`${usageType} limit check passed!`);
      } else {
        toast.error(result.message || 'Usage limit exceeded');
      }
    } catch (error) {
      console.error('Usage check error:', error);
      toast.error('Failed to check usage limit');
    }
  };

  const getStatusBadge = (status: string) => {
    const badges = {
      active: 'bg-success',
      trial: 'bg-info',
      suspended: 'bg-warning',
      expired: 'bg-danger'
    };
    return badges[status as keyof typeof badges] || 'bg-secondary';
  };

  const getPlanBadge = (plan: string) => {
    const badges = {
      starter: 'bg-primary',
      professional: 'bg-success',
      enterprise: 'bg-warning',
      custom: 'bg-dark'
    };
    return badges[plan as keyof typeof badges] || 'bg-secondary';
  };

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">License Validation</h1>
          <p className="text-muted mb-0">Validate your license key and check feature access</p>
        </div>
        <div className="d-flex gap-2">
          <button 
            className="btn btn-outline-primary"
            onClick={() => {
              setLicenseKey('');
              setLicenseInfo(null);
              setValidationResult(null);
              localStorage.removeItem('licenseKey');
            }}
          >
            🔄 Reset
          </button>
        </div>
      </div>

      {/* License Key Input */}
      <div className="card mb-4">
        <div className="card-header">
          <h5 className="mb-0">License Key Validation</h5>
        </div>
        <div className="card-body">
          <div className="row g-3">
            <div className="col-md-8">
              <label className="form-label">License Key</label>
              <input
                type="text"
                className="form-control"
                placeholder="Enter your license key (e.g., TC-PRO-2024-001)"
                value={licenseKey}
                onChange={(e) => setLicenseKey(e.target.value)}
              />
              <div className="form-text">
                Enter the license key provided by your administrator
              </div>
            </div>
            <div className="col-md-4 d-flex align-items-end">
              <button
                className="btn btn-primary w-100"
                onClick={validateLicense}
                disabled={isValidating || !licenseKey.trim()}
              >
                {isValidating ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    Validating...
                  </>
                ) : (
                  <>🔍 Validate License</>
                )}
              </button>
            </div>
          </div>

          {/* Validation Result */}
          {validationResult && (
            <div className={`alert ${validationResult.success ? 'alert-success' : 'alert-danger'} mt-3`}>
              <div className="d-flex align-items-center">
                <i className={`fas ${validationResult.success ? 'fa-check-circle' : 'fa-exclamation-triangle'} me-2`}></i>
                <div>
                  <strong>{validationResult.success ? 'Valid License' : 'Invalid License'}</strong>
                  <div className="small">{validationResult.message}</div>
                  {validationResult.error && (
                    <div className="small text-muted">Error Code: {validationResult.error}</div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* License Information */}
      {licenseInfo && (
        <div className="row mb-4">
          <div className="col-md-8">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">License Information</h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-md-6">
                    <label className="form-label">Company Name</label>
                    <div className="form-control-plaintext">{licenseInfo.companyName}</div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Licensed Domain</label>
                    <div className="form-control-plaintext">
                      <span className="badge bg-light text-dark">{licenseInfo.domain}</span>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Plan</label>
                    <div className="form-control-plaintext">
                      <span className={`badge ${getPlanBadge(licenseInfo.plan)}`}>
                        {licenseInfo.plan.charAt(0).toUpperCase() + licenseInfo.plan.slice(1)}
                      </span>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Status</label>
                    <div className="form-control-plaintext">
                      <span className={`badge ${getStatusBadge(licenseInfo.status)}`}>
                        {licenseInfo.status.charAt(0).toUpperCase() + licenseInfo.status.slice(1)}
                      </span>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Expires On</label>
                    <div className="form-control-plaintext">
                      {licenseInfo.expiresAt}
                      <div className={`small ${licenseInfo.daysUntilExpiry <= 30 ? 'text-warning' : 'text-muted'}`}>
                        {licenseInfo.daysUntilExpiry > 0 
                          ? `${licenseInfo.daysUntilExpiry} days remaining`
                          : `${Math.abs(licenseInfo.daysUntilExpiry)} days overdue`
                        }
                      </div>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Limits</label>
                    <div className="form-control-plaintext">
                      <div className="small">
                        <div>Users: {licenseInfo.limits.maxUsers === 999 ? '∞' : licenseInfo.limits.maxUsers}</div>
                        <div>Storage: {licenseInfo.limits.maxStorage === 1000 ? '∞' : `${licenseInfo.limits.maxStorage}GB`}</div>
                      </div>
                    </div>
                  </div>
                  <div className="col-12">
                    <label className="form-label">Available Features</label>
                    <div className="form-control-plaintext">
                      <div className="d-flex flex-wrap gap-1">
                        {licenseInfo.features.map((feature, index) => (
                          <span key={index} className="badge bg-success">
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card">
              <div className="card-header">
                <h6 className="mb-0">Feature Validation</h6>
              </div>
              <div className="card-body">
                <div className="mb-3">
                  <label className="form-label">Test Feature Access</label>
                  <select
                    className="form-select"
                    value={selectedFeature}
                    onChange={(e) => setSelectedFeature(e.target.value)}
                  >
                    <option value="">Select a feature</option>
                    {availableFeatures.map(feature => (
                      <option key={feature} value={feature}>{feature}</option>
                    ))}
                  </select>
                </div>
                <button
                  className="btn btn-outline-primary w-100 mb-3"
                  onClick={validateFeature}
                  disabled={!selectedFeature}
                >
                  🔍 Test Feature
                </button>

                <hr />

                <div className="mb-3">
                  <label className="form-label">Test Usage Limits</label>
                  <select
                    className="form-select"
                    value={usageType}
                    onChange={(e) => setUsageType(e.target.value as 'users' | 'storage')}
                  >
                    <option value="users">Users</option>
                    <option value="storage">Storage</option>
                  </select>
                </div>
                <button
                  className="btn btn-outline-warning w-100"
                  onClick={checkUsageLimit}
                >
                  📊 Check Limit
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Integration Guide */}
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">Integration Guide</h5>
        </div>
        <div className="card-body">
          <h6>How to integrate license validation in your application:</h6>
          
          <div className="mb-3">
            <h6 className="text-primary">1. Add License Key Header</h6>
            <pre className="bg-light p-3 rounded">
{`// Add this header to all API requests
headers: {
  'x-license-key': 'YOUR_LICENSE_KEY',
  'Content-Type': 'application/json'
}`}
            </pre>
          </div>

          <div className="mb-3">
            <h6 className="text-primary">2. Validate License</h6>
            <pre className="bg-light p-3 rounded">
{`// Validate license before using features
fetch('/api/clients/license/validate', {
  headers: { 'x-license-key': 'YOUR_LICENSE_KEY' }
})`}
            </pre>
          </div>

          <div className="mb-3">
            <h6 className="text-primary">3. Check Feature Access</h6>
            <pre className="bg-light p-3 rounded">
{`// Check if specific feature is available
fetch('/api/clients/license/validate-feature/tally-sync', {
  headers: { 'x-license-key': 'YOUR_LICENSE_KEY' }
})`}
            </pre>
          </div>

          <div className="alert alert-info">
            <h6>📋 Important Notes:</h6>
            <ul className="mb-0">
              <li>License validation is based on the requesting domain</li>
              <li>Subdomains are allowed by default</li>
              <li>License keys are case-sensitive</li>
              <li>Expired licenses will return 403 Forbidden</li>
              <li>Feature access depends on your subscription plan</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LicenseValidation;
