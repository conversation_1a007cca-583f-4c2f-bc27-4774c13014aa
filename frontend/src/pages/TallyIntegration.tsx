import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface TallyConfig {
  serverUrl: string;
  port: number;
  companyName: string;
  username: string;
  password: string;
  syncInterval: number;
  autoSync: boolean;
  syncModules: {
    ledgers: boolean;
    vouchers: boolean;
    stockItems: boolean;
    stockGroups: boolean;
    units: boolean;
    godowns: boolean;
    costCentres: boolean;
    budgets: boolean;
  };
}

interface SyncStatus {
  isConnected: boolean;
  lastSync: string | null;
  nextSync: string | null;
  syncInProgress: boolean;
  totalRecords: number;
  syncedRecords: number;
  errors: string[];
}

interface SyncLog {
  id: string;
  timestamp: string;
  module: string;
  action: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  recordsProcessed: number;
}

const TallyIntegration: React.FC = () => {
  const [activeTab, setActiveTab] = useState('configuration');
  const [config, setConfig] = useState<TallyConfig>({
    serverUrl: 'localhost',
    port: 9000,
    companyName: '',
    username: '',
    password: '',
    syncInterval: 60, // minutes
    autoSync: true,
    syncModules: {
      ledgers: true,
      vouchers: true,
      stockItems: true,
      stockGroups: true,
      units: true,
      godowns: true,
      costCentres: true,
      budgets: false,
    }
  });

  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isConnected: false,
    lastSync: null,
    nextSync: null,
    syncInProgress: false,
    totalRecords: 0,
    syncedRecords: 0,
    errors: []
  });

  const [syncLogs, setSyncLogs] = useState<SyncLog[]>([]);
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  useEffect(() => {
    // Load saved configuration
    loadConfiguration();
    // Load sync status
    loadSyncStatus();
    // Load sync logs
    loadSyncLogs();
  }, []);

  const loadConfiguration = () => {
    const savedConfig = localStorage.getItem('tallyConfig');
    if (savedConfig) {
      setConfig(JSON.parse(savedConfig));
    }
  };

  const saveConfiguration = () => {
    localStorage.setItem('tallyConfig', JSON.stringify(config));
    toast.success('Configuration saved successfully!');
  };

  const loadSyncStatus = () => {
    // Simulate loading sync status
    setSyncStatus({
      isConnected: true,
      lastSync: '2024-12-06 08:30:00',
      nextSync: '2024-12-06 09:30:00',
      syncInProgress: false,
      totalRecords: 1250,
      syncedRecords: 1250,
      errors: []
    });
  };

  const loadSyncLogs = () => {
    // Simulate loading sync logs
    const mockLogs: SyncLog[] = [
      {
        id: '1',
        timestamp: '2024-12-06 08:30:00',
        module: 'Ledgers',
        action: 'Export',
        status: 'success',
        message: 'Successfully exported 45 ledger accounts to Tally',
        recordsProcessed: 45
      },
      {
        id: '2',
        timestamp: '2024-12-06 08:25:00',
        module: 'Vouchers',
        action: 'Export',
        status: 'success',
        message: 'Successfully exported 128 vouchers to Tally',
        recordsProcessed: 128
      },
      {
        id: '3',
        timestamp: '2024-12-06 08:20:00',
        module: 'Stock Items',
        action: 'Export',
        status: 'warning',
        message: 'Exported 89 stock items with 2 warnings',
        recordsProcessed: 89
      },
      {
        id: '4',
        timestamp: '2024-12-06 08:15:00',
        module: 'Connection',
        action: 'Test',
        status: 'success',
        message: 'Successfully connected to Tally server',
        recordsProcessed: 0
      }
    ];
    setSyncLogs(mockLogs);
  };

  const testConnection = async () => {
    setIsTestingConnection(true);
    try {
      // Real API call to test Tally connection
      const response = await fetch('http://localhost:3000/api/tally/test-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config)
      });

      const result = await response.json();

      if (result.success) {
        setSyncStatus(prev => ({ ...prev, isConnected: true }));
        toast.success(result.message);

        // Add log entry
        const newLog: SyncLog = {
          id: Date.now().toString(),
          timestamp: new Date().toLocaleString(),
          module: 'Connection',
          action: 'Test',
          status: 'success',
          message: `Connected to Tally server at ${config.serverUrl}:${config.port}`,
          recordsProcessed: 0
        };
        setSyncLogs(prev => [newLog, ...prev]);
      } else {
        throw new Error(result.message || 'Connection failed');
      }

    } catch (error) {
      setSyncStatus(prev => ({ ...prev, isConnected: false }));
      toast.error('Failed to connect to Tally server');
      console.error('Connection test error:', error);
    } finally {
      setIsTestingConnection(false);
    }
  };

  const startManualSync = async () => {
    setSyncStatus(prev => ({ ...prev, syncInProgress: true, syncedRecords: 0 }));
    
    try {
      // Simulate sync process
      const modules = Object.entries(config.syncModules)
        .filter(([_, enabled]) => enabled)
        .map(([module, _]) => module);
      
      let totalProcessed = 0;
      
      for (const module of modules) {
        // Simulate processing each module
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const recordsProcessed = Math.floor(Math.random() * 100) + 10;
        totalProcessed += recordsProcessed;
        
        setSyncStatus(prev => ({ 
          ...prev, 
          syncedRecords: totalProcessed,
          totalRecords: modules.length * 50
        }));
        
        // Add log entry
        const newLog: SyncLog = {
          id: Date.now().toString() + module,
          timestamp: new Date().toLocaleString(),
          module: module.charAt(0).toUpperCase() + module.slice(1),
          action: 'Export',
          status: 'success',
          message: `Successfully exported ${recordsProcessed} ${module} to Tally`,
          recordsProcessed
        };
        setSyncLogs(prev => [newLog, ...prev.slice(0, 19)]); // Keep only last 20 logs
      }
      
      setSyncStatus(prev => ({ 
        ...prev, 
        syncInProgress: false,
        lastSync: new Date().toLocaleString(),
        nextSync: new Date(Date.now() + config.syncInterval * 60000).toLocaleString()
      }));
      
      toast.success('Manual synchronization completed successfully!');
      
    } catch (error) {
      setSyncStatus(prev => ({ ...prev, syncInProgress: false }));
      toast.error('Synchronization failed');
    }
  };

  const handleConfigChange = (field: keyof TallyConfig, value: any) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  const handleSyncModuleChange = (module: keyof TallyConfig['syncModules']) => {
    setConfig(prev => ({
      ...prev,
      syncModules: {
        ...prev.syncModules,
        [module]: !prev.syncModules[module]
      }
    }));
  };

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Tally Integration</h1>
          <p className="text-muted mb-0">Synchronize financial data with Tally accounting software</p>
        </div>
        <div className="d-flex gap-2">
          <button 
            className="btn btn-outline-primary"
            onClick={testConnection}
            disabled={isTestingConnection}
          >
            {isTestingConnection ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Testing...
              </>
            ) : (
              <>🔗 Test Connection</>
            )}
          </button>
          <button 
            className="btn btn-primary"
            onClick={startManualSync}
            disabled={syncStatus.syncInProgress || !syncStatus.isConnected}
          >
            {syncStatus.syncInProgress ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Syncing...
              </>
            ) : (
              <>🔄 Manual Sync</>
            )}
          </button>
        </div>
      </div>

      {/* Connection Status */}
      <div className="alert alert-info mb-4">
        <div className="row align-items-center">
          <div className="col-md-3">
            <div className="d-flex align-items-center">
              <div className={`badge ${syncStatus.isConnected ? 'bg-success' : 'bg-danger'} me-2`}>
                {syncStatus.isConnected ? '🟢 Connected' : '🔴 Disconnected'}
              </div>
              <span className="small text-muted">
                {config.serverUrl}:{config.port}
              </span>
            </div>
          </div>
          <div className="col-md-3">
            <small className="text-muted d-block">Last Sync:</small>
            <span className="small">{syncStatus.lastSync || 'Never'}</span>
          </div>
          <div className="col-md-3">
            <small className="text-muted d-block">Next Sync:</small>
            <span className="small">{syncStatus.nextSync || 'Not scheduled'}</span>
          </div>
          <div className="col-md-3">
            {syncStatus.syncInProgress && (
              <div className="progress">
                <div 
                  className="progress-bar progress-bar-striped progress-bar-animated" 
                  role="progressbar" 
                  style={{ width: `${(syncStatus.syncedRecords / syncStatus.totalRecords) * 100}%` }}
                >
                  {syncStatus.syncedRecords}/{syncStatus.totalRecords}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <ul className="nav nav-tabs mb-4">
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'configuration' ? 'active' : ''}`}
            onClick={() => setActiveTab('configuration')}
          >
            ⚙️ Configuration
          </button>
        </li>
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'sync-modules' ? 'active' : ''}`}
            onClick={() => setActiveTab('sync-modules')}
          >
            📊 Sync Modules
          </button>
        </li>
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'sync-logs' ? 'active' : ''}`}
            onClick={() => setActiveTab('sync-logs')}
          >
            📋 Sync Logs
          </button>
        </li>
        <li className="nav-item">
          <button
            className={`nav-link ${activeTab === 'documentation' ? 'active' : ''}`}
            onClick={() => setActiveTab('documentation')}
          >
            📖 Documentation
          </button>
        </li>
      </ul>

      {/* Configuration Tab */}
      {activeTab === 'configuration' && (
        <div className="row">
          <div className="col-md-8">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">Tally Server Configuration</h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-md-6">
                    <label className="form-label">Server URL</label>
                    <input
                      type="text"
                      className="form-control"
                      value={config.serverUrl}
                      onChange={(e) => handleConfigChange('serverUrl', e.target.value)}
                      placeholder="localhost"
                    />
                    <div className="form-text">Tally server IP address or hostname</div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Port</label>
                    <input
                      type="number"
                      className="form-control"
                      value={config.port}
                      onChange={(e) => handleConfigChange('port', parseInt(e.target.value))}
                      placeholder="9000"
                    />
                    <div className="form-text">Tally server port (default: 9000)</div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Company Name</label>
                    <input
                      type="text"
                      className="form-control"
                      value={config.companyName}
                      onChange={(e) => handleConfigChange('companyName', e.target.value)}
                      placeholder="Your Company Name"
                    />
                    <div className="form-text">Tally company database name</div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Sync Interval (minutes)</label>
                    <select
                      className="form-select"
                      value={config.syncInterval}
                      onChange={(e) => handleConfigChange('syncInterval', parseInt(e.target.value))}
                    >
                      <option value={15}>15 minutes</option>
                      <option value={30}>30 minutes</option>
                      <option value={60}>1 hour</option>
                      <option value={120}>2 hours</option>
                      <option value={240}>4 hours</option>
                      <option value={480}>8 hours</option>
                      <option value={1440}>24 hours</option>
                    </select>
                    <div className="form-text">How often to sync data automatically</div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Username (Optional)</label>
                    <input
                      type="text"
                      className="form-control"
                      value={config.username}
                      onChange={(e) => handleConfigChange('username', e.target.value)}
                      placeholder="Tally username"
                    />
                    <div className="form-text">Leave blank if no authentication required</div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label">Password (Optional)</label>
                    <input
                      type="password"
                      className="form-control"
                      value={config.password}
                      onChange={(e) => handleConfigChange('password', e.target.value)}
                      placeholder="Tally password"
                    />
                    <div className="form-text">Leave blank if no authentication required</div>
                  </div>
                  <div className="col-12">
                    <div className="form-check">
                      <input
                        className="form-check-input"
                        type="checkbox"
                        checked={config.autoSync}
                        onChange={(e) => handleConfigChange('autoSync', e.target.checked)}
                        id="autoSync"
                      />
                      <label className="form-check-label" htmlFor="autoSync">
                        Enable automatic synchronization
                      </label>
                    </div>
                  </div>
                </div>
                <div className="mt-3">
                  <button className="btn btn-primary me-2" onClick={saveConfiguration}>
                    💾 Save Configuration
                  </button>
                  <button className="btn btn-outline-secondary" onClick={loadConfiguration}>
                    🔄 Reset to Saved
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card">
              <div className="card-header">
                <h6 className="mb-0">Connection Requirements</h6>
              </div>
              <div className="card-body">
                <ul className="list-unstyled small">
                  <li className="mb-2">
                    <i className="fas fa-check text-success me-2"></i>
                    Tally.ERP 9 or TallyPrime installed
                  </li>
                  <li className="mb-2">
                    <i className="fas fa-check text-success me-2"></i>
                    Tally server mode enabled
                  </li>
                  <li className="mb-2">
                    <i className="fas fa-check text-success me-2"></i>
                    Port 9000 accessible
                  </li>
                  <li className="mb-2">
                    <i className="fas fa-check text-success me-2"></i>
                    Company database loaded
                  </li>
                  <li className="mb-2">
                    <i className="fas fa-check text-success me-2"></i>
                    XML/JSON API enabled
                  </li>
                </ul>
              </div>
            </div>

            <div className="card mt-3">
              <div className="card-header">
                <h6 className="mb-0">Sync Statistics</h6>
              </div>
              <div className="card-body">
                <div className="row text-center">
                  <div className="col-6">
                    <h4 className="text-primary">1,250</h4>
                    <small className="text-muted">Total Records</small>
                  </div>
                  <div className="col-6">
                    <h4 className="text-success">1,250</h4>
                    <small className="text-muted">Synced</small>
                  </div>
                </div>
                <hr />
                <div className="row text-center">
                  <div className="col-6">
                    <h5 className="text-info">45</h5>
                    <small className="text-muted">Ledgers</small>
                  </div>
                  <div className="col-6">
                    <h5 className="text-warning">128</h5>
                    <small className="text-muted">Vouchers</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Sync Modules Tab */}
      {activeTab === 'sync-modules' && (
        <div className="row">
          <div className="col-md-8">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">Data Synchronization Modules</h5>
              </div>
              <div className="card-body">
                <p className="text-muted mb-4">
                  Select which data modules to synchronize with Tally. Each module will export relevant data from the inventory system to Tally in the appropriate format.
                </p>

                <div className="row g-3">
                  {Object.entries(config.syncModules).map(([module, enabled]) => (
                    <div key={module} className="col-md-6">
                      <div className={`card h-100 ${enabled ? 'border-primary' : 'border-light'}`}>
                        <div className="card-body">
                          <div className="d-flex justify-content-between align-items-start mb-2">
                            <h6 className="card-title mb-0">
                              {module.charAt(0).toUpperCase() + module.slice(1).replace(/([A-Z])/g, ' $1')}
                            </h6>
                            <div className="form-check form-switch">
                              <input
                                className="form-check-input"
                                type="checkbox"
                                checked={enabled}
                                onChange={() => handleSyncModuleChange(module as keyof TallyConfig['syncModules'])}
                                id={`sync-${module}`}
                              />
                            </div>
                          </div>
                          <p className="card-text small text-muted">
                            {getModuleDescription(module)}
                          </p>
                          <div className="d-flex justify-content-between align-items-center">
                            <span className={`badge ${enabled ? 'bg-success' : 'bg-secondary'}`}>
                              {enabled ? 'Enabled' : 'Disabled'}
                            </span>
                            <small className="text-muted">
                              {getModuleRecordCount(module)} records
                            </small>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card">
              <div className="card-header">
                <h6 className="mb-0">Sync Process Flow</h6>
              </div>
              <div className="card-body">
                <div className="timeline">
                  <div className="timeline-item">
                    <div className="timeline-marker bg-primary"></div>
                    <div className="timeline-content">
                      <h6 className="timeline-title">Data Collection</h6>
                      <p className="timeline-text small">Gather data from enabled modules</p>
                    </div>
                  </div>
                  <div className="timeline-item">
                    <div className="timeline-marker bg-info"></div>
                    <div className="timeline-content">
                      <h6 className="timeline-title">Format Conversion</h6>
                      <p className="timeline-text small">Convert to Tally XML/JSON format</p>
                    </div>
                  </div>
                  <div className="timeline-item">
                    <div className="timeline-marker bg-warning"></div>
                    <div className="timeline-content">
                      <h6 className="timeline-title">Data Validation</h6>
                      <p className="timeline-text small">Validate data integrity and format</p>
                    </div>
                  </div>
                  <div className="timeline-item">
                    <div className="timeline-marker bg-success"></div>
                    <div className="timeline-content">
                      <h6 className="timeline-title">Tally Import</h6>
                      <p className="timeline-text small">Send data to Tally server</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Sync Logs Tab */}
      {activeTab === 'sync-logs' && (
        <div className="card">
          <div className="card-header d-flex justify-content-between align-items-center">
            <h5 className="mb-0">Synchronization Logs</h5>
            <button className="btn btn-outline-secondary btn-sm" onClick={loadSyncLogs}>
              🔄 Refresh
            </button>
          </div>
          <div className="card-body">
            {syncLogs.length === 0 ? (
              <div className="text-center py-4">
                <i className="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                <h6 className="text-muted">No sync logs available</h6>
                <p className="text-muted">Sync logs will appear here after synchronization activities.</p>
              </div>
            ) : (
              <div className="table-responsive">
                <table className="table table-hover">
                  <thead>
                    <tr>
                      <th>Timestamp</th>
                      <th>Module</th>
                      <th>Action</th>
                      <th>Status</th>
                      <th>Records</th>
                      <th>Message</th>
                    </tr>
                  </thead>
                  <tbody>
                    {syncLogs.map((log) => (
                      <tr key={log.id}>
                        <td>
                          <small className="text-muted">{log.timestamp}</small>
                        </td>
                        <td>
                          <span className="badge bg-light text-dark">{log.module}</span>
                        </td>
                        <td>{log.action}</td>
                        <td>
                          <span className={`badge ${
                            log.status === 'success' ? 'bg-success' :
                            log.status === 'error' ? 'bg-danger' : 'bg-warning'
                          }`}>
                            {log.status === 'success' ? '✅' : log.status === 'error' ? '❌' : '⚠️'} {log.status}
                          </span>
                        </td>
                        <td>
                          {log.recordsProcessed > 0 && (
                            <span className="badge bg-info">{log.recordsProcessed}</span>
                          )}
                        </td>
                        <td>
                          <small>{log.message}</small>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Documentation Tab */}
      {activeTab === 'documentation' && (
        <div className="row">
          <div className="col-md-8">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">📖 Tally Integration Guide</h5>
              </div>
              <div className="card-body">
                <h6>Overview</h6>
                <p>
                  The Tally integration system automatically synchronizes financial data from your inventory management system
                  to Tally accounting software. This ensures that your accounting records are always up-to-date with your
                  business operations.
                </p>

                <h6 className="mt-4">Setup Requirements</h6>
                <ol>
                  <li><strong>Tally Installation:</strong> Ensure Tally.ERP 9 or TallyPrime is installed and running</li>
                  <li><strong>Server Mode:</strong> Enable Tally server mode to accept external connections</li>
                  <li><strong>Port Configuration:</strong> Configure Tally to listen on port 9000 (default)</li>
                  <li><strong>Company Database:</strong> Load your company database in Tally</li>
                  <li><strong>API Access:</strong> Enable XML/JSON API access in Tally configuration</li>
                </ol>

                <h6 className="mt-4">Data Synchronization</h6>
                <p>The following data types are synchronized:</p>
                <ul>
                  <li><strong>Ledgers:</strong> Customer and vendor accounts, expense accounts</li>
                  <li><strong>Vouchers:</strong> Sales, purchase, payment, receipt vouchers</li>
                  <li><strong>Stock Items:</strong> Product information, pricing, units</li>
                  <li><strong>Stock Groups:</strong> Product categories and classifications</li>
                  <li><strong>Units:</strong> Measurement units for products</li>
                  <li><strong>Godowns:</strong> Warehouse and location information</li>
                  <li><strong>Cost Centres:</strong> Department and project tracking</li>
                </ul>

                <h6 className="mt-4">Troubleshooting</h6>
                <div className="alert alert-info">
                  <h6>Common Issues:</h6>
                  <ul className="mb-0">
                    <li><strong>Connection Failed:</strong> Verify Tally is running in server mode on correct port</li>
                    <li><strong>Data Sync Errors:</strong> Check data format and ensure ledger accounts exist</li>
                    <li><strong>Authentication Issues:</strong> Verify username/password if authentication is enabled</li>
                    <li><strong>Timeout Errors:</strong> Check network connectivity and Tally server responsiveness</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card">
              <div className="card-header">
                <h6 className="mb-0">🔗 Quick Links</h6>
              </div>
              <div className="card-body">
                <div className="list-group list-group-flush">
                  <a href="https://help.tallysolutions.com/developer-reference/" target="_blank" className="list-group-item list-group-item-action">
                    📚 Tally Developer Reference
                  </a>
                  <a href="https://help.tallysolutions.com/developer-reference/integration-capabilities/" target="_blank" className="list-group-item list-group-item-action">
                    🔌 Integration Capabilities
                  </a>
                  <a href="#" className="list-group-item list-group-item-action">
                    🎥 Video Tutorials
                  </a>
                  <a href="#" className="list-group-item list-group-item-action">
                    💬 Support Forum
                  </a>
                </div>
              </div>
            </div>

            <div className="card mt-3">
              <div className="card-header">
                <h6 className="mb-0">⚡ Quick Actions</h6>
              </div>
              <div className="card-body">
                <div className="d-grid gap-2">
                  <button className="btn btn-outline-primary btn-sm" onClick={testConnection}>
                    🔗 Test Connection
                  </button>
                  <button className="btn btn-outline-success btn-sm" onClick={startManualSync}>
                    🔄 Manual Sync
                  </button>
                  <button className="btn btn-outline-info btn-sm" onClick={loadSyncLogs}>
                    📋 View Logs
                  </button>
                  <button className="btn btn-outline-secondary btn-sm" onClick={saveConfiguration}>
                    💾 Save Config
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // Helper functions
  function getModuleDescription(module: string): string {
    const descriptions: Record<string, string> = {
      ledgers: 'Customer and vendor accounts, expense and income ledgers',
      vouchers: 'Sales, purchase, payment, receipt, and journal vouchers',
      stockItems: 'Product information, pricing, and inventory details',
      stockGroups: 'Product categories and group classifications',
      units: 'Measurement units for products and services',
      godowns: 'Warehouse locations and storage information',
      costCentres: 'Department and project cost tracking',
      budgets: 'Budget allocations and financial planning data'
    };
    return descriptions[module] || 'Data synchronization module';
  }

  function getModuleRecordCount(module: string): number {
    const counts: Record<string, number> = {
      ledgers: 45,
      vouchers: 128,
      stockItems: 89,
      stockGroups: 12,
      units: 8,
      godowns: 3,
      costCentres: 5,
      budgets: 0
    };
    return counts[module] || 0;
  }
};

export default TallyIntegration;
