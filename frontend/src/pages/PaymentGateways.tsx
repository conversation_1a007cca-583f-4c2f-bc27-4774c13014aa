import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { PaymentGateway, PaymentGatewayFormData, PaymentTestResponse } from '../types/payment';
import { paymentGatewayTemplates, getPaymentGatewayTemplate } from '../data/paymentGatewayTemplates';

const PaymentGateways: React.FC = () => {
  const [gateways, setGateways] = useState<PaymentGateway[]>([]);
  const [showAddGateway, setShowAddGateway] = useState(false);
  const [editingGateway, setEditingGateway] = useState<PaymentGateway | null>(null);
  const [testingGateway, setTestingGateway] = useState<string | null>(null);
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway | null>(null);

  // Load sample data
  useEffect(() => {
    const sampleGateways: PaymentGateway[] = [
      {
        id: '1',
        name: 'Stripe Production',
        type: 'stripe',
        enabled: true,
        isDefault: true,
        configuration: {
          publishableKey: 'pk_live_...',
          secretKey: 'sk_live_...',
          environment: 'production',
          currency: 'USD',
          minimumAmount: 50,
          maximumAmount: 100000
        },
        supportedCurrencies: ['USD', 'EUR', 'GBP'],
        supportedCountries: ['US', 'CA', 'GB', 'AU'],
        features: [
          { name: 'Credit Cards', enabled: true },
          { name: 'Digital Wallets', enabled: true },
          { name: 'Recurring Payments', enabled: true }
        ],
        status: 'active',
        lastTested: '2024-01-15T10:30:00Z',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-15T10:30:00Z'
      },
      {
        id: '2',
        name: 'PayPal Business',
        type: 'paypal',
        enabled: true,
        isDefault: false,
        configuration: {
          clientId: 'AeA1QIZXiflr8_...',
          clientSecret: 'EGnHDxD_qRPdaLdHgGlliB...',
          environment: 'production',
          currency: 'USD'
        },
        supportedCurrencies: ['USD', 'EUR', 'GBP'],
        supportedCountries: ['US', 'CA', 'GB', 'AU'],
        features: [
          { name: 'PayPal Payments', enabled: true },
          { name: 'Express Checkout', enabled: true }
        ],
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-10T00:00:00Z'
      }
    ];
    setGateways(sampleGateways);
  }, []);

  const handleAddGateway = () => {
    setEditingGateway(null);
    setShowAddGateway(true);
  };

  const handleEditGateway = (gateway: PaymentGateway) => {
    setEditingGateway(gateway);
    setShowAddGateway(true);
  };

  const handleDeleteGateway = (gatewayId: string) => {
    if (window.confirm('Are you sure you want to delete this payment gateway?')) {
      setGateways(prev => prev.filter(g => g.id !== gatewayId));
      toast.success('Payment gateway deleted successfully');
    }
  };

  const handleToggleGateway = (gatewayId: string) => {
    setGateways(prev => prev.map(gateway => 
      gateway.id === gatewayId 
        ? { ...gateway, enabled: !gateway.enabled }
        : gateway
    ));
    toast.success('Gateway status updated');
  };

  const handleSetDefault = (gatewayId: string) => {
    setGateways(prev => prev.map(gateway => ({
      ...gateway,
      isDefault: gateway.id === gatewayId
    })));
    toast.success('Default gateway updated');
  };

  const handleTestGateway = async (gatewayId: string) => {
    setTestingGateway(gatewayId);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const testResult: PaymentTestResponse = {
      success: true,
      gatewayId,
      testResults: {
        connection: true,
        authentication: true,
        webhook: true,
        testTransaction: true
      },
      message: 'All tests passed successfully'
    };

    setTestingGateway(null);
    
    if (testResult.success) {
      setGateways(prev => prev.map(gateway => 
        gateway.id === gatewayId 
          ? { ...gateway, status: 'active', lastTested: new Date().toISOString() }
          : gateway
      ));
      toast.success('Gateway test completed successfully');
    } else {
      toast.error('Gateway test failed');
    }
  };

  const getStatusBadge = (status: string) => {
    const badges = {
      active: 'bg-success',
      inactive: 'bg-secondary',
      testing: 'bg-warning',
      error: 'bg-danger'
    };
    return badges[status as keyof typeof badges] || 'bg-secondary';
  };

  return (
    <div className="container-fluid p-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-1">Payment Gateways</h1>
          <p className="text-muted mb-0">Configure and manage payment processing systems</p>
        </div>
        <div className="d-flex gap-2">
          <button
            className="btn btn-outline-primary"
            onClick={() => toast.success('Payment settings coming soon!')}
          >
            ⚙️ Global Settings
          </button>
          <button
            className="btn btn-primary"
            onClick={handleAddGateway}
          >
            ➕ Add Gateway
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary bg-opacity-10 p-3 rounded">
                    <span className="text-primary fs-4">💳</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Total Gateways</h6>
                  <h4 className="mb-0">{gateways.length}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-success bg-opacity-10 p-3 rounded">
                    <span className="text-success fs-4">✅</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Active Gateways</h6>
                  <h4 className="mb-0">{gateways.filter(g => g.enabled).length}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-warning bg-opacity-10 p-3 rounded">
                    <span className="text-warning fs-4">⭐</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Default Gateway</h6>
                  <h4 className="mb-0">{gateways.find(g => g.isDefault)?.name || 'None'}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-info bg-opacity-10 p-3 rounded">
                    <span className="text-info fs-4">🌍</span>
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <h6 className="mb-0">Supported Currencies</h6>
                  <h4 className="mb-0">{new Set(gateways.flatMap(g => g.supportedCurrencies)).size}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Gateways List */}
      <div className="card">
        <div className="card-header">
          <h5 className="mb-0">Configured Payment Gateways</h5>
        </div>
        <div className="card-body p-0">
          {gateways.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-3">
                <span className="display-1">💳</span>
              </div>
              <h5>No Payment Gateways Configured</h5>
              <p className="text-muted">Add your first payment gateway to start accepting payments</p>
              <button className="btn btn-primary" onClick={handleAddGateway}>
                Add Payment Gateway
              </button>
            </div>
          ) : (
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead className="table-light">
                  <tr>
                    <th>Gateway</th>
                    <th>Type</th>
                    <th>Status</th>
                    <th>Environment</th>
                    <th>Default</th>
                    <th>Last Tested</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {gateways.map((gateway) => (
                    <tr key={gateway.id}>
                      <td>
                        <div className="d-flex align-items-center">
                          <span className="me-2 fs-5">
                            {paymentGatewayTemplates.find(t => t.type === gateway.type)?.icon}
                          </span>
                          <div>
                            <div className="fw-medium">{gateway.name}</div>
                            <small className="text-muted">
                              {gateway.supportedCurrencies.join(', ')}
                            </small>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span className="badge bg-light text-dark">
                          {paymentGatewayTemplates.find(t => t.type === gateway.type)?.name}
                        </span>
                      </td>
                      <td>
                        <span className={`badge ${getStatusBadge(gateway.status)}`}>
                          {gateway.enabled ? gateway.status : 'disabled'}
                        </span>
                      </td>
                      <td>
                        <span className={`badge ${gateway.configuration.environment === 'production' ? 'bg-success' : 'bg-warning'}`}>
                          {gateway.configuration.environment}
                        </span>
                      </td>
                      <td>
                        {gateway.isDefault && (
                          <span className="badge bg-primary">Default</span>
                        )}
                      </td>
                      <td>
                        {gateway.lastTested ? (
                          <small className="text-muted">
                            {new Date(gateway.lastTested).toLocaleDateString()}
                          </small>
                        ) : (
                          <small className="text-muted">Never</small>
                        )}
                      </td>
                      <td>
                        <div className="btn-group btn-group-sm">
                          <button
                            className="btn btn-outline-primary"
                            onClick={() => handleTestGateway(gateway.id)}
                            disabled={testingGateway === gateway.id}
                          >
                            {testingGateway === gateway.id ? (
                              <span className="spinner-border spinner-border-sm me-1"></span>
                            ) : (
                              '🧪'
                            )}
                            Test
                          </button>
                          <button
                            className="btn btn-outline-secondary"
                            onClick={() => handleEditGateway(gateway)}
                          >
                            ✏️ Edit
                          </button>
                          <button
                            className={`btn ${gateway.enabled ? 'btn-outline-warning' : 'btn-outline-success'}`}
                            onClick={() => handleToggleGateway(gateway.id)}
                          >
                            {gateway.enabled ? '⏸️ Disable' : '▶️ Enable'}
                          </button>
                          {!gateway.isDefault && (
                            <button
                              className="btn btn-outline-primary"
                              onClick={() => handleSetDefault(gateway.id)}
                            >
                              ⭐ Set Default
                            </button>
                          )}
                          <button
                            className="btn btn-outline-danger"
                            onClick={() => handleDeleteGateway(gateway.id)}
                          >
                            🗑️ Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Add/Edit Gateway Modal */}
      {showAddGateway && (
        <PaymentGatewayModal
          gateway={editingGateway}
          onSave={(gatewayData) => {
            if (editingGateway) {
              // Update existing gateway
              setGateways(prev => prev.map(g => 
                g.id === editingGateway.id 
                  ? { ...g, ...gatewayData, updatedAt: new Date().toISOString() }
                  : g
              ));
              toast.success('Payment gateway updated successfully');
            } else {
              // Add new gateway
              const newGateway: PaymentGateway = {
                id: (gateways.length + 1).toString(),
                ...gatewayData,
                supportedCountries: ['US', 'CA', 'GB', 'AU'],
                status: 'inactive',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
              };
              setGateways(prev => [...prev, newGateway]);
              toast.success('Payment gateway added successfully');
            }
            setShowAddGateway(false);
          }}
          onCancel={() => setShowAddGateway(false)}
        />
      )}
    </div>
  );
};

// Payment Gateway Modal Component
const PaymentGatewayModal: React.FC<{
  gateway: PaymentGateway | null;
  onSave: (data: PaymentGatewayFormData) => void;
  onCancel: () => void;
}> = ({ gateway, onSave, onCancel }) => {
  const [formData, setFormData] = useState<PaymentGatewayFormData>({
    name: gateway?.name || '',
    type: gateway?.type || 'stripe',
    enabled: gateway?.enabled || false,
    isDefault: gateway?.isDefault || false,
    configuration: gateway?.configuration || { environment: 'sandbox' },
    supportedCurrencies: gateway?.supportedCurrencies || ['USD'],
    features: gateway?.features || []
  });

  const [selectedTemplate, setSelectedTemplate] = useState(
    gateway ? getPaymentGatewayTemplate(gateway.type) : paymentGatewayTemplates[0]
  );

  const handleTypeChange = (type: string) => {
    const template = getPaymentGatewayTemplate(type);
    if (template) {
      setSelectedTemplate(template);
      setFormData(prev => ({
        ...prev,
        type: type as any,
        configuration: { environment: 'sandbox' },
        features: template.supportedFeatures.map(feature => ({
          name: feature,
          enabled: true
        }))
      }));
    }
  };

  const handleConfigChange = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      configuration: {
        ...prev.configuration,
        [key]: value
      }
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-xl">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">
              {gateway ? 'Edit Payment Gateway' : 'Add Payment Gateway'}
            </h5>
            <button type="button" className="btn-close" onClick={onCancel}></button>
          </div>
          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              <div className="row">
                {/* Gateway Type Selection */}
                <div className="col-md-4">
                  <div className="card h-100">
                    <div className="card-header">
                      <h6 className="mb-0">Gateway Type</h6>
                    </div>
                    <div className="card-body">
                      {paymentGatewayTemplates.map((template) => (
                        <div key={template.type} className="mb-2">
                          <div
                            className={`border rounded p-3 cursor-pointer ${
                              formData.type === template.type ? 'border-primary bg-primary bg-opacity-10' : 'border-light'
                            }`}
                            onClick={() => handleTypeChange(template.type)}
                          >
                            <div className="d-flex align-items-center">
                              <span className="me-2 fs-4">{template.icon}</span>
                              <div>
                                <div className="fw-medium">{template.name}</div>
                                <small className="text-muted">{template.description}</small>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Configuration Form */}
                <div className="col-md-8">
                  <div className="card h-100">
                    <div className="card-header">
                      <h6 className="mb-0">Configuration</h6>
                    </div>
                    <div className="card-body">
                      {/* Basic Settings */}
                      <div className="mb-3">
                        <label className="form-label">Gateway Name</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.name}
                          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                          placeholder="e.g., Stripe Production"
                          required
                        />
                      </div>

                      <div className="row mb-3">
                        <div className="col-md-6">
                          <div className="form-check">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={formData.enabled}
                              onChange={(e) => setFormData(prev => ({ ...prev, enabled: e.target.checked }))}
                            />
                            <label className="form-check-label">Enable Gateway</label>
                          </div>
                        </div>
                        <div className="col-md-6">
                          <div className="form-check">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              checked={formData.isDefault}
                              onChange={(e) => setFormData(prev => ({ ...prev, isDefault: e.target.checked }))}
                            />
                            <label className="form-check-label">Set as Default</label>
                          </div>
                        </div>
                      </div>

                      {/* Dynamic Configuration Fields */}
                      {selectedTemplate && (
                        <>
                          <h6 className="mb-3">Required Configuration</h6>
                          {selectedTemplate.requiredFields.map((field) => (
                            <div key={field.key} className="mb-3">
                              <label className="form-label">
                                {field.label}
                                {field.required && <span className="text-danger">*</span>}
                              </label>
                              {field.type === 'select' ? (
                                <select
                                  className="form-select"
                                  value={formData.configuration[field.key] || ''}
                                  onChange={(e) => handleConfigChange(field.key, e.target.value)}
                                  required={field.required}
                                >
                                  <option value="">Select {field.label}</option>
                                  {field.options?.map((option) => (
                                    <option key={option.value} value={option.value}>
                                      {option.label}
                                    </option>
                                  ))}
                                </select>
                              ) : (
                                <input
                                  type={field.type}
                                  className="form-control"
                                  value={formData.configuration[field.key] || ''}
                                  onChange={(e) => handleConfigChange(field.key, e.target.value)}
                                  placeholder={field.placeholder}
                                  required={field.required}
                                />
                              )}
                              {field.description && (
                                <small className="form-text text-muted">{field.description}</small>
                              )}
                            </div>
                          ))}

                          {selectedTemplate.optionalFields.length > 0 && (
                            <>
                              <h6 className="mb-3 mt-4">Optional Configuration</h6>
                              {selectedTemplate.optionalFields.map((field) => (
                                <div key={field.key} className="mb-3">
                                  <label className="form-label">{field.label}</label>
                                  {field.type === 'select' ? (
                                    <select
                                      className="form-select"
                                      value={formData.configuration[field.key] || ''}
                                      onChange={(e) => handleConfigChange(field.key, e.target.value)}
                                    >
                                      <option value="">Select {field.label}</option>
                                      {field.options?.map((option) => (
                                        <option key={option.value} value={option.value}>
                                          {option.label}
                                        </option>
                                      ))}
                                    </select>
                                  ) : (
                                    <input
                                      type={field.type}
                                      className="form-control"
                                      value={formData.configuration[field.key] || ''}
                                      onChange={(e) => handleConfigChange(field.key, e.target.value)}
                                      placeholder={field.placeholder}
                                    />
                                  )}
                                  {field.description && (
                                    <small className="form-text text-muted">{field.description}</small>
                                  )}
                                </div>
                              ))}
                            </>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onCancel}>
                Cancel
              </button>
              <button type="submit" className="btn btn-primary">
                {gateway ? 'Update Gateway' : 'Add Gateway'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default PaymentGateways;
