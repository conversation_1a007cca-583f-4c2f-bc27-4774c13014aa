import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import CategoryManager from '../components/Categories/CategoryManager';
import CategoryForm from '../components/Categories/CategoryForm';
import AdvancedProductForm from '../components/Products/AdvancedProductForm';
import ProductTable from '../components/Products/ProductTable';
import { Product, ProductFormData, ProductFilters } from '../types/product';
import { productService } from '../services/productService';

interface Category {
  id: string;
  name: string;
  description: string;
  parentId: string | null;
  slug: string;
  image: string;
  status: 'active' | 'inactive';
  sortOrder: number;
  metaTitle: string;
  metaDescription: string;
  seoKeywords: string;
  customFields: { [key: string]: any };
  children?: Category[];
  productCount: number;
  createdAt: string;
  updatedAt: string;
}



const ProductCatalog: React.FC = () => {
  const [activeView, setActiveView] = useState<'products' | 'categories'>('products');
  const [categories, setCategories] = useState<Category[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [showCategoryForm, setShowCategoryForm] = useState(false);
  const [showProductForm, setShowProductForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<ProductFilters>({
    page: 1,
    limit: 10
  });
  const [totalProducts, setTotalProducts] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Sample data
  const sampleCategories: Category[] = [
    {
      id: 'cat_1',
      name: 'Electronics',
      description: 'Electronic devices and accessories',
      parentId: null,
      slug: 'electronics',
      image: 'https://via.placeholder.com/150x150?text=Electronics',
      status: 'active',
      sortOrder: 1,
      metaTitle: 'Electronics - Best Deals',
      metaDescription: 'Find the best electronic devices and accessories',
      seoKeywords: 'electronics, devices, gadgets',
      customFields: { featured: true, commission: 5 },
      productCount: 45,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      children: [
        {
          id: 'cat_2',
          name: 'Smartphones',
          description: 'Mobile phones and accessories',
          parentId: 'cat_1',
          slug: 'smartphones',
          image: 'https://via.placeholder.com/150x150?text=Smartphones',
          status: 'active',
          sortOrder: 1,
          metaTitle: 'Smartphones',
          metaDescription: 'Latest smartphones and mobile accessories',
          seoKeywords: 'smartphones, mobile, phones',
          customFields: { warranty: '2 years' },
          productCount: 25,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 'cat_3',
          name: 'Laptops',
          description: 'Laptops and computer accessories',
          parentId: 'cat_1',
          slug: 'laptops',
          image: 'https://via.placeholder.com/150x150?text=Laptops',
          status: 'active',
          sortOrder: 2,
          metaTitle: 'Laptops',
          metaDescription: 'High-performance laptops and accessories',
          seoKeywords: 'laptops, computers, notebooks',
          customFields: { warranty: '3 years' },
          productCount: 20,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      ]
    },
    {
      id: 'cat_4',
      name: 'Clothing',
      description: 'Fashion and apparel',
      parentId: null,
      slug: 'clothing',
      image: 'https://via.placeholder.com/150x150?text=Clothing',
      status: 'active',
      sortOrder: 2,
      metaTitle: 'Clothing & Fashion',
      metaDescription: 'Trendy clothing and fashion accessories',
      seoKeywords: 'clothing, fashion, apparel',
      customFields: { seasonal: true, return_policy: '30 days' },
      productCount: 78,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      children: [
        {
          id: 'cat_5',
          name: 'Men\'s Clothing',
          description: 'Men\'s fashion and apparel',
          parentId: 'cat_4',
          slug: 'mens-clothing',
          image: 'https://via.placeholder.com/150x150?text=Mens',
          status: 'active',
          sortOrder: 1,
          metaTitle: 'Men\'s Clothing',
          metaDescription: 'Stylish men\'s clothing and accessories',
          seoKeywords: 'mens clothing, fashion, style',
          customFields: { size_guide: 'mens-sizes.pdf' },
          productCount: 40,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 'cat_6',
          name: 'Women\'s Clothing',
          description: 'Women\'s fashion and apparel',
          parentId: 'cat_4',
          slug: 'womens-clothing',
          image: 'https://via.placeholder.com/150x150?text=Womens',
          status: 'active',
          sortOrder: 2,
          metaTitle: 'Women\'s Clothing',
          metaDescription: 'Elegant women\'s clothing and accessories',
          seoKeywords: 'womens clothing, fashion, style',
          customFields: { size_guide: 'womens-sizes.pdf' },
          productCount: 38,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      ]
    }
  ];

  const sampleProducts: Product[] = [
    {
      id: 'prod_1',
      name: 'iPhone 15 Pro',
      slug: 'iphone-15-pro',
      description: 'Latest iPhone with advanced features and powerful performance',
      shortDescription: 'Premium smartphone with Pro camera system',
      sku: 'IPHONE-15-PRO',
      barcode: '123456789012',
      price: 999.99,
      comparePrice: 1099.99,
      costPrice: 750.00,
      stock: 50,
      minStock: 10,
      maxStock: 1000,
      trackStock: true,
      allowBackorder: false,
      weight: 0.221,
      length: 15.9,
      width: 7.6,
      height: 0.83,
      categories: ['cat_2'],
      categoryNames: ['Smartphones'],
      tags: ['smartphone', 'apple', 'premium', 'new'],
      images: [
        'https://via.placeholder.com/400x400?text=iPhone+15+Pro',
        'https://via.placeholder.com/400x400?text=iPhone+15+Pro+Back'
      ],
      status: 'active',
      visibility: 'public',
      featured: true,
      seoTitle: 'iPhone 15 Pro - Premium Smartphone',
      seoDescription: 'Get the latest iPhone 15 Pro with advanced camera system and powerful performance',
      seoKeywords: 'iphone, smartphone, apple, mobile phone',
      options: [
        {
          id: 'color',
          name: 'Color',
          type: 'select',
          required: true,
          values: ['Natural Titanium', 'Blue Titanium', 'White Titanium', 'Black Titanium'],
          defaultValue: 'Natural Titanium'
        },
        {
          id: 'storage',
          name: 'Storage',
          type: 'select',
          required: true,
          values: ['128GB', '256GB', '512GB', '1TB'],
          defaultValue: '128GB'
        }
      ],
      variants: [
        {
          id: 'var_1',
          sku: 'IPHONE-15-PRO-NT-128',
          price: 999.99,
          stock: 15,
          options: { color: 'Natural Titanium', storage: '128GB' },
          weight: 0.221
        },
        {
          id: 'var_2',
          sku: 'IPHONE-15-PRO-BT-256',
          price: 1099.99,
          stock: 12,
          options: { color: 'Blue Titanium', storage: '256GB' },
          weight: 0.221
        }
      ],
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'prod_2',
      name: 'MacBook Pro 16"',
      slug: 'macbook-pro-16',
      description: 'Professional laptop with M3 Pro chip for demanding workflows',
      shortDescription: 'High-performance laptop for professionals',
      sku: 'MACBOOK-PRO-16',
      barcode: '123456789013',
      price: 2499.99,
      comparePrice: 2699.99,
      costPrice: 1800.00,
      stock: 25,
      minStock: 5,
      maxStock: 100,
      trackStock: true,
      allowBackorder: true,
      weight: 2.15,
      length: 35.57,
      width: 24.81,
      height: 1.68,
      categories: ['cat_3'],
      categoryNames: ['Laptops'],
      tags: ['laptop', 'apple', 'professional', 'macbook'],
      images: [
        'https://via.placeholder.com/400x400?text=MacBook+Pro+16',
        'https://via.placeholder.com/400x400?text=MacBook+Pro+Side'
      ],
      status: 'active',
      visibility: 'public',
      featured: true,
      seoTitle: 'MacBook Pro 16" - Professional Laptop',
      seoDescription: 'Powerful MacBook Pro 16" with M3 Pro chip for professional workflows',
      seoKeywords: 'macbook, laptop, apple, professional, m3',
      options: [
        {
          id: 'chip',
          name: 'Chip',
          type: 'select',
          required: true,
          values: ['M3 Pro', 'M3 Max'],
          defaultValue: 'M3 Pro'
        },
        {
          id: 'memory',
          name: 'Memory',
          type: 'select',
          required: true,
          values: ['18GB', '36GB', '64GB', '128GB'],
          defaultValue: '18GB'
        },
        {
          id: 'storage',
          name: 'Storage',
          type: 'select',
          required: true,
          values: ['512GB SSD', '1TB SSD', '2TB SSD', '4TB SSD', '8TB SSD'],
          defaultValue: '512GB SSD'
        }
      ],
      variants: [],
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ];

  // Load products from service
  const loadProducts = async () => {
    try {
      setLoading(true);
      const response = await productService.getProducts(filters);
      setProducts(response.products);
      setTotalProducts(response.total);
      setTotalPages(response.totalPages);
    } catch (error) {
      toast.error('Failed to load products');
      console.error('Error loading products:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setCategories(sampleCategories);
    loadProducts();
  }, [filters]);

  useEffect(() => {
    loadProducts();
  }, []);

  const handleSaveCategory = (categoryData: Omit<Category, 'id'>) => {
    if (editingCategory) {
      // Update existing category
      setCategories(prev => prev.map(cat => 
        cat.id === editingCategory.id 
          ? { ...categoryData, id: editingCategory.id, createdAt: cat.createdAt, updatedAt: new Date().toISOString() }
          : cat
      ));
    } else {
      // Add new category
      const newCategory: Category = {
        ...categoryData,
        id: `cat_${Date.now()}`,
        productCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      setCategories(prev => [...prev, newCategory]);
    }
    
    setShowCategoryForm(false);
    setEditingCategory(null);
  };

  const handleSaveProduct = async (productData: ProductFormData) => {
    try {
      setLoading(true);
      console.log('handleSaveProduct called with:', productData);

      if (editingProduct) {
        // Update existing product
        console.log('Updating product:', editingProduct.id);
        const updatedProduct = await productService.updateProduct(editingProduct.id, productData);
        console.log('Product updated successfully:', updatedProduct);
        toast.success('Product updated successfully');
      } else {
        // Add new product
        console.log('Creating new product');
        const newProduct = await productService.createProduct(productData);
        console.log('Product created successfully:', newProduct);
        toast.success('Product created successfully');
      }

      // Reload products
      console.log('Reloading products...');
      await loadProducts();

      setShowProductForm(false);
      setEditingProduct(null);
    } catch (error) {
      toast.error(editingProduct ? 'Failed to update product' : 'Failed to create product');
      console.error('Error saving product:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setShowCategoryForm(true);
  };

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setShowProductForm(true);
  };

  const handleDeleteProduct = async (productId: string) => {
    console.log('handleDeleteProduct called with ID:', productId);

    if (confirm('Are you sure you want to delete this product?')) {
      try {
        setLoading(true);
        console.log('Deleting product...');
        await productService.deleteProduct(productId);
        console.log('Product deleted successfully');
        toast.success('Product deleted successfully');
        await loadProducts();
      } catch (error) {
        toast.error('Failed to delete product');
        console.error('Error deleting product:', error);
      } finally {
        setLoading(false);
      }
    } else {
      console.log('Delete cancelled by user');
    }
  };

  const getCategoryName = (categoryId: string): string => {
    const findCategory = (cats: Category[]): string => {
      for (const cat of cats) {
        if (cat.id === categoryId) return cat.name;
        if (cat.children) {
          const found = findCategory(cat.children);
          if (found) return found;
        }
      }
      return '';
    };
    return findCategory(categories);
  };

  const flattenCategories = (cats: Category[]): Category[] => {
    const result: Category[] = [];
    const flatten = (categories: Category[]) => {
      for (const cat of categories) {
        result.push(cat);
        if (cat.children) {
          flatten(cat.children);
        }
      }
    };
    flatten(cats);
    return result;
  };

  return (
    <div className="container-fluid">
      {/* Page Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-sm-flex align-items-center justify-content-between">
            <div>
              <h4 className="mb-1 fw-bold">Product Catalog</h4>
              <p className="text-muted mb-0">Manage your products and categories</p>
            </div>
            <div className="d-flex gap-2">
              <button 
                className="btn btn-outline-primary"
                onClick={() => setShowCategoryForm(true)}
              >
                ➕ Add Category
              </button>
              <button
                className="btn btn-primary"
                onClick={(e) => {
                  console.log('Add Product button clicked', e);
                  console.log('Current showProductForm state:', showProductForm);
                  setShowProductForm(true);
                  console.log('setShowProductForm(true) called');
                }}
              >
                ➕ Add Product
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* View Toggle */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="btn-group" role="group">
            <button
              type="button"
              className={`btn ${activeView === 'products' ? 'btn-primary' : 'btn-outline-primary'}`}
              onClick={() => setActiveView('products')}
            >
              📦 Products ({products.length})
            </button>
            <button
              type="button"
              className={`btn ${activeView === 'categories' ? 'btn-primary' : 'btn-outline-primary'}`}
              onClick={() => setActiveView('categories')}
            >
              📁 Categories ({categories.length})
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="row">
        <div className="col-12">
          {activeView === 'products' ? (
            <ProductTable
              products={products.map(product => ({
                ...product,
                categoryNames: product.categories.map(catId => getCategoryName(catId))
              }))}
              onEdit={handleEditProduct}
              onDelete={handleDeleteProduct}
            />
          ) : (
            <CategoryManager
              onCategorySelect={handleEditCategory}
            />
          )}
        </div>
      </div>

      {/* Category Form Modal */}
      <CategoryForm
        category={editingCategory}
        parentCategories={flattenCategories(categories)}
        onSave={handleSaveCategory}
        onCancel={() => {
          setShowCategoryForm(false);
          setEditingCategory(null);
        }}
        isOpen={showCategoryForm}
      />

      {/* Product Form Modal */}
      <AdvancedProductForm
        product={editingProduct}
        categories={flattenCategories(categories)}
        onSave={handleSaveProduct}
        onCancel={() => {
          setShowProductForm(false);
          setEditingProduct(null);
        }}
        isOpen={showProductForm}
      />
    </div>
  );
};

export default ProductCatalog;
