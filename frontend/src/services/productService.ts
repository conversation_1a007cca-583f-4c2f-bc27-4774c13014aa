import { Product, ProductFormData, ProductVariant, ProductOption } from '../types/product';

// Mock data for development - replace with actual API calls
let mockProducts: Product[] = [
  {
    id: '1',
    name: 'Premium Wireless Headphones',
    slug: 'premium-wireless-headphones',
    description: 'High-quality wireless headphones with noise cancellation',
    shortDescription: 'Premium wireless headphones',
    sku: 'PWH-001',
    barcode: '1234567890123',
    price: 299.99,
    comparePrice: 399.99,
    costPrice: 150.00,
    stock: 50,
    minStock: 10,
    maxStock: 200,
    trackStock: true,
    allowBackorder: false,
    weight: 0.5,
    length: 20,
    width: 15,
    height: 8,
    categories: ['electronics', 'audio'],
    categoryNames: ['Electronics', 'Audio'],
    tags: ['wireless', 'premium', 'noise-cancellation'],
    images: [
      'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400',
      'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=400'
    ],
    featured: true,
    status: 'active',
    visibility: 'public',
    seoTitle: 'Premium Wireless Headphones - Best Audio Quality',
    seoDescription: 'Experience premium audio quality with our wireless headphones featuring noise cancellation technology.',
    seoKeywords: 'wireless headphones, premium audio, noise cancellation',
    variants: [
      {
        id: 'v1',
        name: 'Black - Standard',
        sku: 'PWH-001-BLK',
        price: 299.99,
        stock: 25,
        options: { color: 'Black', size: 'Standard' },
        images: ['https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400']
      },
      {
        id: 'v2',
        name: 'White - Standard',
        sku: 'PWH-001-WHT',
        price: 299.99,
        stock: 25,
        options: { color: 'White', size: 'Standard' },
        images: ['https://images.unsplash.com/photo-1484704849700-f032a568e944?w=400']
      }
    ],
    options: [
      {
        id: 'color',
        name: 'Color',
        type: 'select',
        required: true,
        values: ['Black', 'White', 'Silver'],
        defaultValue: 'Black'
      },
      {
        id: 'warranty',
        name: 'Extended Warranty',
        type: 'boolean',
        required: false,
        values: [],
        defaultValue: false
      }
    ],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z'
  },
  {
    id: '2',
    name: 'Smart Fitness Watch',
    slug: 'smart-fitness-watch',
    description: 'Advanced fitness tracking with heart rate monitoring and GPS',
    shortDescription: 'Smart fitness watch with GPS',
    sku: 'SFW-002',
    barcode: '1234567890124',
    price: 199.99,
    comparePrice: 249.99,
    costPrice: 100.00,
    stock: 75,
    minStock: 15,
    maxStock: 150,
    trackStock: true,
    allowBackorder: true,
    weight: 0.2,
    length: 5,
    width: 4,
    height: 1.5,
    categories: ['electronics', 'fitness'],
    categoryNames: ['Electronics', 'Fitness'],
    tags: ['smartwatch', 'fitness', 'gps', 'health'],
    images: [
      'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400',
      'https://images.unsplash.com/photo-**********-1dfe5d97d256?w=400'
    ],
    featured: false,
    status: 'active',
    visibility: 'public',
    seoTitle: 'Smart Fitness Watch - Track Your Health',
    seoDescription: 'Monitor your fitness goals with our advanced smartwatch featuring GPS and heart rate tracking.',
    seoKeywords: 'smartwatch, fitness tracker, GPS, heart rate monitor',
    variants: [
      {
        id: 'v3',
        name: 'Black - 42mm',
        sku: 'SFW-002-BLK-42',
        price: 199.99,
        stock: 40,
        options: { color: 'Black', size: '42mm' },
        images: ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400']
      },
      {
        id: 'v4',
        name: 'Silver - 46mm',
        sku: 'SFW-002-SLV-46',
        price: 219.99,
        stock: 35,
        options: { color: 'Silver', size: '46mm' },
        images: ['https://images.unsplash.com/photo-**********-1dfe5d97d256?w=400']
      }
    ],
    options: [
      {
        id: 'size',
        name: 'Watch Size',
        type: 'select',
        required: true,
        values: ['42mm', '46mm'],
        defaultValue: '42mm'
      },
      {
        id: 'band',
        name: 'Band Material',
        type: 'select',
        required: false,
        values: ['Sport', 'Leather', 'Metal'],
        defaultValue: 'Sport'
      }
    ],
    createdAt: '2024-01-10T08:00:00Z',
    updatedAt: '2024-01-18T16:45:00Z'
  }
];

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const productService = {
  // Get all products with optional filters
  async getProducts(filters?: {
    search?: string;
    category?: string;
    status?: string;
    featured?: boolean;
    page?: number;
    limit?: number;
  }): Promise<{ products: Product[]; total: number; page: number; totalPages: number }> {
    await delay(500);
    
    let filteredProducts = [...mockProducts];
    
    // Apply filters
    if (filters?.search) {
      const search = filters.search.toLowerCase();
      filteredProducts = filteredProducts.filter(product =>
        product.name.toLowerCase().includes(search) ||
        product.sku.toLowerCase().includes(search) ||
        product.description.toLowerCase().includes(search)
      );
    }
    
    if (filters?.category) {
      filteredProducts = filteredProducts.filter(product =>
        product.categories.includes(filters.category!)
      );
    }
    
    if (filters?.status) {
      filteredProducts = filteredProducts.filter(product =>
        product.status === filters.status
      );
    }
    
    if (filters?.featured !== undefined) {
      filteredProducts = filteredProducts.filter(product =>
        product.featured === filters.featured
      );
    }
    
    // Pagination
    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);
    
    return {
      products: paginatedProducts,
      total: filteredProducts.length,
      page,
      totalPages: Math.ceil(filteredProducts.length / limit)
    };
  },

  // Get single product by ID
  async getProduct(id: string): Promise<Product | null> {
    await delay(300);
    return mockProducts.find(product => product.id === id) || null;
  },

  // Create new product
  async createProduct(productData: ProductFormData): Promise<Product> {
    await delay(800);

    const newProduct: Product = {
      id: Date.now().toString(),
      name: productData.name,
      slug: productData.name.toLowerCase().replace(/\s+/g, '-'),
      description: productData.description,
      shortDescription: productData.shortDescription || '',
      sku: productData.sku,
      barcode: productData.barcode,
      price: productData.price,
      comparePrice: productData.comparePrice,
      costPrice: productData.costPrice,
      stock: productData.stock,
      minStock: productData.minStock || 0,
      maxStock: productData.maxStock || 1000,
      trackStock: productData.trackStock,
      allowBackorder: productData.allowBackorder,
      weight: productData.weight,
      length: productData.length,
      width: productData.width,
      height: productData.height,
      categories: productData.categories,
      categoryNames: productData.categoryNames,
      tags: productData.tags,
      images: productData.images,
      featured: productData.featured,
      status: productData.status,
      visibility: productData.visibility,
      seoTitle: productData.seoTitle,
      seoDescription: productData.seoDescription,
      seoKeywords: productData.seoKeywords,
      variants: productData.variants || [],
      options: productData.options || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    mockProducts.unshift(newProduct);
    return newProduct;
  },

  // Update existing product
  async updateProduct(id: string, productData: Partial<ProductFormData>): Promise<Product> {
    await delay(800);

    const index = mockProducts.findIndex(product => product.id === id);
    if (index === -1) {
      throw new Error('Product not found');
    }

    const currentProduct = mockProducts[index];
    const updatedProduct: Product = {
      ...currentProduct,
      name: productData.name ?? currentProduct.name,
      description: productData.description ?? currentProduct.description,
      shortDescription: productData.shortDescription ?? currentProduct.shortDescription,
      sku: productData.sku ?? currentProduct.sku,
      barcode: productData.barcode ?? currentProduct.barcode,
      price: productData.price ?? currentProduct.price,
      comparePrice: productData.comparePrice ?? currentProduct.comparePrice,
      costPrice: productData.costPrice ?? currentProduct.costPrice,
      stock: productData.stock ?? currentProduct.stock,
      minStock: productData.minStock ?? currentProduct.minStock,
      maxStock: productData.maxStock ?? currentProduct.maxStock,
      trackStock: productData.trackStock ?? currentProduct.trackStock,
      allowBackorder: productData.allowBackorder ?? currentProduct.allowBackorder,
      weight: productData.weight ?? currentProduct.weight,
      length: productData.length ?? currentProduct.length,
      width: productData.width ?? currentProduct.width,
      height: productData.height ?? currentProduct.height,
      categories: productData.categories ?? currentProduct.categories,
      categoryNames: productData.categoryNames ?? currentProduct.categoryNames,
      tags: productData.tags ?? currentProduct.tags,
      images: productData.images ?? currentProduct.images,
      featured: productData.featured ?? currentProduct.featured,
      status: productData.status ?? currentProduct.status,
      visibility: productData.visibility ?? currentProduct.visibility,
      seoTitle: productData.seoTitle ?? currentProduct.seoTitle,
      seoDescription: productData.seoDescription ?? currentProduct.seoDescription,
      seoKeywords: productData.seoKeywords ?? currentProduct.seoKeywords,
      variants: productData.variants ?? currentProduct.variants,
      options: productData.options ?? currentProduct.options,
      updatedAt: new Date().toISOString()
    };

    if (productData.name) {
      updatedProduct.slug = productData.name.toLowerCase().replace(/\s+/g, '-');
    }

    mockProducts[index] = updatedProduct;
    return updatedProduct;
  },

  // Delete product
  async deleteProduct(id: string): Promise<void> {
    await delay(500);
    
    const index = mockProducts.findIndex(product => product.id === id);
    if (index === -1) {
      throw new Error('Product not found');
    }
    
    mockProducts.splice(index, 1);
  },

  // Bulk operations
  async bulkUpdateProducts(ids: string[], updates: Partial<ProductFormData>): Promise<Product[]> {
    await delay(1000);
    
    const updatedProducts: Product[] = [];
    
    for (const id of ids) {
      const index = mockProducts.findIndex(product => product.id === id);
      if (index !== -1) {
        const updatedProduct = {
          ...mockProducts[index],
          ...updates,
          updatedAt: new Date().toISOString()
        };
        mockProducts[index] = updatedProduct;
        updatedProducts.push(updatedProduct);
      }
    }
    
    return updatedProducts;
  },

  async bulkDeleteProducts(ids: string[]): Promise<void> {
    await delay(1000);
    
    for (const id of ids) {
      const index = mockProducts.findIndex(product => product.id === id);
      if (index !== -1) {
        mockProducts.splice(index, 1);
      }
    }
  },

  // Product variants
  async getProductVariants(productId: string): Promise<ProductVariant[]> {
    await delay(300);
    const product = mockProducts.find(p => p.id === productId);
    return product?.variants || [];
  },

  async createProductVariant(productId: string, variant: Omit<ProductVariant, 'id'>): Promise<ProductVariant> {
    await delay(500);
    
    const product = mockProducts.find(p => p.id === productId);
    if (!product) {
      throw new Error('Product not found');
    }
    
    const newVariant: ProductVariant = {
      ...variant,
      id: `v${Date.now()}`
    };
    
    if (!product.variants) {
      product.variants = [];
    }
    
    product.variants.push(newVariant);
    product.updatedAt = new Date().toISOString();
    
    return newVariant;
  },

  async updateProductVariant(productId: string, variantId: string, updates: Partial<ProductVariant>): Promise<ProductVariant> {
    await delay(500);
    
    const product = mockProducts.find(p => p.id === productId);
    if (!product || !product.variants) {
      throw new Error('Product or variant not found');
    }
    
    const variantIndex = product.variants.findIndex(v => v.id === variantId);
    if (variantIndex === -1) {
      throw new Error('Variant not found');
    }
    
    const updatedVariant = {
      ...product.variants[variantIndex],
      ...updates
    };
    
    product.variants[variantIndex] = updatedVariant;
    product.updatedAt = new Date().toISOString();
    
    return updatedVariant;
  },

  async deleteProductVariant(productId: string, variantId: string): Promise<void> {
    await delay(500);
    
    const product = mockProducts.find(p => p.id === productId);
    if (!product || !product.variants) {
      throw new Error('Product or variant not found');
    }
    
    const variantIndex = product.variants.findIndex(v => v.id === variantId);
    if (variantIndex === -1) {
      throw new Error('Variant not found');
    }
    
    product.variants.splice(variantIndex, 1);
    product.updatedAt = new Date().toISOString();
  },

  // Stock management
  async updateStock(productId: string, variantId: string | null, quantity: number): Promise<void> {
    await delay(300);
    
    const product = mockProducts.find(p => p.id === productId);
    if (!product) {
      throw new Error('Product not found');
    }
    
    if (variantId) {
      const variant = product.variants?.find(v => v.id === variantId);
      if (!variant) {
        throw new Error('Variant not found');
      }
      variant.stock = quantity;
    } else {
      product.stock = quantity;
    }
    
    product.updatedAt = new Date().toISOString();
  },

  // Search and filters
  async searchProducts(query: string): Promise<Product[]> {
    await delay(300);
    
    const searchTerm = query.toLowerCase();
    return mockProducts.filter(product =>
      product.name.toLowerCase().includes(searchTerm) ||
      product.sku.toLowerCase().includes(searchTerm) ||
      product.description.toLowerCase().includes(searchTerm) ||
      product.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  },

  // Analytics
  async getProductAnalytics(productId: string): Promise<{
    views: number;
    sales: number;
    revenue: number;
    conversionRate: number;
    stockTurnover: number;
  }> {
    await delay(500);
    
    // Mock analytics data
    return {
      views: Math.floor(Math.random() * 1000) + 100,
      sales: Math.floor(Math.random() * 50) + 10,
      revenue: Math.floor(Math.random() * 10000) + 1000,
      conversionRate: Math.random() * 10 + 2,
      stockTurnover: Math.random() * 5 + 1
    };
  }
};

export default productService;
