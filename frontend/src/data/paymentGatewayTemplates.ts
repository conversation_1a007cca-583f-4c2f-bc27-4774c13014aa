import { PaymentGatewayTemplate, PaymentConfigField } from '../types/payment';

// Common configuration fields
const commonFields: PaymentConfigField[] = [
  {
    key: 'environment',
    label: 'Environment',
    type: 'select',
    required: true,
    description: 'Select the environment for this gateway',
    options: [
      { value: 'sandbox', label: 'Sandbox (Testing)' },
      { value: 'production', label: 'Production (Live)' }
    ]
  },
  {
    key: 'currency',
    label: 'Default Currency',
    type: 'select',
    required: true,
    description: 'Default currency for transactions',
    options: [
      { value: 'USD', label: 'US Dollar (USD)' },
      { value: 'EUR', label: 'Euro (EUR)' },
      { value: 'GBP', label: 'British Pound (GBP)' },
      { value: 'INR', label: 'Indian Rupee (INR)' },
      { value: 'CAD', label: 'Canadian Dollar (CAD)' },
      { value: 'AUD', label: 'Australian Dollar (AUD)' }
    ]
  },
  {
    key: 'minimumAmount',
    label: 'Minimum Amount',
    type: 'number',
    required: false,
    description: 'Minimum transaction amount (in cents/paise)',
    validation: { min: 0 }
  },
  {
    key: 'maximumAmount',
    label: 'Maximum Amount',
    type: 'number',
    required: false,
    description: 'Maximum transaction amount (in cents/paise)',
    validation: { min: 0 }
  }
];

// Payment Gateway Templates
export const paymentGatewayTemplates: PaymentGatewayTemplate[] = [
  {
    type: 'stripe',
    name: 'Stripe',
    description: 'Accept payments online with Stripe. Supports credit cards, digital wallets, and more.',
    icon: '💳',
    documentation: 'https://stripe.com/docs',
    requiredFields: [
      {
        key: 'publishableKey',
        label: 'Publishable Key',
        type: 'text',
        required: true,
        description: 'Your Stripe publishable key (starts with pk_)',
        placeholder: 'pk_test_...',
        validation: { pattern: '^pk_' }
      },
      {
        key: 'secretKey',
        label: 'Secret Key',
        type: 'password',
        required: true,
        description: 'Your Stripe secret key (starts with sk_)',
        placeholder: 'sk_test_...',
        validation: { pattern: '^sk_' }
      },
      ...commonFields
    ],
    optionalFields: [
      {
        key: 'webhookSecret',
        label: 'Webhook Endpoint Secret',
        type: 'password',
        required: false,
        description: 'Webhook endpoint secret for verifying webhook signatures',
        placeholder: 'whsec_...'
      },
      {
        key: 'processingFee',
        label: 'Processing Fee',
        type: 'number',
        required: false,
        description: 'Additional processing fee (percentage)',
        validation: { min: 0, max: 100 }
      }
    ],
    supportedFeatures: [
      'Credit Cards',
      'Digital Wallets',
      'Bank Transfers',
      'Recurring Payments',
      'Refunds',
      'Webhooks',
      'Multi-currency'
    ],
    defaultCurrencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
    testCredentials: {
      publishableKey: 'pk_test_TYooMQauvdEDq54NiTphI7jx',
      secretKey: 'sk_test_4eC39HqLyjWDarjtT1zdp7dc'
    }
  },
  {
    type: 'paypal',
    name: 'PayPal',
    description: 'Accept payments through PayPal. Trusted by millions worldwide.',
    icon: '🅿️',
    documentation: 'https://developer.paypal.com/docs',
    requiredFields: [
      {
        key: 'clientId',
        label: 'Client ID',
        type: 'text',
        required: true,
        description: 'Your PayPal application client ID',
        placeholder: 'AeA1QIZXiflr8_...'
      },
      {
        key: 'clientSecret',
        label: 'Client Secret',
        type: 'password',
        required: true,
        description: 'Your PayPal application client secret',
        placeholder: 'EGnHDxD_qRPdaLdHgGlliB...'
      },
      ...commonFields
    ],
    optionalFields: [
      {
        key: 'webhookId',
        label: 'Webhook ID',
        type: 'text',
        required: false,
        description: 'PayPal webhook ID for event notifications'
      }
    ],
    supportedFeatures: [
      'PayPal Payments',
      'Credit Cards',
      'Express Checkout',
      'Recurring Payments',
      'Refunds',
      'Webhooks'
    ],
    defaultCurrencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
    testCredentials: {
      clientId: 'AeA1QIZXiflr8_5159LI2BRVQm8-6Al_5YOg2EWlni9qGz3D8XjHyqJOhpKN',
      clientSecret: 'EGnHDxD_qRPdaLdHgGlliB4_3DgF_VWuRrmWwHcguj-DQTkfz6uFmRRQmHX'
    }
  },
  {
    type: 'razorpay',
    name: 'Razorpay',
    description: 'India\'s leading payment gateway. Accept payments in INR and international currencies.',
    icon: '💰',
    documentation: 'https://razorpay.com/docs',
    requiredFields: [
      {
        key: 'keyId',
        label: 'Key ID',
        type: 'text',
        required: true,
        description: 'Your Razorpay Key ID',
        placeholder: 'rzp_test_...'
      },
      {
        key: 'keySecret',
        label: 'Key Secret',
        type: 'password',
        required: true,
        description: 'Your Razorpay Key Secret',
        placeholder: 'Your secret key'
      },
      ...commonFields
    ],
    optionalFields: [
      {
        key: 'webhookSecret',
        label: 'Webhook Secret',
        type: 'password',
        required: false,
        description: 'Webhook secret for verifying webhook signatures'
      }
    ],
    supportedFeatures: [
      'Credit Cards',
      'Debit Cards',
      'Net Banking',
      'UPI',
      'Wallets',
      'EMI',
      'Recurring Payments',
      'Refunds',
      'Webhooks'
    ],
    defaultCurrencies: ['INR', 'USD', 'EUR'],
    testCredentials: {
      keyId: 'rzp_test_1DP5mmOlF5G5ag',
      keySecret: 'thisissecretkey'
    }
  },
  {
    type: 'square',
    name: 'Square',
    description: 'Accept payments online and in-person with Square.',
    icon: '⬜',
    documentation: 'https://developer.squareup.com/docs',
    requiredFields: [
      {
        key: 'applicationId',
        label: 'Application ID',
        type: 'text',
        required: true,
        description: 'Your Square application ID',
        placeholder: 'sq0idp-...'
      },
      {
        key: 'accessToken',
        label: 'Access Token',
        type: 'password',
        required: true,
        description: 'Your Square access token',
        placeholder: 'EAAAEOuLQOXXXXXXXXXXXXXX'
      },
      ...commonFields
    ],
    optionalFields: [
      {
        key: 'webhookSignatureKey',
        label: 'Webhook Signature Key',
        type: 'password',
        required: false,
        description: 'Webhook signature key for verifying webhooks'
      }
    ],
    supportedFeatures: [
      'Credit Cards',
      'Digital Wallets',
      'Gift Cards',
      'Refunds',
      'Webhooks'
    ],
    defaultCurrencies: ['USD', 'CAD', 'GBP', 'AUD'],
    testCredentials: {
      applicationId: '*****************************',
      accessToken: 'EAAAEOuLQOXXXXXXXXXXXXXX'
    }
  },
  {
    type: 'bank_transfer',
    name: 'Bank Transfer',
    description: 'Accept payments via direct bank transfers.',
    icon: '🏦',
    documentation: '',
    requiredFields: [
      {
        key: 'bankName',
        label: 'Bank Name',
        type: 'text',
        required: true,
        description: 'Name of your bank',
        placeholder: 'Chase Bank'
      },
      {
        key: 'accountNumber',
        label: 'Account Number',
        type: 'text',
        required: true,
        description: 'Your bank account number',
        placeholder: '**********'
      },
      {
        key: 'routingNumber',
        label: 'Routing Number',
        type: 'text',
        required: true,
        description: 'Bank routing number',
        placeholder: '*********'
      },
      ...commonFields.filter(field => field.key !== 'environment')
    ],
    optionalFields: [
      {
        key: 'swiftCode',
        label: 'SWIFT Code',
        type: 'text',
        required: false,
        description: 'SWIFT code for international transfers',
        placeholder: 'CHASUS33'
      },
      {
        key: 'processingTime',
        label: 'Processing Time',
        type: 'text',
        required: false,
        description: 'Expected processing time',
        placeholder: '1-3 business days'
      }
    ],
    supportedFeatures: [
      'Bank Transfers',
      'International Transfers',
      'Manual Verification'
    ],
    defaultCurrencies: ['USD', 'EUR', 'GBP', 'CAD']
  },
  {
    type: 'cash_on_delivery',
    name: 'Cash on Delivery',
    description: 'Accept cash payments upon delivery.',
    icon: '💵',
    documentation: '',
    requiredFields: [
      ...commonFields.filter(field => field.key === 'currency')
    ],
    optionalFields: [
      {
        key: 'deliveryFee',
        label: 'Delivery Fee',
        type: 'number',
        required: false,
        description: 'Additional delivery fee',
        validation: { min: 0 }
      },
      {
        key: 'maxDistance',
        label: 'Maximum Delivery Distance (km)',
        type: 'number',
        required: false,
        description: 'Maximum delivery distance',
        validation: { min: 0 }
      }
    ],
    supportedFeatures: [
      'Cash Payments',
      'Local Delivery',
      'Manual Processing'
    ],
    defaultCurrencies: ['USD', 'EUR', 'GBP', 'INR', 'CAD']
  }
];

// Helper function to get template by type
export const getPaymentGatewayTemplate = (type: string): PaymentGatewayTemplate | undefined => {
  return paymentGatewayTemplates.find(template => template.type === type);
};

// Helper function to get all supported gateway types
export const getSupportedGatewayTypes = () => {
  return paymentGatewayTemplates.map(template => ({
    value: template.type,
    label: template.name,
    icon: template.icon,
    description: template.description
  }));
};
