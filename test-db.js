// Test database connection
const { Client } = require('pg');

async function testDatabase() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'inventory_db',
    user: 'postgres',
    password: '', // Using trust authentication
  });

  try {
    console.log('🔌 Testing database connection...');
    await client.connect();
    console.log('✅ Connected to PostgreSQL database');
    
    const result = await client.query('SELECT version()');
    console.log('📊 Database version:', result.rows[0].version);
    
    // Test creating a simple table
    await client.query(`
      CREATE TABLE IF NOT EXISTS test_table (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100),
        created_at TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✅ Test table created successfully');
    
    // Insert test data
    await client.query("INSERT INTO test_table (name) VALUES ('Test Connection')");
    console.log('✅ Test data inserted');
    
    // Query test data
    const testResult = await client.query('SELECT * FROM test_table');
    console.log('✅ Test data retrieved:', testResult.rows.length, 'rows');
    
    // Clean up
    await client.query('DROP TABLE test_table');
    console.log('✅ Test table cleaned up');
    
    console.log('🎉 Database connection test completed successfully!');
    
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    console.error('   Code:', error.code);
    console.error('   Detail:', error.detail);
  } finally {
    await client.end();
  }
}

testDatabase();
