-- Initialize the inventory database
-- This script runs when the PostgreSQL container starts for the first time

-- Ensure the user exists and has the right permissions
ALTER USER inventory_user WITH PASSWORD 'inventory_password';
ALTER USER inventory_user CREATEDB;

-- Grant all privileges on the database
GRANT ALL PRIVILEGES ON DATABASE inventory_db TO inventory_user;

-- Connect to the inventory database
\c inventory_db;

-- <PERSON> schema permissions
GRANT ALL ON SCHEMA public TO inventory_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO inventory_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO inventory_user;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO inventory_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO inventory_user;
